--------- beginning of main
--------- beginning of system
2025-05-02 23:51:55.569  2786-5279  FreecessController      system_server                        D  com.deace.deacear(10553) is important[2]
2025-05-02 23:51:57.669 22827-22827 HoneySpace.IconView     com.sec.android.app.launcher         I  start dim animation : Graffiti AR Deace
2025-05-02 23:51:57.671  2786-4351  Pageboost               system_server                        I  start alp : com.deace.deacear
2025-05-02 23:51:57.671  2786-4360  Pageboost               system_server                        I  alp for : com.deace.deacear , 1
2025-05-02 23:51:57.672  5361-20843 pageboostd              pageboostd                           E  alp start : app comdeacedeacear
2025-05-02 23:51:57.673  2786-2860  ChimeraSys...ntListener system_server                        I  appLaunchIntent package name is: com.deace.deacear
2025-05-02 23:51:57.677  5361-20843 pageboostd              pageboostd                           E  comdeacedeacear, amt 1069056 scnt 2 fcnt 0
2025-05-02 23:51:57.681  4359-4442  ShellStartingWindow     com.android.systemui                 D  preload Icon com.deace.deacear
2025-05-02 23:51:57.705  5361-20843 pageboostd              pageboostd                           E  comdeacedeacear, amt 15581184 scnt 17 fcnt 2
2025-05-02 23:51:57.705  5361-20843 pageboostd              pageboostd                           E  alp end : app comdeacedeacear data_amount 16650240
2025-05-02 23:51:57.713 22827-22827 HoneySpace...iewAdapter com.sec.android.app.launcher         I  onAppClick() id: 24825, event App(item=id:24825 label:Graffiti AR Deace componnet:com.deace.deacear/.MainActivity#UserHandle{0} badgeCount:0  drag:false iconState:NONE, pageId=4, x=3, y=1)
2025-05-02 23:51:57.721  2786-2860  ChimeraSys...ntListener system_server                        I  appLaunchIntent package name is: com.deace.deacear
2025-05-02 23:51:57.721  2786-8004  Pageboost               system_server                        I  onAppLaunch : com.deace.deacear
2025-05-02 23:51:57.725  2786-8004  WindowManager           system_server                        V  Collecting in transition 15878: ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity, caller=com.android.server.wm.Transition.collect:753 com.android.server.wm.TransitionController.collect:975 com.android.server.wm.ActivityStarter.startActivityUnchecked:2407 com.android.server.wm.ActivityStarter.executeRequest:1959 com.android.server.wm.ActivityStarter.execute:1075 
2025-05-02 23:51:57.725  2786-8004  ActivityTaskManager     system_server                        D  TaskLaunchParamsModifier:task=null activity=ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t-1} display-from-option=0 display-id=0 task-display-area-windowing-mode=1 suggested-display-area=DefaultTaskDisplayArea_d0@179017892
2025-05-02 23:51:57.725  2786-8004  ActivityTaskManager     system_server                        D  TaskLaunchParamsModifier:task=null activity=ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t-1} display-from-option=0 display-id=0 task-display-area-windowing-mode=1 suggested-display-area=DefaultTaskDisplayArea_d0@179017892 inherit-from-source=fullscreen non-freeform-task-display-area display-area=DefaultTaskDisplayArea_d0@179017892 default-portrait freeform-size-mismatch=Rect(81, 549 - 999, 1707)
2025-05-02 23:51:57.727  2786-8004  ActivityTaskManager     system_server                        D  TaskLaunchParamsModifier:task=Task{afd1290 #32676 type=standard A=10553:com.deace.deacear} activity=ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t-1} display-from-option=0 display-id=0 task-display-area-windowing-mode=1 suggested-display-area=DefaultTaskDisplayArea_d0@179017892 inherit-from-source=fullscreen non-freeform-task-display-area display-area=DefaultTaskDisplayArea_d0@179017892 default-portrait freeform-size-mismatch=Rect(81, 549 - 999, 1707)
2025-05-02 23:51:57.727  2786-8004  WindowManager           system_server                        V  Collecting in transition 15878: Task{afd1290 #32676 type=standard A=10553:com.deace.deacear}, caller=com.android.server.wm.Transition.collect:753 com.android.server.wm.Transition.collectExistenceChange:936 com.android.server.wm.TransitionController.collectExistenceChange:1025 com.android.server.wm.ActivityStarter.setNewTask:4833 com.android.server.wm.ActivityStarter.startActivityInner:3041 
2025-05-02 23:51:57.727  1712-3722  SurfaceFlinger          surfaceflinger                       I  id=283203 createSurf, flag=84004, ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}#283203
2025-05-02 23:51:57.729  2786-8004  ActivityTaskManager     system_server                        D  Add starting ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}: typeParam=143 startingData=com.android.server.wm.SplashScreenStartingData@1f3538e
2025-05-02 23:51:57.729  2786-8004  WindowManager           system_server                        V  Added starting ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}: startingWindow=null startingView=com.android.server.wm.StartingSurfaceController$StartingSurface@5e407af
2025-05-02 23:51:57.731  2786-8004  WindowManager           system_server                        V  Collecting in transition 15878: ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}, caller=com.android.server.wm.Transition.collect:753 com.android.server.wm.Transition.collectExistenceChange:936 com.android.server.wm.TransitionController.collectExistenceChange:1025 com.android.server.wm.ActivityStarter.handleStartResult:2632 com.android.server.wm.ActivityStarter.startActivityUnchecked:2415 
2025-05-02 23:51:57.731  2786-8004  ActivityTaskManager     system_server                        I  START u0 {act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.deace.deacear/.MainActivity bnds=[638,497][838,821]} with LAUNCH_MULTIPLE from uid 10142 (BAL_ALLOW_ALLOWLISTED_COMPONENT) result code=0
2025-05-02 23:51:57.731  4359-4406  WindowManagerShell      com.android.systemui                 V  Transition requested: android.os.BinderProxy@a2152b4 TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=32676 displayId=0 isRunning=true baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.deace.deacear/.MainActivity } baseActivity=ComponentInfo{com.deace.deacear/com.deace.deacear.MainActivity} topActivity=ComponentInfo{com.deace.deacear/com.deace.deacear.MainActivity} origActivity=null realActivity=ComponentInfo{com.deace.deacear/com.deace.deacear.MainActivity} numActivities=1 lastActiveTime=********** supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 maxWidth=-1 maxHeight=-1 defaultMinSize=220 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@52f4a52} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 75 - 0, 0) topActivityInfo=ActivityInfo{b33d123 com.deace.deacear.MainActivity} launchCookies=[android.os.BinderProxy@2dae620] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=false isVisible=false isVisibleRequested=false isSleeping=false topActivityInSizeCompat=false topActivityEligibleForLetterboxEducation= false topActivityLetterboxed= false isFromDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 locusId=null displayAreaFeatureId=1 cameraCompatControlState=hidden originallySupportedMultiWindow=true hasWallpaper=false topActivityInFixedAspectRatio=false rootAffinity=10553:com.deace.deacear topActivityInDisplayCompat=false topActivityInBoundsCompat=false topActivityBounds=null singleTapFromLetterbox=false isTopTaskInStage=false topActivityUiMode=33 CoverLauncherWidgetTask=false CoverScreenTask=false isAllowedSeamlessRotation=false isTopTransparentActivity=false hasPopOver=false}, remoteTransition = RemoteTransition { remoteTransition = android.window.IRemoteTransition$Stub$Proxy@611ebd9, appThread = android.app.IApplicationThread$Stub$Proxy@e53b99e, debugName = QuickstepLaunch }, displayChange = null }
2025-05-02 23:51:57.732 22827-23021 HoneySpace...ggingUtils com.sec.android.app.launcher         I  SA Logging screenID: 101 eventId: 1001 detail: {PackageName=com.deace.deacear, det=1} value: -1
2025-05-02 23:51:57.733 22827-5063  HoneySpace...sitoryImpl com.sec.android.app.launcher         I  sendSuggestedAppsLaunchTarget: ComponentInfo{com.deace.deacear/com.deace.deacear.MainActivity}
2025-05-02 23:51:57.734  2786-5397  WindowManager           system_server                        D  prepareSync <SYNC_STATE_WAITING_FOR_DRAW>, mPrepareSyncSeqId=0, win=Window{5c08345 u0 Splash Screen com.deace.deacear}
2025-05-02 23:51:57.734  1712-3723  SurfaceFlinger          surfaceflinger                       I  id=283204 createSurf, flag=84004, 5c08345 Splash Screen com.deace.deacear#283204
2025-05-02 23:51:57.735  2786-5397  WindowManager           system_server                        V  addWindow: ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676} startingWindow=Window{5c08345 u0 Splash Screen com.deace.deacear}
2025-05-02 23:51:57.736  4359-4441  InsetsController        com.android.systemui                 I  onStateChanged: host=Splash Screen com.deace.deacear, from=android.view.ViewRootImpl.setView:1797, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2316), mDisplayCutout=DisplayCutout{insets=Rect(0, 75 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(513, 0 - 567, 75), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2316 physicalDisplayWidth=1080 physicalDisplayHeight=2316 density={2.8125} cutoutSpec={M 0,0 H -9.466666666666667 V 26.66666666666667‬ H 9.466666666666667 V 0 H 0 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=8, center=Point(8, 8)}, RoundedCorner{position=TopRight, radius=8, center=Point(1072, 8)}, RoundedCorner{position=BottomRight, radius=8, center=Point(1072, 2308)}, RoundedCorner{position=BottomLeft, radius=8, center=Point(8, 2308)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2316), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(956, 0 - 1080, 75) rotation=0}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1440 displayHeight=3088 physicalPixelDisplaySizeRatio=1.0 rotation=0 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {d8380000 mType=statusBars mFrame=[0,0][1080,75] mVisible=true mFlags=[]}, InsetsSource: {d8380005 mType=mandatorySystemGestures mFrame=[0,0][1080,109] mVisible=true mFlags=[]}, InsetsSource: {d8380006 mType=tappableElement mFrame=[0,0][1080,75] mVisible=true mFlags=[]}, InsetsSource: {27 mType=displayCutout mFrame=[0,0][1080,75] mVisible=true mFlags=[]}, InsetsSource: {58b90001 mType=navigationBars mFrame=[0,2181][1080,2316] mVisible=true mFlags=[]}, InsetsSource: {58b90004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags=[]}, InsetsSource: {58b90005 mType=mandatorySystemGestures mFrame=[0,2181][1080,2316] mVisible=true mFlags=[]}, InsetsSource: {58b90006 mType=tappableElement mFrame=[0,2181][1080,2316] mVisible=true mFlags=[]}, InsetsSource: {58b90024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags=[]} }
2025-05-02 23:51:57.736  4359-4441  ViewRootIm...d[deacear] com.android.systemui                 I  synced displayState. AttachInfo displayState=2
2025-05-02 23:51:57.736  4359-4441  ViewRootIm...d[deacear] com.android.systemui                 I  setView = android.widget.FrameLayout@6ac6c95 TM=true
2025-05-02 23:51:57.736  2786-8039  WindowManager           system_server                        V  Collecting in transition 15878: ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}, caller=com.android.server.wm.Transition.collect:753 com.android.server.wm.TransitionController.collect:975 com.android.server.wm.ActivityRecord.setVisibility:6429 com.android.server.wm.ActivityRecord.setVisibility:6381 com.android.server.wm.ActivityRecord.makeVisibleIfNeeded:7188 
2025-05-02 23:51:57.737  2786-8039  WindowManager           system_server                        D  rotationForOrientation, orientationSource=ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}
2025-05-02 23:51:57.737  2786-8039  WindowManager           system_server                        D  rotationForOrientation, orientationSource=ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}
2025-05-02 23:51:57.737  2786-8039  WindowManager           system_server                        D  rotationForOrientation, orientationSource=ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}
2025-05-02 23:51:57.737  2786-8039  WindowManager           system_server                        V  Collecting in transition 15878: ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}, caller=com.android.server.wm.Transition.collect:753 com.android.server.wm.TransitionController.collect:975 com.android.server.wm.ActivityRecord.setVisibility:6429 com.android.server.wm.ActivityRecord.setVisibility:6381 com.android.server.wm.ActivityTaskSupervisor.realStartActivityLocked:965 
2025-05-02 23:51:57.738  2786-8039  SGM:PkgDataHelper       system_server                        D  notifyAppCreate(), pkgName: com.deace.deacear, userId: 0, sendRet: true
2025-05-02 23:51:57.738  2786-3726  SGM:FgCheckThread       system_server                        D  onLooperPrepared(), msg: MSG_APP_CREATE, pkgName: com.deace.deacear, userId: 0
2025-05-02 23:51:57.738  2786-8039  SGM:FgCheckThread       system_server                        D    sendRunningComponentFocus(), pkgName: com.deace.deacear, userId: 0
2025-05-02 23:51:57.738  2786-3726  SGM:FgCheckThread       system_server                        D  onLooperPrepared(), msg: MSG_TASK_FOCUSED, pkgName: com.deace.deacear, userId: 0
2025-05-02 23:51:57.738  2786-3726  SGM:FgCheckThread       system_server                        D    handleTaskFocused(), pkgName: com.deace.deacear, userID:0
2025-05-02 23:51:57.738  2786-3726  SGM:FgCheckThread       system_server                        D    handleResume(). pkgName: com.deace.deacear, userId: 0, isTunableApp: null
2025-05-02 23:51:57.738  2786-3726  SGM:FgCheckThread       system_server                        D  notifyFocusInOut(). of pkg: com.deace.deacear, type: 4, isTunableApp: false, userId: 0
2025-05-02 23:51:57.738  2786-8039  InputDispatcher         system_server                        D  Focused application(0): ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}
2025-05-02 23:51:57.739  2786-8039  SGM:FgCheckThread       system_server                        D    sendRunningComponentFocus(), pkgName: com.deace.deacear, userId: 0
2025-05-02 23:51:57.739  2786-3726  SGM:FgCheckThread       system_server                        D  onLooperPrepared(), msg: MSG_TASK_FOCUSED, pkgName: com.deace.deacear, userId: 0
2025-05-02 23:51:57.739  2786-3726  SGM:FgCheckThread       system_server                        D    handleTaskFocused(), pkgName: com.deace.deacear, userID:0
2025-05-02 23:51:57.739  2786-3726  SGM:FgCheckThread       system_server                        D    handleResume(). pkgName: com.deace.deacear, userId: 0, isTunableApp: null
2025-05-02 23:51:57.739  2786-3726  SGM:FgCheckThread       system_server                        D  notifyFocusInOut(). of pkg: com.deace.deacear, type: 4, isTunableApp: false, userId: 0
2025-05-02 23:51:57.739  2786-8039  ActivityTaskManager     system_server                        D  scheduleTopResumedActivityChanged, onTop=true, r=ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}, caller=com.android.server.wm.ActivityTaskSupervisor.scheduleTopResumedActivityStateIfNeeded:2904 com.android.server.wm.ActivityTaskSupervisor.updateTopResumedActivityIfNeeded:2896 com.android.server.wm.TaskFragment.setResumedActivity:580 com.android.server.wm.TaskFragment.onActivityStateChanged:904 com.android.server.wm.ActivityRecord.setState:6892 com.android.server.wm.Task.minimalResumeActivityLocked:6255 
2025-05-02 23:51:57.739  2786-8039  WindowManager           system_server                        V  Collecting in transition 15878: ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}, caller=com.android.server.wm.Transition.collect:753 com.android.server.wm.TransitionController.collect:975 com.android.server.wm.ActivityRecord.setVisibility:6429 com.android.server.wm.ActivityRecord.setVisibility:6381 com.android.server.wm.ActivityRecord.completeResumeLocked:7499 
2025-05-02 23:51:57.740  2786-8039  WindowManagerServiceExt system_server                        D  notifyOccludeChangeNotice: cn=ComponentInfo{com.deace.deacear/com.deace.deacear.MainActivity} occludesParent=true isInSplitScreenMode=false styleFloating=false caller=com.android.server.wm.ActivityRecord.completeResumeLocked:7556 com.android.server.wm.Task.minimalResumeActivityLocked:6256 com.android.server.wm.ActivityTaskSupervisor.realStartActivityLocked:1187 com.android.server.wm.ActivityTaskSupervisor.startSpecificActivity:1296 com.android.server.wm.TaskFragment.resumeTopActivity:1895 
2025-05-02 23:51:57.741  2786-8039  WindowManager           system_server                        D  SyncGroup 15878:  Unfinished container: ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676} mSyncState=2
2025-05-02 23:51:57.741  2786-8004  WindowManager           system_server                        V  Relayout Window{5c08345 u0 Splash Screen com.deace.deacear}: viewVisibility=0 req=1080x2316 ty=3 d0
2025-05-02 23:51:57.741  2786-2860  GMR                     system_server                        D  fg : 17736 com.deace.deacear
2025-05-02 23:51:57.742  1712-3723  SurfaceFlinger          surfaceflinger                       I  id=283205 createSurf, flag=44004, Splash Screen com.deace.deacear$_4359#283205
2025-05-02 23:51:57.742 22827-23033 HoneySpace...askUseCase com.sec.android.app.launcher         I  topTasks 32676 ComponentInfo{com.deace.deacear/com.deace.deacear.MainActivity}
2025-05-02 23:51:57.742  2786-8004  WindowManager           system_server                        D  makeSurface duration=1 name=Splash Screen com.deace.deacear$_4359
2025-05-02 23:51:57.743  2786-8004  WindowManager           system_server                        D  SyncGroup 15878:  Unfinished container: ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676} mSyncState=2
2025-05-02 23:51:57.744  4359-4441  InsetsController        com.android.systemui                 I  onStateChanged: host=Splash Screen com.deace.deacear, from=android.view.ViewRootImpl.relayoutWindow:10165, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2316), mDisplayCutout=DisplayCutout{insets=Rect(0, 75 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(513, 0 - 567, 75), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2316 physicalDisplayWidth=1080 physicalDisplayHeight=2316 density={2.8125} cutoutSpec={M 0,0 H -9.466666666666667 V 26.66666666666667‬ H 9.466666666666667 V 0 H 0 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=8, center=Point(8, 8)}, RoundedCorner{position=TopRight, radius=8, center=Point(1072, 8)}, RoundedCorner{position=BottomRight, radius=8, center=Point(1072, 2308)}, RoundedCorner{position=BottomLeft, radius=8, center=Point(8, 2308)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2316), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(956, 0 - 1080, 75) rotation=0}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1440 displayHeight=3088 physicalPixelDisplaySizeRatio=1.0 rotation=0 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {d8380000 mType=statusBars mFrame=[0,0][1080,75] mVisible=true mFlags=[]}, InsetsSource: {d8380005 mType=mandatorySystemGestures mFrame=[0,0][1080,109] mVisible=true mFlags=[]}, InsetsSource: {d8380006 mType=tappableElement mFrame=[0,0][1080,75] mVisible=true mFlags=[]}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags=[]}, InsetsSource: {27 mType=displayCutout mFrame=[0,0][1080,75] mVisible=true mFlags=[]}, InsetsSource: {58b90001 mType=navigationBars mFrame=[0,2181][1080,2316] mVisible=true mFlags=[]}, InsetsSource: {58b90004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags=[]}, InsetsSource: {58b90005 mType=mandatorySystemGestures mFrame=[0,2181][1080,2316] mVisible=true mFlags=[]}, InsetsSource: {58b90006 mType=tappableElement mFrame=[0,2181][1080,2316] mVisible=true mFlags=[]}, InsetsSource: {58b90024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags=[]} }
2025-05-02 23:51:57.744  4359-4441  BLASTBufferQueue_Java   com.android.systemui                 I  new BLASTBufferQueue, mName= ViewRootImpl@29b3edd[deacear] mNativeObject= 0xb40000761a070a00 sc.mNativeObject= 0xb4000076132b6940 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3085 android.view.ViewRootImpl.relayoutWindow:10224 android.view.ViewRootImpl.performTraversals:4167 android.view.ViewRootImpl.doTraversal:3345 android.view.ViewRootImpl$TraversalRunnable.run:11437 android.view.Choreographer$CallbackRecord.run:1690 android.view.Choreographer$CallbackRecord.run:1699 android.view.Choreographer.doCallbacks:1154 android.view.Choreographer.doFrame:1080 android.view.Choreographer$FrameDisplayEventReceiver.run:1647 
2025-05-02 23:51:57.744  4359-4441  BLASTBufferQueue_Java   com.android.systemui                 I  update, w= 1080 h= 2316 mName = ViewRootImpl@29b3edd[deacear] mNativeObject= 0xb40000761a070a00 sc.mNativeObject= 0xb4000076132b6940 format= -3 caller= android.graphics.BLASTBufferQueue.<init>:89 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3085 android.view.ViewRootImpl.relayoutWindow:10224 android.view.ViewRootImpl.performTraversals:4167 android.view.ViewRootImpl.doTraversal:3345 android.view.ViewRootImpl$TraversalRunnable.run:11437 
2025-05-02 23:51:57.745  4359-4441  ViewRootIm...d[deacear] com.android.systemui                 I  Relayout returned: old=(0,0,1080,2316) new=(0,0,1080,2316) relayoutAsync=false req=(1080,2316)0 dur=7 res=0x403 s={true 0xb400007613aaf800} ch=true seqId=0
2025-05-02 23:51:57.745  4359-4441  ViewRootIm...d[deacear] com.android.systemui                 I  performConfigurationChange setNightDimText nightDimLevel=0
2025-05-02 23:51:57.745  4359-4441  ViewRootIm...d[deacear] com.android.systemui                 D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007613aaf800} hwInitialized=true
2025-05-02 23:51:57.745  2786-5397  CoreBackPreview         system_server                        D  Window{5c08345 u0 Splash Screen com.deace.deacear}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@ad8d5fd, mPriority=0, mIsAnimationCallback=false}
2025-05-02 23:51:57.747  4359-4441  ViewRootIm...d[deacear] com.android.systemui                 D  reportNextDraw android.view.ViewRootImpl.performTraversals:4781 android.view.ViewRootImpl.doTraversal:3345 android.view.ViewRootImpl$TraversalRunnable.run:11437 android.view.Choreographer$CallbackRecord.run:1690 android.view.Choreographer$CallbackRecord.run:1699 
2025-05-02 23:51:57.747  4359-4441  ViewRootIm...d[deacear] com.android.systemui                 I  Setup new sync=wmsSync-ViewRootImpl@29b3edd[deacear]#79
2025-05-02 23:51:57.747  4359-4441  ViewRootIm...d[deacear] com.android.systemui                 I  Creating new active sync group ViewRootImpl@29b3edd[deacear]#80
2025-05-02 23:51:57.747  4359-4441  SurfaceSyncGroup        com.android.systemui                 I  addLocalSync=ViewRootImpl@29b3edd[deacear]#80 to name=wmsSync-ViewRootImpl@29b3edd[deacear]#79, callers=android.window.SurfaceSyncGroup.add:431 android.window.SurfaceSyncGroup.add:392 android.window.SurfaceSyncGroup.add:340 android.view.ViewRootImpl.createSyncIfNeeded:4912 android.view.ViewRootImpl.performTraversals:4796 android.view.ViewRootImpl.doTraversal:3345 
2025-05-02 23:51:57.751  4359-4441  ViewRootIm...d[deacear] com.android.systemui                 I  registerCallbacksForSync syncBuffer=false
2025-05-02 23:51:57.754  4359-5762  ViewRootIm...d[deacear] com.android.systemui                 I  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-05-02 23:51:57.754  4359-5762  ViewRootIm...d[deacear] com.android.systemui                 I  mWNT: t=0xb4000075fccd2300 mBlastBufferQueue=0xb40000761a070a00 fn= 1 mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$8.onFrameDraw:13946 android.view.ThreadedRenderer$1.onFrameDraw:792 <bottom of call stack> 
2025-05-02 23:51:57.754  2786-2860  GameSDK@LifeCycle       system_server                        I  noteResumeComponent(): package name  : com.deace.deacear
2025-05-02 23:51:57.756 24622-27858 [AirCmd]_A...chDetector com...ng.android.service.aircommand  I  onTaskFocusChanged : taskId=32676, componentName=ComponentInfo{com.deace.deacear/com.deace.deacear.MainActivity}
2025-05-02 23:51:57.757  2786-2860  SGM:GameManager         system_server                        D  identifyGamePackage. com.deace.deacear, mCurrentUserId: 0, callerUserId: 0, callingMethodInfo: com.samsung.android.game.SemGameManager.isGamePackage(SemGameManager.java:111)
2025-05-02 23:51:57.757  2786-2860  SGM:PkgDataHelper       system_server                        D  getGamePkgData(). com.deace.deacear
2025-05-02 23:51:57.757  2786-2860  SGM:SemGameManager      system_server                        D  isGamePackage(), pkgName=com.deace.deacear, ret=false
2025-05-02 23:51:57.757  4359-5762  ViewRootIm...d[deacear] com.android.systemui                 I  Setting up sync and frameCommitCallback
2025-05-02 23:51:57.757 22827-22827 Edge.ActivityUtils      com.sec.android.app.launcher         I  HomePackage : com.sec.android.app.launcher, resumePackageName : com.deace.deacear
2025-05-02 23:51:57.757  4359-4449  BLASTBufferQueue        com.android.systemui                 I  [ViewRootImpl@29b3edd[deacear]#10475](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-05-02 23:51:57.758  4359-4449  ViewRootIm...d[deacear] com.android.systemui                 I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-05-02 23:51:57.758  4359-4449  SurfaceSyncGroup        com.android.systemui                 I  onTransactionReady mName=wmsSync-ViewRootImpl@29b3edd[deacear]#79 callback=57366840
2025-05-02 23:51:57.758  2786-2860  SGM:FgCheckThread       system_server                        D  noteResumeComponent(), received pkgName: com.deace.deacear, userId: 0
2025-05-02 23:51:57.758  4359-4441  ViewRootIm...d[deacear] com.android.systemui                 I  reportDrawFinished seqId=0
2025-05-02 23:51:57.759 27366-27850 SAMSUNGWALLET           com.samsung.android.spay             I  [Hint_SimpleHintProcessMonitor] isHomePackageRunning topPkgName = com.deace.deacear, topClsName = com.deace.deacear.MainActivity, return false , se : false
2025-05-02 23:51:57.759  2786-3726  SGM:FgCheckThread       system_server                        D  onLooperPrepared(), msg: MSG_APP_RESUME, pkgName: com.deace.deacear, userid: 0
2025-05-02 23:51:57.759  2786-3726  SGM:FgCheckThread       system_server                        D    handleResume(). pkgName: com.deace.deacear, userId: 0, isTunableApp: null
2025-05-02 23:51:57.759  2786-3726  SGM:FgCheckThread       system_server                        D  notifyFocusInOut(). of pkg: com.deace.deacear, type: 4, isTunableApp: false, userId: 0
2025-05-02 23:51:57.759  2786-8039  SGM:GameManager         system_server                        D  identifyForegroundApp. com.deace.deacear, mCurrentUserId: 0, callerUserId: 0
2025-05-02 23:51:57.759  2786-8039  SGM:PkgDataHelper       system_server                        D  getGamePkgData(). com.deace.deacear
2025-05-02 23:51:57.761  2786-5397  WindowManager           system_server                        D  finishDrawingWindow: Window{5c08345 u0 Splash Screen com.deace.deacear} mDrawState=DRAW_PENDING seqId=0
2025-05-02 23:51:57.761  2786-5397  WindowManager           system_server                        V  Finishing drawing window Window{5c08345 u0 Splash Screen com.deace.deacear}: mDrawState=DRAW_PENDING
2025-05-02 23:51:57.761  2786-5397  WindowManager           system_server                        V  Draw state now committed in Window{5c08345 u0 Splash Screen com.deace.deacear}
2025-05-02 23:51:57.762  2786-2861  WindowManager           system_server                        V  performShowLocked: mDrawState=HAS_DRAWN in Window{5c08345 u0 Splash Screen com.deace.deacear}
2025-05-02 23:51:57.762  2786-2861  WindowManager           system_server                        V  Start calculating TransitionInfo based on participants: {WallpaperWindowToken{1997ade token=android.os.Binder@3cea19}, ActivityRecord{6e13b33 u0 com.sec.android.app.launcher/.activities.LauncherActivity t30806}, Task{afd1290 #32676 type=standard A=10553:com.deace.deacear}, ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}}
2025-05-02 23:51:57.762 24622-27858 [AirCmd]_A...chDetector com...ng.android.service.aircommand  I  onTaskFocusChanged : taskId=32676, componentName=ComponentInfo{com.deace.deacear/com.deace.deacear.MainActivity}
2025-05-02 23:51:57.762  2786-2861  WindowManager           system_server                        V    Final targets: [Task{afd1290 #32676 type=standard A=10553:com.deace.deacear}, Task{e3dac7d #1 type=home}]
2025-05-02 23:51:57.764  2786-2861  SurfaceControl          system_server                        I  show, t=StartTransaction_SyncId<15878>, sc=Surface(name=ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676})/@0xeb42a31, caller=com.android.server.wm.Transition.onTransactionReady:2012 com.android.server.wm.BLASTSyncEngine$SyncGroup.finishNow:321 com.android.server.wm.BLASTSyncEngine$SyncGroup.tryFinish:242 com.android.server.wm.BLASTSyncEngine$SyncGroup.-$$Nest$mtryFinish:0 com.android.server.wm.BLASTSyncEngine.onSurfacePlacement:685 com.android.server.wm.RootWindowContainer.performSurfacePlacementNoTrace:993 
2025-05-02 23:51:57.764  2786-2861  WindowManager           system_server                        V  Calling onTransitionReady: {id=15878 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[
                                                                                                            {WCT{RemoteToken{54ef884 Task{afd1290 #32676 type=standard A=10553:com.deace.deacear}}} m=OPEN f=NONE leash=Surface(name=Task=32676)/@0xa771516 sb=Rect(0, 0 - 1080, 2316) eb=Rect(0, 0 - 1080, 2316) d=0 dim=true inset=Rect(0, 75 - 0, 135)},
                                                                                                            {WCT{RemoteToken{7622e52 Task{e3dac7d #1 type=home}}} m=TO_BACK f=SHOW_WALLPAPER|EDGE_EXTENSION_RESTRICTION leash=Surface(name=Task=1)/@0xf6963b5 sb=Rect(0, 0 - 1080, 2316) eb=Rect(0, 0 - 1080, 2316) d=0 inset=Rect(0, 75 - 0, 135)}
                                                                                                        ]}
2025-05-02 23:51:57.765  2786-2857  WindowManager           system_server                        V  Sent Transition #15878 createdAt=05-02 23:51:57.724 via request=TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=32676 displayId=0 isRunning=true baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.deace.deacear/.MainActivity } baseActivity=ComponentInfo{com.deace.deacear/com.deace.deacear.MainActivity} topActivity=ComponentInfo{com.deace.deacear/com.deace.deacear.MainActivity} origActivity=null realActivity=ComponentInfo{com.deace.deacear/com.deace.deacear.MainActivity} numActivities=1 lastActiveTime=********** supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 maxWidth=-1 maxHeight=-1 defaultMinSize=220 token=WCT{RemoteToken{54ef884 Task{afd1290 #32676 type=standard A=10553:com.deace.deacear}}} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 75 - 0, 0) topActivityInfo=ActivityInfo{c70386d com.deace.deacear.MainActivity} launchCookies=[android.os.BinderProxy@e476ca2] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=false isVisible=false isVisibleRequested=false isSleeping=false topActivityInSizeCompat=false topActivityEligibleForLetterboxEducation= false topActivityLetterboxed= false isFromDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 locusId=null displayAreaFeatureId=1 cameraCompatControlState=hidden originallySupportedMultiWindow=true hasWallpaper=false topActivityInFixedAspectRatio=false rootAffinity=10553:com.deace.deacear topActivityInDisplayCompat=false topActivityInBoundsCompat=false topActivityBounds=null singleTapFromLetterbox=false isTopTaskInStage=false topActivityUiMode=33 CoverLauncherWidgetTask=false CoverScreenTask=false isAllowedSeamlessRotation=false isTopTransparentActivity=false hasPopOver=false}, remoteTransition = RemoteTransition { remoteTransition = android.window.IRemoteTransition$Stub$Proxy@ececd33, appThread = android.app.IApplicationThread$Stub$Proxy@90518f0, debugName = QuickstepLaunch }, displayChange = null }
2025-05-02 23:51:57.766  2786-2857  WindowManager           system_server                        V      info={id=15878 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[
                                                                                                            {WCT{RemoteToken{54ef884 Task{afd1290 #32676 type=standard A=10553:com.deace.deacear}}} m=OPEN f=NONE leash=Surface(name=Task=32676)/@0xa771516 sb=Rect(0, 0 - 1080, 2316) eb=Rect(0, 0 - 1080, 2316) d=0 dim=true inset=Rect(0, 75 - 0, 135)},
                                                                                                            {WCT{RemoteToken{7622e52 Task{e3dac7d #1 type=home}}} m=TO_BACK f=SHOW_WALLPAPER|EDGE_EXTENSION_RESTRICTION leash=Surface(name=Task=1)/@0xf6963b5 sb=Rect(0, 0 - 1080, 2316) eb=Rect(0, 0 - 1080, 2316) d=0 inset=Rect(0, 75 - 0, 135)}
                                                                                                        ]}
2025-05-02 23:51:57.767  1712-3722  SurfaceFlinger          surfaceflinger                       I  id=283208 createSurf, flag=84000, f3fef8d ActivityRecordInputSink com.deace.deacear/.MainActivity#283208
2025-05-02 23:51:57.770  2786-8004  InsetsSourceProvider    system_server                        D  updateControlForTarget: control=InsetsSourceControl: {58b90001 mType=navigationBars initiallyVisible mSurfacePosition=Point(0, 2181) mInsetsHint=Insets{left=0, top=0, right=0, bottom=135}}, target=Window{5c08345 u0 Splash Screen com.deace.deacear}, from=com.android.server.wm.InsetsStateController.onControlTargetChanged:363 com.android.server.wm.InsetsStateController.onBarControlTargetChanged:332 com.android.server.wm.InsetsPolicy.updateBarControlTarget:173 com.android.server.wm.InsetsPolicy.updateSystemBars:843 com.android.server.wm.DisplayPolicy.updateSystemBarsLw:3484 
2025-05-02 23:51:57.771  2786-8004  InsetsSourceProvider    system_server                        D  updateControlForTarget: control=InsetsSourceControl: {d8380000 mType=statusBars initiallyVisible mSurfacePosition=Point(0, 0) mInsetsHint=Insets{left=0, top=75, right=0, bottom=0}}, target=Window{5c08345 u0 Splash Screen com.deace.deacear}, from=com.android.server.wm.InsetsStateController.onControlTargetChanged:363 com.android.server.wm.InsetsStateController.onBarControlTargetChanged:329 com.android.server.wm.InsetsPolicy.updateBarControlTarget:173 com.android.server.wm.InsetsPolicy.updateSystemBars:843 com.android.server.wm.DisplayPolicy.updateSystemBarsLw:3484 
2025-05-02 23:51:57.771 22827-22827 AppIconSolution         com.sec.android.app.launcher         I  getThemeIconWithBG called with public API, pkg = com.deace.deacear, mode = 48
2025-05-02 23:51:57.771  2786-8004  WindowManager           system_server                        D  updateSystemBarAttributes: displayId=0, win=Window{5c08345 u0 Splash Screen com.deace.deacear}, navColorWin=Window{5c08345 u0 Splash Screen com.deace.deacear}, focusedCanBeNavColorWin=false, behavior=1, appearance=24, statusBarAppearanceRegions=[AppearanceRegion{LIGHT_STATUS_BARS bounds=[0,0][1080,2316]}], requestedVisibilities=-9, from=com.android.server.wm.DisplayPolicy.finishPostLayoutPolicyLw:2339 com.android.server.wm.DisplayContent.applySurfaceChangesTransaction:6181 com.android.server.wm.RootWindowContainer.applySurfaceChangesTransaction:1183 
2025-05-02 23:51:57.771 22827-22827 AppIconSolution         com.sec.android.app.launcher         I  return adaptive icon for com.deace.deacear, isNight = true
2025-05-02 23:51:57.771 22827-22827 LauncherActivityInfo    com.sec.android.app.launcher         I  packageName: com.deace.deacear, useThemeIcon: false, height: 202, width: 202, density: 640
2025-05-02 23:51:57.772  4359-4359  SamsungLig...trolHelper com.android.systemui                 D  onStatusBarAppearanceChanged() -  sbModeChanged:false, statusBarMode:0, barState:MODE_TRANSPARENT, isKeyguardShowing:false, navbarColorManagedByIme:false, stackAppearanceChanged:true, (AppearanceRegion{LIGHT_STATUS_BARS bounds=[0,0][1080,2316]}, ), packageName:com.deace.deacear
2025-05-02 23:51:57.772  4359-4359  NavigationBar           com.android.systemui                 D  onSystemBarAttributesChanged() -  displayId:0, appearance:24, packageName: com.deace.deacear (APPEARANCE_LIGHT_STATUS_BARS APPEARANCE_LIGHT_NAVIGATION_BARS ), navbarColorManagedByIme:false
2025-05-02 23:51:57.772  4359-4359  SamsungLig...trolHelper com.android.systemui                 D  updateNavigationBar (BLACK button) isLightOpaque:false, hasLightNavigationBarFlag:true, packageName:com.deace.deacear, DirectReplying:false, NavBarColorMangedByIme:false, ForceDarkForScrim:false, QsCustomizing:false, PanelHasWhiteBg:false
2025-05-02 23:51:57.772  4359-4359  SamsungLig...trolHelper com.android.systemui                 D  updateStatusBar (BLACK icon) numStacks:1, StatusBarMode:MODE_TRANSPARENT, lightBarBounds:[Rect(0, 0 - 1080, 2316)], StatusBarState:SHADE, packageName:com.deace.deacear, isWhiteKeyguardStatusBar:false
2025-05-02 23:51:57.772  4359-4441  InsetsSourceConsumer    com.android.systemui                 I  applyRequestedVisibilityToControl: visible=true, type=navigationBars, host=Splash Screen com.deace.deacear
2025-05-02 23:51:57.773  4359-4441  InsetsSourceConsumer    com.android.systemui                 I  applyRequestedVisibilityToControl: visible=true, type=statusBars, host=Splash Screen com.deace.deacear
2025-05-02 23:51:57.775  1712-1712  Layer                   surfaceflinger                       I  Layer [Splash Screen com.deace.deacear$_4359#283205] hidden!! flag(0)
2025-05-02 23:51:57.775  1712-1712  Layer                   surfaceflinger                       I  Layer [ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}#283203] hidden!! flag(0)
2025-05-02 23:51:57.775  1712-1712  Layer                   surfaceflinger                       I  Layer [5c08345 Splash Screen com.deace.deacear#283204] hidden!! flag(0)
2025-05-02 23:51:57.794  1712-1712  SurfaceFlinger          surfaceflinger                       D  Display 4630947093241269891 HWC layers:
                                                                                                         DEVICE | 0xb40000730a69e280 | 0100 | RGBA_8888    |    4.0    8.0 1076.0 2308.0 |    0    0 1080 2316 | Wallpaper BBQ wrapper 5_system#81
                                                                                                         DEVICE | 0xb40000730a6cf380 | 0100 | RGBA_8888    |    0.0    0.0 1080.0 2316.0 |    0    0 1080 2316 | com.sec.android.app.launcher/com.sec[...]ities.LauncherActivity$_22827#283191
                                                                                                         CLIENT | 0xb40000732b21bd80 | 0100 | RGBA_8888    |    0.0  336.0 1080.0 1416.0 |  657  551  818  712 | Splash Screen com.deace.deacear$_4359#283205
                                                                                                         DEVICE | 0xb40000730a6e7080 | 0100 | RGBA_8888    |    0.0    0.0 1080.0   75.0 |    0    0 1080   75 | StatusBar$_4359#112
                                                                                                         DEVICE | 0xb40000732b2cab80 | 0100 | RGBA_8888    |    0.0    0.0   67.0  342.0 | 1013  698 1080 1040 | com.sec.android.app.launcher/com.sam[...]dge.CocktailBarService$_22827#283190
                                                                                                         DEVICE | 0xb40000731d7e4f80 | 0100 | RGBA_8888    |    0.0    0.0 1080.0  135.0 |    0 2181 1080 2316 | NavigationBar0$_4359#103
2025-05-02 23:51:57.821 17736-17736 DecorView               com.deace.deacear                    I  setWindowBackground: isPopOver=false color=fffafafa d=android.graphics.drawable.ColorDrawable@682e33a
2025-05-02 23:51:57.824 17736-17736 MainActivity            com.deace.deacear                    D  onCreate started.
2025-05-02 23:51:57.827 17736-17736 MainActivity            com.deace.deacear                    D  Initializing UI...
2025-05-02 23:51:57.827 17736-17736 MainActivity            com.deace.deacear                    D  GLSurfaceView setup complete.
2025-05-02 23:51:57.827 17736-17736 MainActivity            com.deace.deacear                    D  Adding long delay before panel initialization...
2025-05-02 23:51:57.845 24622-8961  [AirCmd]_A...chDetector com...ng.android.service.aircommand  I  onTaskStackChanged : componentName - ComponentInfo{com.deace.deacear/com.deace.deacear.MainActivity}
2025-05-02 23:51:57.885  2786-4160  MdnieScena...rolService system_server                        D   packageName : com.deace.deacear    className : com.deace.deacear.MainActivity
2025-05-02 23:51:58.130  2786-8004  PersonaActivityHelper   system_server                        D  token.toString()  Token{4c8f789 ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}}
2025-05-02 23:51:58.134  1712-1712  Layer                   surfaceflinger                       I  id=283203 removeFromCurrentState ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}#283203 (171)
2025-05-02 23:51:58.134  1712-1712  Layer                   surfaceflinger                       I  id=283208 removeFromCurrentState f3fef8d ActivityRecordInputSink com.deace.deacear/.MainActivity#283208 (171)
2025-05-02 23:51:58.134  1712-1712  Layer                   surfaceflinger                       I  id=283204 removeFromCurrentState 5c08345 Splash Screen com.deace.deacear#283204 (171)
2025-05-02 23:51:58.134  1712-1712  Layer                   surfaceflinger                       I  id=283205 removeFromCurrentState Splash Screen com.deace.deacear$_4359#283205 (171)
2025-05-02 23:51:58.134  1712-1712  Layer                   surfaceflinger                       I  id=283203 addToCurrentState ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}#283203 (171)
2025-05-02 23:51:58.134  1712-1712  Layer                   surfaceflinger                       I  id=283208 addToCurrentState f3fef8d ActivityRecordInputSink com.deace.deacear/.MainActivity#283208 (171)
2025-05-02 23:51:58.134  1712-1712  Layer                   surfaceflinger                       I  id=283204 addToCurrentState 5c08345 Splash Screen com.deace.deacear#283204 (171)
2025-05-02 23:51:58.134  1712-1712  Layer                   surfaceflinger                       I  id=283205 addToCurrentState Splash Screen com.deace.deacear$_4359#283205 (171)
2025-05-02 23:51:58.136  1712-1712  SurfaceFlinger          surfaceflinger                       D  Display 4630947093241269891 HWC layers:
                                                                                                         DEVICE | 0xb40000732b21bd80 | 0100 | RGBA_8888    |    0.0    0.0 1080.0 2316.0 |    0    0 1080 2316 | Splash Screen com.deace.deacear$_4359#283205
                                                                                                         DEVICE | 0xb40000730a6cfe80 | 0100 | RGBA_8888    |    0.0    0.0 1080.0   75.0 |    0    0 1080   75 | StatusBar$_4359#112
                                                                                                         DEVICE | 0xb40000732b2cab80 | 0100 | RGBA_8888    |    0.0    0.0   67.0  342.0 | 1013  698 1080 1040 | com.sec.android.app.launcher/com.sam[...]dge.CocktailBarService$_22827#283190
                                                                                                         DEVICE | 0xb40000730a6cff80 | 0100 | RGBA_8888    |    0.0    0.0 1080.0  135.0 |    0 2181 1080 2316 | NavigationBar0$_4359#103
2025-05-02 23:51:58.970  1058-1058  io_stats                iod                                  D  !@ Read_top(KB): pageboostd(5361) 4408 m.deace.deacear(17736) 536 system_server(2786) 192
2025-05-02 23:52:00.574  2786-5279  FreecessController      system_server                        D  com.deace.deacear(10553) is important[2]
2025-05-02 23:52:00.827 17736-17736 MainActivity            com.deace.deacear                    D  Beginning UI panel initialization with very long delays between initializations...
2025-05-02 23:52:00.827 17736-17736 MainActivity            com.deace.deacear                    D  Starting MainMenu initialization...
2025-05-02 23:52:00.827 17736-17736 MainMenu                com.deace.deacear                    D  Initializing...
2025-05-02 23:52:00.827 17736-17736 MainActivity            com.deace.deacear                    D  MainMenu initialized
2025-05-02 23:52:01.829 17736-17736 MainActivity            com.deace.deacear                    D  Starting GridPanel initialization...
2025-05-02 23:52:01.829 17736-17736 GridPanel               com.deace.deacear                    D  Initializing...
2025-05-02 23:52:01.829 17736-17736 MainActivity            com.deace.deacear                    D  GridPanel initialized
2025-05-02 23:52:02.830 17736-17736 MainActivity            com.deace.deacear                    D  Starting ExportPanel initialization...
2025-05-02 23:52:02.847 17736-17736 ExportPanel             com.deace.deacear                    D  Initializing...
2025-05-02 23:52:02.847 17736-17736 MainActivity            com.deace.deacear                    D  ExportPanel initialized
2025-05-02 23:52:03.848 17736-17736 MainActivity            com.deace.deacear                    D  Starting ConfigPanel initialization...
2025-05-02 23:52:05.579  2786-5279  FreecessController      system_server                        D  com.deace.deacear(10553) is important[2]
2025-05-02 23:52:05.747  2786-8039  SGM:GameManager         system_server                        D  identifyForegroundApp. com.deace.deacear, mCurrentUserId: 0, callerUserId: 0
2025-05-02 23:52:05.747  2786-8039  SGM:PkgDataHelper       system_server                        D  getGamePkgData(). com.deace.deacear
2025-05-02 23:52:05.850 17736-17736 ConfigPanel             com.deace.deacear                    D  Initializing...
2025-05-02 23:52:06.755  1712-1822  RenderEngine            surfaceflinger                       D  [SEC_GC_CMN_SF_EFFECTS] drawLayersInternal,1279, Rendering layer Splash Screen com.deace.deacear$_4359#283205 at index 1
2025-05-02 23:52:07.765 27366-27850 SAMSUNGWALLET           com.samsung.android.spay             I  [Hint_SimpleHintProcessMonitor] isHomePackageRunning topPkgName = com.deace.deacear, topClsName = com.deace.deacear.MainActivity, return false , se : false
2025-05-02 23:52:07.765  2786-2858  ScreenCurtainController system_server                        D  onForegroundActivitiesChanged: com.deace.deacear
2025-05-02 23:52:07.766  2786-5397  SGM:GameManager         system_server                        D  identifyForegroundApp. com.deace.deacear, mCurrentUserId: 0, callerUserId: 0
2025-05-02 23:52:07.766  2786-5397  SGM:PkgDataHelper       system_server                        D  getGamePkgData(). com.deace.deacear
2025-05-02 23:52:07.894  2786-4160  MdnieScena...rolService system_server                        D   packageName : com.deace.deacear    className : com.deace.deacear.MainActivity
2025-05-02 23:52:08.851 17736-17736 ConfigPanel             com.deace.deacear                    D  After 3s delay, initializing views...
2025-05-02 23:52:08.851 17736-17736 ConfigPanel             com.deace.deacear                    E  initializeViews() returned true but some views are not initialized
2025-05-02 23:52:08.852 17736-17736 MainActivity            com.deace.deacear                    D  ConfigPanel initialized
2025-05-02 23:52:10.583  2786-5279  FreecessController      system_server                        D  com.deace.deacear(10553) is important[2]
2025-05-02 23:52:10.853 17736-17736 MainActivity            com.deace.deacear                    D  Starting ImagePanel initialization...
2025-05-02 23:52:12.854 17736-17736 ImagePanel              com.deace.deacear                    D  Initializing...
2025-05-02 23:52:14.856 17736-17736 ImagePanel              com.deace.deacear                    D  After 2s delay, initializing views...
2025-05-02 23:52:14.856 17736-17736 ImagePanel              com.deace.deacear                    E  initializeViews() returned true but some views are not initialized
2025-05-02 23:52:14.857 17736-17736 MainActivity            com.deace.deacear                    D  ImagePanel initialized
2025-05-02 23:52:15.588  2786-5279  FreecessController      system_server                        D  com.deace.deacear(10553) is important[2]
2025-05-02 23:52:15.858 17736-17736 MainActivity            com.deace.deacear                    D  All UI Panels successfully initialized.
2025-05-02 23:52:15.859 17736-17736 MainActivity            com.deace.deacear                    D  Gesture Detector setup complete.
2025-05-02 23:52:15.859 17736-17736 MainActivity            com.deace.deacear                    D  Checking required permissions...
2025-05-02 23:52:15.861 17736-17736 MainActivity            com.deace.deacear                    D  Permissions needed. Requesting...
2025-05-02 23:52:15.873  2786-8004  ActivityTaskManager     system_server                        D  TaskLaunchParamsModifier:task=Task{afd1290 #32676 type=standard A=10553:com.deace.deacear} activity=ActivityRecord{2680e55 u0 com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity t-1} display-area-from-source=DefaultTaskDisplayArea_d0@179017892 display-id=0 task-display-area-windowing-mode=1 suggested-display-area=DefaultTaskDisplayArea_d0@179017892 inherit-from-source=fullscreen non-freeform-task-display-area display-area=DefaultTaskDisplayArea_d0@179017892 default-portrait freeform-size-mismatch=Rect(81, 549 - 999, 1707)
2025-05-02 23:52:15.880  2786-8004  ActivityTaskManager     system_server                        D  scheduleTopResumedActivityChanged, onTop=false, r=ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}, caller=com.android.server.wm.ActivityTaskSupervisor.updateTopResumedActivityIfNeeded:2873 com.android.server.wm.TaskFragment.setResumedActivity:580 com.android.server.wm.TaskFragment.onActivityStateChanged:889 com.android.server.wm.ActivityRecord.setState:6892 com.android.server.wm.TaskFragment.startPausing:2012 com.android.server.wm.TaskFragment.startPausing:1956 
2025-05-02 23:52:15.882  4359-4406  WindowManagerShell      com.android.systemui                 V  Transition requested: android.os.BinderProxy@fe9d837 TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=32676 displayId=0 isRunning=true baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.deace.deacear/.MainActivity } baseActivity=ComponentInfo{com.deace.deacear/com.deace.deacear.MainActivity} topActivity=ComponentInfo{com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity} origActivity=null realActivity=ComponentInfo{com.deace.deacear/com.deace.deacear.MainActivity} numActivities=2 lastActiveTime=********** supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 maxWidth=-1 maxHeight=-1 defaultMinSize=220 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@7fc01a4} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 75 - 0, 0) topActivityInfo=ActivityInfo{7a4db0d com.android.permissioncontroller.permission.ui.GrantPermissionsActivity} launchCookies=[android.os.BinderProxy@2dae620] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true isSleeping=false topActivityInSizeCompat=false topActivityEligibleForLetterboxEducation= false topActivityLetterboxed= false isFromDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 locusId=null displayAreaFeatureId=1 cameraCompatControlState=hidden originallySupportedMultiWindow=true hasWallpaper=false topActivityInFixedAspectRatio=false rootAffinity=10553:com.deace.deacear topActivityInDisplayCompat=false topActivityInBoundsCompat=false topActivityBounds=null singleTapFromLetterbox=false isTopTaskInStage=false topActivityUiMode=33 CoverLauncherWidgetTask=false CoverScreenTask=false isAllowedSeamlessRotation=false isTopTransparentActivity=false hasPopOver=false}, remoteTransition = null, displayChange = null }
2025-05-02 23:52:15.883 17736-17736 MainActivity            com.deace.deacear                    D  onCreate finished.
2025-05-02 23:52:15.889 17736-17736 MainActivity            com.deace.deacear                    D  onResume.
2025-05-02 23:52:15.889 17736-17736 MainActivity            com.deace.deacear                    D  Checking ARCore installation status...
2025-05-02 23:52:15.901 17736-17736 MainActivity            com.deace.deacear                    D  ARCore is supported.
2025-05-02 23:52:15.901 17736-17736 MainActivity            com.deace.deacear                    D  Initializing AR...
2025-05-02 23:52:15.902 17736-17736 third_part..._create.cc com.deace.deacear                    I  Entering ArSession_createWithFeaturesAndTrace
2025-05-02 23:52:15.902 17736-17736 third_part..._create.cc com.deace.deacear                    I  ARCore Version: SDK build name: 1.48
2025-05-02 23:52:15.904 17736-17736 third_part..._create.cc com.deace.deacear                    I  ARCore Version: APK version code: 250340293
2025-05-02 23:52:15.905 17736-17736 third_part..._create.cc com.deace.deacear                    I  Dynamite load ok.
2025-05-02 23:52:15.906 17736-17736 third_part..._create.cc com.deace.deacear                    I  ARCore SDK: dynamite number_of_symbols_loaded=317
2025-05-02 23:52:15.906 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.906624   17736 session_create_implementation.cc:256] Entering createImplementationWithFeaturesAndSettings. ARCore SDK version: [1.48.250210000].
2025-05-02 23:52:15.910 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.910689   17736 session_create_implementation.cc:217] AugmentedRegion downsample mode from Phenotype: true
2025-05-02 23:52:15.912 17736-20895 third_part..._helper.cc com.deace.deacear                    V  JniHelper: attached thread (Called from line 368).
2025-05-02 23:52:15.922 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.922462   17736 session_create_implementation_shared.cc:2440] min_apk_version_code is: 250210000, phenotype flag of enable_dual_camera_support is: false, phenotype flag of unified_data_source_status is: 2, is_dual_camera_supported based on device profile is: false
2025-05-02 23:52:15.922 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.922546   17736 session_create_implementation_shared.cc:2451] Settings.camera_stack_option is not specified
2025-05-02 23:52:15.922 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.922556   17736 session_create_implementation_shared.cc:2524] Datasource will be created with camera_stack_option = kUnifiedMono
2025-05-02 23:52:15.922 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.922562   17736 android_camera.cc:169] Camera start operation timeout set to 4000 ms.
2025-05-02 23:52:15.922 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.922587   17736 android_camera.cc:1825] Initializing camera manager.
2025-05-02 23:52:15.925 17736-17736 VendorTagDescriptor     com.deace.deacear                    D  addVendorDescriptor: vendor tag id 11706189966126095484 added
2025-05-02 23:52:15.925 17736-17736 VendorTagDescriptor     com.deace.deacear                    D  addVendorDescriptor: vendor tag id 15055940602041719656 added
2025-05-02 23:52:15.932 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.932366   17736 android_camera.cc:1851] Camera manager initialized successfully with 4 cameras.
2025-05-02 23:52:15.932 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.932512   17736 android_camera.cc:761] [Camera=0; State=CLOSED] Reset cleanly got to CLOSED state.
2025-05-02 23:52:15.932 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.932677   17736 android_camera.cc:761] [Camera=3; State=CLOSED] Reset cleanly got to CLOSED state.
2025-05-02 23:52:15.932 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.932812   17736 android_camera.cc:761] [Camera=2; State=CLOSED] Reset cleanly got to CLOSED state.
2025-05-02 23:52:15.932 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.932915   17736 android_camera.cc:761] [Camera=1; State=CLOSED] Reset cleanly got to CLOSED state.
2025-05-02 23:52:15.933 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.933197   17736 session_create_implementation_shared.cc:2636] Persistent online recalibration is enabled by Phenotype.
2025-05-02 23:52:15.936 17736-17736 tflite                  com.deace.deacear                    I  Replacing 2 out of 2 node(s) with delegate (TfLiteXNNPackDelegate) node, yielding 1 partitions for subgraph 0.
2025-05-02 23:52:15.936 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.936804   17736 online_calibration_manager.cc:94] OnlineCalibrationManager: Could not open /data/user/0/com.deace.deacear/cache/arcore-online-recalibration for reading.
2025-05-02 23:52:15.940 17736-17736 ARCore-ContextUtils     com.deace.deacear                    W  The API key for use with the Google AR service could not be obtained!
2025-05-02 23:52:15.952 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.952379   17736 android_sensors.cc:136] Using uncalibrated accelerometer.
2025-05-02 23:52:15.952 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.952542   17736 android_sensors.cc:155] Uncalibrated magnetometer available.
2025-05-02 23:52:15.952 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.952715   17736 android_sensors.cc:163] Calibrated magnetometer available.
2025-05-02 23:52:15.952 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.952788   17736 android_sensors.cc:178] Using SENSOR_TYPE_LIGHT
2025-05-02 23:52:15.952 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.952848   17736 android_sensors.cc:178] Using SENSOR_TYPE_PRESSURE
2025-05-02 23:52:15.952 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.952918   17736 android_sensors.cc:178] Using SENSOR_TYPE_PROXIMITY
2025-05-02 23:52:15.952 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.952984   17736 android_sensors.cc:178] Using SENSOR_TYPE_GRAVITY
2025-05-02 23:52:15.953 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.953039   17736 android_sensors.cc:178] Using SENSOR_TYPE_ROTATION_VECTOR
2025-05-02 23:52:15.953 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.953115   17736 android_sensors.cc:178] Using SENSOR_TYPE_GAME_ROTATION_VECTOR
2025-05-02 23:52:15.953 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.953186   17736 android_sensors.cc:181] Could not find SENSOR_TYPE_GEOMAGNETIC_ROTATION_VECTOR
2025-05-02 23:52:15.953 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.953251   17736 android_sensors.cc:178] Using SENSOR_TYPE_STEP_DETECTOR
2025-05-02 23:52:15.953 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.953322   17736 android_sensors.cc:181] Could not find SENSOR_TYPE_HINGE_ANGLE
2025-05-02 23:52:15.953 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.953696   17736 android_platform_checks.cc:206] IsZeroRotationLandscape = false
2025-05-02 23:52:15.953 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.953977   17736 app_version_util.cc:50] Package name: com.google.ar.core App version: 1.48.250340293
2025-05-02 23:52:15.954 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.954056   17736 logger.h:28] DataSourceMetrics: CamerasInit: 625ns
2025-05-02 23:52:15.954 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.954087   17736 image.cc:325] ARCore feature image_lock_mode is: IGNORE_LOCK (0) 
2025-05-02 23:52:15.954 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.954102   17736 session_create_implementation_shared.cc:1644] CPU Image enable frame delay to compensate delay: true
2025-05-02 23:52:15.960 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.960722   17736 config_helpers.cc:429] IMU sigma values from data source are used
2025-05-02 23:52:15.961 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.961080   17736 deep_io.cc:106] Load deepIO config: deepio_v5_pb.uncompressed
2025-05-02 23:52:15.961 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.961183   17736 deep_io.cc:117] Load TFLite model: deepio_v5_tflite.uncompressed
2025-05-02 23:52:15.963 17736-17736 tflite                  com.deace.deacear                    I  Replacing 65 out of 83 node(s) with delegate (TfLiteXNNPackDelegate) node, yielding 19 partitions for subgraph 0.
2025-05-02 23:52:15.966 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.966558   17736 session_create_implementation_shared.cc:985] DeepIO release instantiated successfully.
2025-05-02 23:52:15.966 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.966675   17736 inertial_estimator.cc:95] DeepIO model id: 5
2025-05-02 23:52:15.966 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.966717   17736 config_helpers.cc:378] Failed to find IMU intrinsic covariance matrix in profile for id: 100
2025-05-02 23:52:15.966 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.966759   17736 config_helpers.cc:136] Does not find camera intrinsic covariance matrix in profile for id 0
2025-05-02 23:52:15.966 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.966765   17736 config_helpers.cc:209] Does not find extrinsic covariance matrix in profile for IMU id 100
2025-05-02 23:52:15.967 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.967379   17736 motion_tracking_context.cc:1122] VPS data synchronizer is successfully initialized.
2025-05-02 23:52:15.967 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.967413   17736 feature_matcher_and_filter.cc:213] Enabled the robustification to large-sized and fast-moving objects on this mono-camera device.
2025-05-02 23:52:15.968 17736-17736 tflite                  com.deace.deacear                    I  Replacing 3 out of 3 node(s) with delegate (TfLiteXNNPackDelegate) node, yielding 1 partitions for subgraph 0.
2025-05-02 23:52:15.968 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.968499   17736 pose_confidence_estimator.cc:230] Pose confidence model loaded successfully
2025-05-02 23:52:15.968 17736-20048 third_part..._helper.cc com.deace.deacear                    V  ~JniHelper: about to detach thread (Called from line 368).
2025-05-02 23:52:15.968 17736-20048 third_part..._helper.cc com.deace.deacear                    V  ~JniHelper: detached thread (Called from line 368).
2025-05-02 23:52:15.968 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.968908   17736 bundle_adjustment_initializer.cc:168] [SSBA Initialization] SSBA initialization uses imu preintegration error with shared bias term.
2025-05-02 23:52:15.973 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.973312   17736 asset_manager_util.cc:61] Created global reference to asset manager.
2025-05-02 23:52:15.973 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.973358   17736 session_create_implementation_shared.cc:1669] Normal detector created.
2025-05-02 23:52:15.976 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.976043   17736 planar_target_tracking_manager.h:116] Config of PlanarTargetTrackingManager:
                                                                                                    -pose_refinement_with_detection_interval_ns: 0
                                                                                                    -min_interval_between_detections_ns: 500000000
                                                                                                    -filter_parallax: false
                                                                                                    -filter_result: true
                                                                                                    -multiple_targets: true
                                                                                                    -mini_detection: true
                                                                                                    -tracking_mode: 1
                                                                                                    -camera_id: 0
2025-05-02 23:52:15.980 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.980712   17736 camera_config_manager.cc:731] UpdateBugFixes on CameraConfigManager is unimplemented!
2025-05-02 23:52:15.980 17736-17736 third_part...age_jni.cc com.deace.deacear                    I  Loading AImage symbols
2025-05-02 23:52:15.981 17736-17736 MainActivity            com.deace.deacear                    D  AR Session created.
2025-05-02 23:52:15.981 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.981256   17736 session.cc:623] New session config:
                                                                                                    ArConfig
                                                                                                     augmented_face_mode:           AR_AUGMENTED_FACE_MODE_DISABLED
                                                                                                     augmented_image_database:      <null>
                                                                                                     cloud_anchor_mode:             AR_CLOUD_ANCHOR_MODE_DISABLED
                                                                                                     depth_mode:                    AR_DEPTH_MODE_DISABLED
                                                                                                     semantic_mode:                AR_SEMANTIC_MODE_DISABLED
                                                                                                     focus_mode:                    AR_FOCUS_MODE_FIXED
                                                                                                     geospatial_mode:               AR_GEOSPATIAL_MODE_DISABLED
                                                                                                     instant_placement_mode:        AR_INSTANT_PLACEMENT_MODE_DISABLED
                                                                                                     light_estimation_mode:         AR_LIGHT_ESTIMATION_MODE_ENVIRONMENTAL_HDR
                                                                                                     plane_finding_mode:            AR_PLANE_FINDING_MODE_VERTICAL
                                                                                                     update_mode:                   AR_UPDATE_MODE_LATEST_CAMERA_IMAGE
2025-05-02 23:52:15.981 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.981319   17736 session.cc:616] Session::CheckAndWriteCurrentConfig returning OK.
2025-05-02 23:52:15.981 17736-17736 MainActivity            com.deace.deacear                    D  AR Session configured.
2025-05-02 23:52:15.981 17736-17736 MainActivity            com.deace.deacear                    D  ARRenderer and ARSessionManager created.
2025-05-02 23:52:15.981 17736-17736 MainActivity            com.deace.deacear                    D  Setting ARSessionManager for UI panels...
2025-05-02 23:52:15.981 17736-17736 GridPanel               com.deace.deacear                    D  ARSessionManager set.
2025-05-02 23:52:15.981 17736-17736 ImagePanel              com.deace.deacear                    D  ARSessionManager set for ImagePanel.
2025-05-02 23:52:15.981 17736-17736 ARRenderer              com.deace.deacear                    D  Plane Visualizer Enabled set to: true
2025-05-02 23:52:15.981 17736-17736 ARSessionManager        com.deace.deacear                    D  Set Plane Visibility: true
2025-05-02 23:52:15.981 17736-17736 ARSessionManager        com.deace.deacear                    D  Update Grid Line Thickness: 1.0
2025-05-02 23:52:15.981 17736-17736 MainActivity            com.deace.deacear                    D  ARSessionManager set for panels.
2025-05-02 23:52:15.981 17736-17736 MainActivity            com.deace.deacear                    D  AR Initialization complete. Loading layout hidden.
2025-05-02 23:52:15.984 17736-17771 NativeCust...ncyManager com.deace.deacear                    D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-05-02 23:52:15.986  1712-3722  SurfaceFlinger          surfaceflinger                       I  id=283214 createSurf, flag=84004, 8e51b7a com.deace.deacear/com.deace.deacear.MainActivity#283214
2025-05-02 23:52:15.989 17736-17736 InsetsController        com.deace.deacear                    I  onStateChanged: host=com.deace.deacear/com.deace.deacear.MainActivity, from=android.view.ViewRootImpl.setView:1797, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2316), mDisplayCutout=DisplayCutout{insets=Rect(0, 75 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(513, 0 - 567, 75), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2316 physicalDisplayWidth=1080 physicalDisplayHeight=2316 density={2.8125} cutoutSpec={M 0,0 H -9.466666666666667 V 26.66666666666667‬ H 9.466666666666667 V 0 H 0 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=8, center=Point(8, 8)}, RoundedCorner{position=TopRight, radius=8, center=Point(1072, 8)}, RoundedCorner{position=BottomRight, radius=8, center=Point(1072, 2308)}, RoundedCorner{position=BottomLeft, radius=8, center=Point(8, 2308)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2316), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(956, 0 - 1080, 75) rotation=0}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1440 displayHeight=3088 physicalPixelDisplaySizeRatio=1.0 rotation=0 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {d8380000 mType=statusBars mFrame=[0,0][1080,75] mVisible=true mFlags=[]}, InsetsSource: {d8380005 mType=mandatorySystemGestures mFrame=[0,0][1080,109] mVisible=true mFlags=[]}, InsetsSource: {d8380006 mType=tappableElement mFrame=[0,0][1080,75] mVisible=true mFlags=[]}, InsetsSource: {27 mType=displayCutout mFrame=[0,0][1080,75] mVisible=true mFlags=[]}, InsetsSource: {58b90001 mType=navigationBars mFrame=[0,2181][1080,2316] mVisible=true mFlags=[]}, InsetsSource: {58b90004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags=[]}, InsetsSource: {58b90005 mType=mandatorySystemGestures mFrame=[0,2181][1080,2316] mVisible=true mFlags=[]}, InsetsSource: {58b90006 mType=tappableElement mFrame=[0,2181][1080,2316] mVisible=true mFlags=[]}, InsetsSource: {58b90024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags=[]} }
2025-05-02 23:52:15.989 17736-17736 ViewRootIm...nActivity] com.deace.deacear                    I  synced displayState. AttachInfo displayState=2
2025-05-02 23:52:15.990 17736-17736 ViewRootIm...nActivity] com.deace.deacear                    I  setView = com.android.internal.policy.DecorView@268d389 TM=true
2025-05-02 23:52:15.991 17736-17736 MainActivity            com.deace.deacear                    D  onPause.
2025-05-02 23:52:15.991 17736-17736 ARSessionManager        com.deace.deacear                    D  Session paused state recorded
2025-05-02 23:52:15.991 17736-17736 MainActivity            com.deace.deacear                    D  ARSessionManager notified of pause
2025-05-02 23:52:15.991 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.991625   17736 session.cc:1702] Entering Session::Pause.
2025-05-02 23:52:15.991 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.991711   17736 plane_manager.cc:968] PopulatePlaneEstimationStatistics  number_of_normal_segmentations: 0, number_of_plane_normal_segment_matches: 0, vertical_planes_boundary_area: 0, vertical_planes_ml_boundary_growth: 0, number_of_vertical_planes: 0, number_of_vertical_planes_with_ml_boundary_growth: 0, avg_duration_in_seconds: 0, tof_merge_quality_stats_.Empty(): true, tof_merge_quality_stats_.hist_area_overlap_ratio_: [0.750, 0.760): 0.000000, [0.760, 0.770): 0.000000, [0.770, 0.780): 0.000000, [0.780, 0.790): 0.000000, [0.790, 0.800): 0.000000, [0.800, 0.810): 0.000000, [0.810, 0.820): 0.000000, [0.820, 0.830): 0.000000, [0.830, 0.840): 0.000000, [0.840, 0.850): 0.000000, [0.850, 0.860): 0.000000, [0.860, 0.870): 0.000000, [0.870, 0.880): 0.000000, [0.880, 0.890): 0.000000, [0.890, 0.900): 0.000000, [0.900, 0.910): 0.000000, [0.910, 0.920): 0.000000, [0.920, 0.930): 0.000000, [0.930, 0.940): 0.000000, [0.940, 0.950): 0.000000, [0.950, 0.960): 0.000000, [0.960, 0.970): 0.000000, [0.970, 0.980): 0.000000, [0.980, 0.990): 0.000000, [0.990, 1.000): 0.000000, [1.000, inf): 0.000000, , tof_merge_quality_stats_.hist_feature_overlap_ratio_: [0.900, 0.905): 0.000000, [0.905, 0.910): 0.000000, [0.910, 0.915): 0.000000, [0.915, 0.920): 0.000000, [0.920, 0.925): 0.000000, [0.925, 0.930): 0.000000, [0.930, 0.935): 0.000000, [0.935, 0.940): 0.000000, [0.940, 0.945): 0.000000, [0.945, 0.950): 0.000000, [0.950, 0.955): 0.000000, [0.955, 0.960): 0.000000, [0.960, 0.965): 0.000000, [0.965, 0.970): 0.000000, [0.970, 0.975): 0.000000, [0.975, 0.980): 0.000000, [0.980, 0.985): 0.000000, [0.985, 0.990): 0.000000, [0.990, 0.995): 0.000000, [0.995, 1.000): 0.000000, [1.000, inf): 0.000000, , tof_merge_quality_stats_.hist_normal_closeness_: [0.900, 0.905): 0.000000, [0.905, 0.910): 0.000000, [0.910, 0.915): 0.000000, [0.915, 0.920): 0.000000, [0.920, 0.925): 0.000000, [0.925, 0.930): 0.000000, [0.930, 0.935): 0.000000, [0.935, 0.940): 0.000000, [0.940, 0.945): 0.000000, [0.945, 0.950): 0.000000, [0.950, 0.955): 0.000000, [0.955, 0.960): 0.000000, [0.960, 0.965): 0.000000, [0.965, 0.970): 0.000000, [0.970, 0.975): 0.000000, [0.975, 0.980): 0.000000, [0.980, 0.985): 0.000000, [0.985, 0.990): 0.000000, [0.990, 0.995): 0.000000, [0.995, 1.000): 0.000000, [1.000, inf): 0.000000, , tof_merge_quality_stats_.hist_plane_distance_: [0.000, 0.010): 0.000000, [0.010, 0.020): 0.000000, [0.020, 0.030): 0.000000, [0.030, 0.040): 0.000000, [0.040, 0.050): 0.000000, [0.050, 0.060): 0.000000, [0.060, 0.070): 0.000000, [0.070, 0.080): 0.000000, [0.080, 0.090): 0.000000, [0.090, 0.100): 0.000000, [0.100, 0.110): 0.000000, [0.110, 0.120): 0.000000, [0.120, 0.130): 0.000000, [0.130, 0.140): 0.000000, [0.140, 0.150): 0.000000, [0.150, 0.160): 0.000000, [0.160, 0.170): 0.000000, [0.170, 0.180): 0.000000, [0.180, 0.190): 0.000000, [0.190, 0.200): 0.000000, [0.200, 0.210): 0.000000, [0.210, 0.220): 0.000000, [0.220, 0.230): 0.000000, [0.230, 0.240): 0.000000, [0.240, 0.250): 0.000000, [0.250, 0.260): 0.000000, [0.260, 0.270): 0.000000, [0.270, 0.280): 0.000000, [0.280, 0.290): 0.000000, [0.290, 0.300): 0.000000, [0.300, 0.310): 0.000000, [0.310, 0.320): 0.000000, [0.320, 0.330): 0.000000, [0.330, 0.340): 0.000000, [0.340, 0.350): 0.000000, [0.350, 0.360): 0.000000, [0.360, 0.370): 0.000000, [0.370, 0.380): 0.000000, [0.380, 0.390): 0.000000, [0.390, 0.400): 0.000000, [0.400, 0.410): 0.000000, [0.410, 0.420): 0.000000, [0.420, 0.430): 0.000000, [0.430, 0.440): 0.000000, [0.440, 0.450): 0.000000, [0.450, 0.460): 0.000000, [0.460, 0.470): 0.000000, [0.470, 0.480): 0.000000, [0.480, 0.490): 0.000000, [0.490, 0.500): 0.000000, [0.500, inf): 0.000000, , ms_merge_quality_stats_.Empty(): true, ms_merge_quality_stats_.hist_area_overlap_ratio_: [0.750, 0.760): 0.000000, [0.760, 0.770): 0.000000, [0.770, 0.780): 0.000000, [0.780, 0.790): 0.000000, [0.790, 0.800): 0.000000, [0.800, 0.810): 0.000000, [0.810, 0.820): 0.000000, [0.820, 0.830): 0.000000, [0.830, 0.840): 0.000000, [0.840, 0.8
2025-05-02 23:52:15.991 17736-17736 native                  com.deace.deacear                    I  
2025-05-02 23:52:15.991 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.991870   17736 online_calibration_manager.cc:167] OnlineCalibrationManager: Discarding the new online recalibration estimates.
2025-05-02 23:52:15.991 17736-17736 native                  com.deace.deacear                    I  I0000 00:00:1746222735.991880   17736 session.cc:1768] Session::PauseWithAnalytics returning OK.
2025-05-02 23:52:15.992 17736-20895 native                  com.deace.deacear                    W  W0000 00:00:1746222735.992708   20895 analytics_logger.cc:406] Could not send event. Event will be dropped.
2025-05-02 23:52:15.992 17736-20895 native                  com.deace.deacear                    W  W0000 00:00:1746222735.992962   20895 analytics_logger.cc:406] Could not send event. Event will be dropped.
2025-05-02 23:52:15.993 17736-20895 native                  com.deace.deacear                    W  W0000 00:00:1746222735.993129   20895 analytics_logger.cc:406] Could not send event. Event will be dropped.
2025-05-02 23:52:15.993 17736-20895 native                  com.deace.deacear                    W  W0000 00:00:1746222735.993299   20895 analytics_logger.cc:406] Could not send event. Event will be dropped.
2025-05-02 23:52:15.993 17736-20895 native                  com.deace.deacear                    W  W0000 00:00:1746222735.993469   20895 analytics_logger.cc:406] Could not send event. Event will be dropped.
2025-05-02 23:52:15.993 17736-20895 native                  com.deace.deacear                    W  W0000 00:00:1746222735.993640   20895 analytics_logger.cc:406] Could not send event. Event will be dropped.
2025-05-02 23:52:15.993 17736-20895 native                  com.deace.deacear                    W  W0000 00:00:1746222735.993812   20895 analytics_logger.cc:406] Could not send event. Event will be dropped.
2025-05-02 23:52:15.993 17736-20895 native                  com.deace.deacear                    W  W0000 00:00:1746222735.993992   20895 analytics_logger.cc:406] Could not send event. Event will be dropped.
2025-05-02 23:52:15.994 17736-20895 native                  com.deace.deacear                    W  W0000 00:00:1746222735.994160   20895 analytics_logger.cc:406] Could not send event. Event will be dropped.
2025-05-02 23:52:15.994 17736-20895 native                  com.deace.deacear                    W  W0000 00:00:1746222735.994328   20895 analytics_logger.cc:406] Could not send event. Event will be dropped.
2025-05-02 23:52:15.994 17736-20895 native                  com.deace.deacear                    W  W0000 00:00:1746222735.994509   20895 analytics_logger.cc:406] Could not send event. Event will be dropped.
2025-05-02 23:52:15.994 17736-20895 native                  com.deace.deacear                    W  W0000 00:00:1746222735.994691   20895 analytics_logger.cc:406] Could not send event. Event will be dropped.
2025-05-02 23:52:15.996 17736-17736 MainActivity            com.deace.deacear                    D  AR Session paused
2025-05-02 23:52:15.996 17736-17736 MainActivity            com.deace.deacear                    D  GLSurfaceView paused
2025-05-02 23:52:15.997  2786-2860  SGM:FgCheckThread       system_server                        D  notePauseComponent(), received pkgName: com.deace.deacear, userId: 0
2025-05-02 23:52:15.997  2786-5397  WindowManager           system_server                        D  rotationForOrientation, orientationSource=ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}
2025-05-02 23:52:16.001  1712-1712  Layer                   surfaceflinger                       I  Layer [8e51b7a com.deace.deacear/com.deace.deacear.MainActivity#283214] hidden!! flag(0)
2025-05-02 23:52:16.001 17736-17771 OpenGLRenderer          com.deace.deacear                    D  HWUI - treat SMPTE_170M as sRGB
2025-05-02 23:52:16.004 17736-17736 SurfaceView@2b5191a     com.deace.deacear                    I  onWindowVisibilityChanged(0) true android.opengl.GLSurfaceView{2b5191a V.E...... ......I. 0,0-0,0 #7f090041 app:id/ar_surface_view} of ViewRootImpl@4ea1142[MainActivity]
2025-05-02 23:52:16.004 17736-17736 SurfaceView@2b5191a     com.deace.deacear                    D  45422874 updateSurface: has no frame
2025-05-02 23:52:16.005 17736-17736 AnimatorSet             com.deace.deacear                    D  mReversing is false. Don't call initChildren.
2025-05-02 23:52:16.006 17736-17736 AnimatorSet             com.deace.deacear                    D  mReversing is false. Don't call initChildren.
2025-05-02 23:52:16.006 17736-17736 AnimatorSet             com.deace.deacear                    D  mReversing is false. Don't call initChildren.
2025-05-02 23:52:16.007 17736-17736 AnimatorSet             com.deace.deacear                    D  mReversing is false. Don't call initChildren.
2025-05-02 23:52:16.008 17736-17736 AnimatorSet             com.deace.deacear                    D  mReversing is false. Don't call initChildren.
2025-05-02 23:52:16.009 17736-17736 AnimatorSet             com.deace.deacear                    D  mReversing is false. Don't call initChildren.
2025-05-02 23:52:16.011  2786-8004  SGM:GameManager         system_server                        D  identifyForegroundApp. com.deace.deacear, mCurrentUserId: 0, callerUserId: 0
2025-05-02 23:52:16.011  2786-8004  SGM:PkgDataHelper       system_server                        D  getGamePkgData(). com.deace.deacear
2025-05-02 23:52:16.012  2786-8039  CoreBackPreview         system_server                        D  Window{8e51b7a u0 com.deace.deacear/com.deace.deacear.MainActivity}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@4f261fc, mPriority=0, mIsAnimationCallback=false}
2025-05-02 23:52:16.013  2786-8039  WindowManager           system_server                        V  Relayout Window{8e51b7a u0 com.deace.deacear/com.deace.deacear.MainActivity}: viewVisibility=0 req=1080x2316 ty=1 d0
2025-05-02 23:52:16.013  1712-1802  SurfaceFlinger          surfaceflinger                       I  id=283215 createSurf, flag=44004, com.deace.deacear/com.deace.deacear.MainActivity$_17736#283215
2025-05-02 23:52:16.014  2786-8039  WindowManager           system_server                        D  makeSurface duration=1 name=com.deace.deacear/com.deace.deacear.MainActivity$_17736
2025-05-02 23:52:16.017 17736-17736 BufferQueueProducer     com.deace.deacear                    I  [](id:45480000000f,api:0,p:523591056,c:17736) setDequeueTimeout:2077252342
2025-05-02 23:52:16.017 17736-17736 BLASTBufferQueue_Java   com.deace.deacear                    I  new BLASTBufferQueue, mName= ViewRootImpl@4ea1142[MainActivity] mNativeObject= 0xb400007632ed1600 sc.mNativeObject= 0xb40000761ee29600 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3085 android.view.ViewRootImpl.relayoutWindow:10224 android.view.ViewRootImpl.performTraversals:4167 android.view.ViewRootImpl.doTraversal:3345 android.view.ViewRootImpl$TraversalRunnable.run:11437 android.view.Choreographer$CallbackRecord.run:1690 android.view.Choreographer$CallbackRecord.run:1699 android.view.Choreographer.doCallbacks:1154 android.view.Choreographer.doFrame:1080 android.view.Choreographer$FrameDisplayEventReceiver.run:1647 
2025-05-02 23:52:16.017 17736-17736 BLASTBufferQueue_Java   com.deace.deacear                    I  update, w= 1080 h= 2316 mName = ViewRootImpl@4ea1142[MainActivity] mNativeObject= 0xb400007632ed1600 sc.mNativeObject= 0xb40000761ee29600 format= -3 caller= android.graphics.BLASTBufferQueue.<init>:89 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3085 android.view.ViewRootImpl.relayoutWindow:10224 android.view.ViewRootImpl.performTraversals:4167 android.view.ViewRootImpl.doTraversal:3345 android.view.ViewRootImpl$TraversalRunnable.run:11437 
2025-05-02 23:52:16.018 17736-17736 ViewRootIm...nActivity] com.deace.deacear                    I  Relayout returned: old=(0,0,1080,2316) new=(0,0,1080,2316) relayoutAsync=false req=(1080,2316)0 dur=4 res=0x403 s={true 0xb400007632ece800} ch=true seqId=0
2025-05-02 23:52:16.018 17736-17736 ViewRootIm...nActivity] com.deace.deacear                    I  performConfigurationChange setNightDimText nightDimLevel=0
2025-05-02 23:52:16.019 17736-17736 ViewRootIm...nActivity] com.deace.deacear                    D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007632ece800} hwInitialized=true
2025-05-02 23:52:16.020 17736-17736 SurfaceView@2b5191a     com.deace.deacear                    D  45422874 updateSurface: has no frame
2025-05-02 23:52:16.020 17736-17736 SurfaceView@2b5191a     com.deace.deacear                    I  windowStopped(false) true android.opengl.GLSurfaceView{2b5191a V.E...... ......ID 0,0-1080,2106 #7f090041 app:id/ar_surface_view} of ViewRootImpl@4ea1142[MainActivity]
2025-05-02 23:52:16.020 17736-17736 SurfaceView@2b5191a     com.deace.deacear                    D  45422874 updateSurface: has no frame
2025-05-02 23:52:16.021 17736-17736 ViewRootIm...nActivity] com.deace.deacear                    D  reportNextDraw android.view.ViewRootImpl.performTraversals:4781 android.view.ViewRootImpl.doTraversal:3345 android.view.ViewRootImpl$TraversalRunnable.run:11437 android.view.Choreographer$CallbackRecord.run:1690 android.view.Choreographer$CallbackRecord.run:1699 
2025-05-02 23:52:16.021 17736-17736 SurfaceView             com.deace.deacear                    I  45422874 Changes: creating=true format=true size=true visible=true alpha=false hint=false visible=true left=true top=true z=false attached=true lifecycleStrategy=false
2025-05-02 23:52:16.021  1712-1802  SurfaceFlinger          surfaceflinger                       I  id=283216 createSurf, flag=20004, Bounds for - com.deace.deacear/com.deace.deacear.MainActivity@0#283216
2025-05-02 23:52:16.022  1712-1802  SurfaceFlinger          surfaceflinger                       I  id=283217 createSurf, flag=84004, SurfaceView[com.deace.deacear/com.deace.deacear.MainActivity]@0#283217
2025-05-02 23:52:16.022  1712-1802  SurfaceFlinger          surfaceflinger                       I  id=283218 createSurf, flag=44000, SurfaceView[com.deace.deacear/com.deace.deacear.MainActivity]@0(BLAST)#283218
2025-05-02 23:52:16.022  1712-1802  SurfaceFlinger          surfaceflinger                       I  id=283219 createSurf, flag=20404, Background for SurfaceView[com.deace.deacear/com.deace.deacear.MainActivity]@0#283219
2025-05-02 23:52:16.022 17736-17736 BufferQueueProducer     com.deace.deacear                    I  [](id:454800000010,api:0,p:-1,c:17736) setDequeueTimeout:2077252342
2025-05-02 23:52:16.022 17736-17736 BLASTBufferQueue_Java   com.deace.deacear                    I  update, w= 1080 h= 2106 mName = null mNativeObject= 0xb400007632ed1f00 sc.mNativeObject= 0xb40000761ee2a440 format= -3 caller= android.view.SurfaceView.createBlastSurfaceControls:1518 android.view.SurfaceView.updateSurface:1194 android.view.SurfaceView.lambda$new$0:258 android.view.SurfaceView.$r8$lambda$cm3nmzErr-srXoT_KjIYQgdhFN0:0 android.view.SurfaceView$$ExternalSyntheticLambda2.onPreDraw:2 android.view.ViewTreeObserver.dispatchOnPreDraw:1204 
2025-05-02 23:52:16.023 17736-17736 SurfaceView@2b5191a     com.deace.deacear                    I  45422874 Cur surface: Surface(name=null)/@0xf848943
2025-05-02 23:52:16.023 17736-17736 SurfaceView@2b5191a     com.deace.deacear                    I  pST: sr = Rect(0, 75 - 1080, 2181) sw = 1080 sh = 2106
2025-05-02 23:52:16.023 17736-17736 SurfaceView@2b5191a     com.deace.deacear                    D  45422874 performSurfaceTransaction RenderWorker position = [0, 75, 1080, 2181] surfaceSize = 1080x2106
2025-05-02 23:52:16.023 17736-17736 SurfaceView@2b5191a     com.deace.deacear                    I  updateSurface: mVisible = true mSurface.isValid() = true
2025-05-02 23:52:16.023 17736-17736 SurfaceView@2b5191a     com.deace.deacear                    I  updateSurface: mSurfaceCreated = false surfaceChanged = true visibleChanged = true
2025-05-02 23:52:16.023 17736-17736 SurfaceView             com.deace.deacear                    I  45422874 visibleChanged -- surfaceCreated
2025-05-02 23:52:16.023 17736-17736 SurfaceView@2b5191a     com.deace.deacear                    I  surfaceCreated 1 #8 android.opengl.GLSurfaceView{2b5191a V.E...... ......ID 0,0-1080,2106 #7f090041 app:id/ar_surface_view}
2025-05-02 23:52:16.023 17736-17736 SurfaceView             com.deace.deacear                    I  45422874 surfaceChanged -- format=-3 w=1080 h=2106
2025-05-02 23:52:16.023 17736-17736 SurfaceView@2b5191a     com.deace.deacear                    I  surfaceChanged (1080,2106) 1 #8 android.opengl.GLSurfaceView{2b5191a V.E...... ......ID 0,0-1080,2106 #7f090041 app:id/ar_surface_view}
2025-05-02 23:52:16.024 17736-17736 SurfaceView             com.deace.deacear                    I  45422874 surfaceRedrawNeeded
2025-05-02 23:52:16.024 17736-20860 GLThread                com.deace.deacear                    W  Warning, !readyToDraw() but waiting for draw finished! Early reporting draw finished.
2025-05-02 23:52:16.024 17736-17736 SurfaceView@2b5191a     com.deace.deacear                    V  Layout: x=0 y=75 w=1080 h=2106, frame=Rect(0, 0 - 1080, 2106)
2025-05-02 23:52:16.024 17736-17736 ViewRootIm...nActivity] com.deace.deacear                    I  Setup new sync=wmsSync-ViewRootImpl@4ea1142[MainActivity]#33
2025-05-02 23:52:16.024 17736-17736 ViewRootIm...nActivity] com.deace.deacear                    I  Creating new active sync group ViewRootImpl@4ea1142[MainActivity]#34
2025-05-02 23:52:16.024 17736-20860 SurfaceView             com.deace.deacear                    I  45422874 finishedDrawing
2025-05-02 23:52:16.024 17736-17736 SurfaceSyncGroup        com.deace.deacear                    I  addLocalSync=ViewRootImpl@4ea1142[MainActivity]#34 to name=wmsSync-ViewRootImpl@4ea1142[MainActivity]#33, callers=android.window.SurfaceSyncGroup.add:431 android.window.SurfaceSyncGroup.add:392 android.window.SurfaceSyncGroup.add:340 android.view.ViewRootImpl.createSyncIfNeeded:4912 android.view.ViewRootImpl.performTraversals:4796 android.view.ViewRootImpl.doTraversal:3345 
2025-05-02 23:52:16.024 17736-17736 ViewRootIm...nActivity] com.deace.deacear                    I  registerCallbacksForSync syncBuffer=false
2025-05-02 23:52:16.026 17736-17771 SurfaceView             com.deace.deacear                    D  45422874 updateSurfacePosition RenderWorker, frameNr = 1, position = [0, 75, 1080, 2181] surfaceSize = 1080x2106
2025-05-02 23:52:16.026 17736-17771 SurfaceView@2b5191a     com.deace.deacear                    I  uSP: rtp = Rect(0, 75 - 1080, 2181) rtsw = 1080 rtsh = 2106
2025-05-02 23:52:16.026 17736-17771 SurfaceView@2b5191a     com.deace.deacear                    I  onSSPAndSRT: pl = 0 pt = 75 sx = 1.0 sy = 1.0
2025-05-02 23:52:16.026  1712-1712  Layer                   surfaceflinger                       I  Layer [Bounds for - com.deace.deacear/com.deace.deacear.MainActivity@0#283216] hidden!! flag(0)
2025-05-02 23:52:16.026 17736-17771 SurfaceView@2b5191a     com.deace.deacear                    I  aOrMT: ViewRootImpl@4ea1142[MainActivity] t = android.view.SurfaceControl$Transaction@a3d683e fN = 1 android.view.SurfaceView.-$$Nest$mapplyOrMergeTransaction:0 android.view.SurfaceView$SurfaceViewPositionUpdateListener.positionChanged:1667 android.graphics.RenderNode$CompositePositionUpdateListener.positionChanged:369 
2025-05-02 23:52:16.026 17736-17771 ViewRootIm...nActivity] com.deace.deacear                    I  mWNT: t=0xb400007632f4dc80 mBlastBufferQueue=0xb400007632ed1600 fn= 1 mRenderHdrSdrRatio=1.0 caller= android.view.SurfaceView.applyOrMergeTransaction:1599 android.view.SurfaceView.-$$Nest$mapplyOrMergeTransaction:0 android.view.SurfaceView$SurfaceViewPositionUpdateListener.positionChanged:1667 
2025-05-02 23:52:16.026 17736-17893 ViewRootIm...nActivity] com.deace.deacear                    I  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-05-02 23:52:16.026 17736-17893 ViewRootIm...nActivity] com.deace.deacear                    I  mWNT: t=0xb400007632f4e280 mBlastBufferQueue=0xb400007632ed1600 fn= 1 mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$8.onFrameDraw:13946 android.view.ThreadedRenderer$1.onFrameDraw:792 <bottom of call stack> 
2025-05-02 23:52:16.026 17736-17893 ViewRootIm...nActivity] com.deace.deacear                    I  Setting up sync and frameCommitCallback
2025-05-02 23:52:16.030 17736-17771 BLASTBufferQueue        com.deace.deacear                    I  [ViewRootImpl@4ea1142[MainActivity]#9](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-05-02 23:52:16.031  2786-8004  InputDispatcher         system_server                        D  Focused application(0): ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}
2025-05-02 23:52:16.031 17736-17771 ViewRootIm...nActivity] com.deace.deacear                    I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-05-02 23:52:16.031 17736-17771 SurfaceSyncGroup        com.deace.deacear                    I  onTransactionReady mName=wmsSync-ViewRootImpl@4ea1142[MainActivity]#33 callback=238072057
2025-05-02 23:52:16.031  2786-8004  WindowManager           system_server                        V  Changing focus from null to Window{8e51b7a u0 com.deace.deacear/com.deace.deacear.MainActivity} displayId=0 Callers=com.android.server.wm.RootWindowContainer.updateFocusedWindowLocked:581 com.android.server.wm.WindowManagerService.updateFocusedWindowLocked:6910 com.android.server.wm.ActivityTaskManagerService.setLastResumedActivityUncheckLocked:6164 com.android.server.wm.ActivityTaskSupervisor.updateTopResumedActivityIfNeeded:2894 
2025-05-02 23:52:16.031 17736-17771 OpenGLRenderer          com.deace.deacear                    D  CFMS:: SetUp Pid : 17736    Tid : 17771
2025-05-02 23:52:16.031 17736-17736 ViewRootIm...nActivity] com.deace.deacear                    I  reportDrawFinished seqId=0
2025-05-02 23:52:16.033  2786-8004  InsetsSourceProvider    system_server                        D  updateControlForTarget: control=InsetsSourceControl: {58b90001 mType=navigationBars initiallyVisible mSurfacePosition=Point(0, 2181) mInsetsHint=Insets{left=0, top=0, right=0, bottom=135}}, target=Window{8e51b7a u0 com.deace.deacear/com.deace.deacear.MainActivity}, from=com.android.server.wm.InsetsStateController.onControlTargetChanged:363 com.android.server.wm.InsetsStateController.onBarControlTargetChanged:332 com.android.server.wm.InsetsPolicy.updateBarControlTarget:173 com.android.server.wm.InsetsPolicy.updateSystemBars:843 com.android.server.wm.DisplayPolicy.updateSystemBarsLw:3484 
2025-05-02 23:52:16.034  2786-8004  InsetsSourceProvider    system_server                        D  updateControlForTarget: control=InsetsSourceControl: {d8380000 mType=statusBars initiallyVisible mSurfacePosition=Point(0, 0) mInsetsHint=Insets{left=0, top=75, right=0, bottom=0}}, target=Window{8e51b7a u0 com.deace.deacear/com.deace.deacear.MainActivity}, from=com.android.server.wm.InsetsStateController.onControlTargetChanged:363 com.android.server.wm.InsetsStateController.onBarControlTargetChanged:329 com.android.server.wm.InsetsPolicy.updateBarControlTarget:173 com.android.server.wm.InsetsPolicy.updateSystemBars:843 com.android.server.wm.DisplayPolicy.updateSystemBarsLw:3484 
2025-05-02 23:52:16.034  2786-8004  WindowManager           system_server                        D  updateSystemBarAttributes: displayId=0, win=Window{8e51b7a u0 com.deace.deacear/com.deace.deacear.MainActivity}, navColorWin=Window{5c08345 u0 Splash Screen com.deace.deacear}, focusedCanBeNavColorWin=false, behavior=1, appearance=16, statusBarAppearanceRegions=[AppearanceRegion{LIGHT_STATUS_BARS bounds=[0,0][1080,2316]}], requestedVisibilities=-9, from=com.android.server.wm.DisplayPolicy.focusChangedLw:2988 com.android.server.wm.DisplayContent.updateFocusedWindowLocked:4900 com.android.server.wm.RootWindowContainer.updateFocusedWindowLocked:581 
2025-05-02 23:52:16.034  2786-8004  SystemKeyManager        system_server                        V  updateFocusedWindow() is called, com.deace.deacear/com.deace.deacear.MainActivity
2025-05-02 23:52:16.034  2786-8004  SystemKeyManager        system_server                        I  requested systemKeyInfo size=1 focusedWindow=com.deace.deacear/com.deace.deacear.MainActivity
2025-05-02 23:52:16.034  2786-8004  SystemKeyManager        system_server                        I  requested systemKeyInfo size=3 focusedWindow=com.deace.deacear/com.deace.deacear.MainActivity
2025-05-02 23:52:16.034  2786-8004  ActivityTaskManager     system_server                        D  scheduleTopResumedActivityChanged, onTop=true, r=ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}, caller=com.android.server.wm.ActivityTaskSupervisor.scheduleTopResumedActivityStateIfNeeded:2904 com.android.server.wm.ActivityTaskSupervisor.updateTopResumedActivityIfNeeded:2896 com.android.server.wm.TaskFragment.setResumedActivity:580 com.android.server.wm.TaskFragment.onActivityStateChanged:904 com.android.server.wm.ActivityRecord.setState:6892 com.android.server.wm.TaskFragment.resumeTopActivity:1756 
2025-05-02 23:52:16.034  1712-1712  Layer                   surfaceflinger                       I  Layer [SurfaceView[com.deace.deacear/com.deace.deacear.MainActivity]@0#283217] hidden!! flag(0)
2025-05-02 23:52:16.034 17736-17736 GridPanel               com.deace.deacear                    D  Updating fix button state: isFixed = false
2025-05-02 23:52:16.034  2786-8004  WindowManager           system_server                        D  rotationForOrientation, orientationSource=ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}
2025-05-02 23:52:16.034  4359-4359  NavigationBar           com.android.systemui                 D  onSystemBarAttributesChanged() -  displayId:0, appearance:16, packageName: com.deace.deacear (APPEARANCE_LIGHT_NAVIGATION_BARS ), navbarColorManagedByIme:false
2025-05-02 23:52:16.035 17736-17736 GridPanel               com.deace.deacear                    D  Synced UI: isFixed=false, opacityProgress=50
2025-05-02 23:52:16.035 17736-17736 ImagePanel              com.deace.deacear                    W  syncUiStateFromManager called before views were initialized
2025-05-02 23:52:16.036  2786-2857  WindowManager           system_server                        V  Sent Transition #15879 createdAt=05-02 23:52:15.872 via request=TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=32676 displayId=0 isRunning=true baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.deace.deacear/.MainActivity } baseActivity=ComponentInfo{com.deace.deacear/com.deace.deacear.MainActivity} topActivity=ComponentInfo{com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity} origActivity=null realActivity=ComponentInfo{com.deace.deacear/com.deace.deacear.MainActivity} numActivities=2 lastActiveTime=********** supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 maxWidth=-1 maxHeight=-1 defaultMinSize=220 token=WCT{RemoteToken{54ef884 Task{afd1290 #32676 type=standard A=10553:com.deace.deacear}}} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 75 - 0, 0) topActivityInfo=ActivityInfo{a76d001 com.android.permissioncontroller.permission.ui.GrantPermissionsActivity} launchCookies=[android.os.BinderProxy@e476ca2] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true isSleeping=false topActivityInSizeCompat=false topActivityEligibleForLetterboxEducation= false topActivityLetterboxed= false isFromDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 locusId=null displayAreaFeatureId=1 cameraCompatControlState=hidden originallySupportedMultiWindow=true hasWallpaper=false topActivityInFixedAspectRatio=false rootAffinity=10553:com.deace.deacear topActivityInDisplayCompat=false topActivityInBoundsCompat=false topActivityBounds=null singleTapFromLetterbox=false isTopTaskInStage=false topActivityUiMode=33 CoverLauncherWidgetTask=false CoverScreenTask=false isAllowedSeamlessRotation=false isTopTransparentActivity=false hasPopOver=false}, remoteTransition = null, displayChange = null }
2025-05-02 23:52:16.037  4359-4441  InsetsController        com.android.systemui                 I  onStateChanged: host=Splash Screen com.deace.deacear, from=android.view.ViewRootImpl$ViewRootHandler.handleMessageImpl:7279, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2316), mDisplayCutout=DisplayCutout{insets=Rect(0, 75 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(513, 0 - 567, 75), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2316 physicalDisplayWidth=1080 physicalDisplayHeight=2316 density={2.8125} cutoutSpec={M 0,0 H -9.466666666666667 V 26.66666666666667‬ H 9.466666666666667 V 0 H 0 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=8, center=Point(8, 8)}, RoundedCorner{position=TopRight, radius=8, center=Point(1072, 8)}, RoundedCorner{position=BottomRight, radius=8, center=Point(1072, 2308)}, RoundedCorner{position=BottomLeft, radius=8, center=Point(8, 2308)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2316), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(956, 0 - 1080, 75) rotation=0}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1440 displayHeight=3088 physicalPixelDisplaySizeRatio=1.0 rotation=0 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {d8380000 mType=statusBars mFrame=[0,0][1080,75] mVisible=true mFlags=[]}, InsetsSource: {d8380005 mType=mandatorySystemGestures mFrame=[0,0][1080,109] mVisible=true mFlags=[]}, InsetsSource: {d8380006 mType=tappableElement mFrame=[0,0][1080,75] mVisible=true mFlags=[]}, InsetsSource: {27 mType=displayCutout mFrame=[0,0][1080,75] mVisible=true mFlags=[]}, InsetsSource: {58b90001 mType=navigationBars mFrame=[0,2181][1080,2316] mVisible=true mFlags=[]}, InsetsSource: {58b90004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags=[]}, InsetsSource: {58b90005 mType=mandatorySystemGestures mFrame=[0,2181][1080,2316] mVisible=true mFlags=[]}, InsetsSource: {58b90006 mType=tappableElement mFrame=[0,2181][1080,2316] mVisible=true mFlags=[]}, InsetsSource: {58b90024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags=[]} }
2025-05-02 23:52:16.037  2786-5397  WindowManager           system_server                        D  finishDrawingWindow: Window{8e51b7a u0 com.deace.deacear/com.deace.deacear.MainActivity} mDrawState=DRAW_PENDING seqId=0
2025-05-02 23:52:16.039  2786-2861  WindowManager           system_server                        V  Finish starting ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676}: first real window is shown, no animation
2025-05-02 23:52:16.039  2786-2861  WindowManager           system_server                        V  Schedule remove starting ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676} startingWindow=Window{5c08345 u0 Splash Screen com.deace.deacear} animate=true Callers=com.android.server.wm.ActivityRecord.removeStartingWindow:3496 com.android.server.wm.ActivityRecord.onFirstWindowDrawn:8007 com.android.server.wm.WindowState.performShowLocked:5350 com.android.server.wm.WindowStateAnimator.commitFinishDrawingLocked:286 com.android.server.wm.DisplayContent.lambda$new$8:1293 
2025-05-02 23:52:16.039  2786-2861  TaskOrganizerController system_server                        D  applyStartingWindowAnimation, window=Window{8e51b7a u0 com.deace.deacear/com.deace.deacear.MainActivity}, caller=com.android.server.wm.TaskOrganizerController.removeStartingWindow:809 com.android.server.wm.StartingSurfaceController$StartingSurface.remove:292 com.android.server.wm.ActivityRecord.removeStartingWindowAnimation:3576 
2025-05-02 23:52:16.039  2786-2861  WindowManager           system_server                        I  Reparenting to leash, surface=Surface(name=8e51b7a com.deace.deacear/com.deace.deacear.MainActivity)/@0xe42f494, leashParent=Surface(name=ActivityRecord{c6a2953 u0 com.deace.deacear/.MainActivity t32676})/@0xeb42a31
2025-05-02 23:52:16.039  1712-3722  SurfaceFlinger          surfaceflinger                       I  id=283222 createSurf, flag=24000, Surface(name=8e51b7a com.deace.deacear/com.deace.deacear.MainActivity)/@0xe42f494 - animation-leash of starting_reveal#283222
2025-05-02 23:52:16.040  2786-2861  WindowManager           system_server                        D  makeSurface duration=0 leash=Surface(name=Surface(name=8e51b7a com.deace.deacear/com.deace.deacear.MainActivity)/@0xe42f494 - animation-leash of starting_reveal)/@0xd8d4c7e
2025-05-02 23:52:16.041  2786-2861  WindowManager           system_server                        V  performShowLocked: mDrawState=HAS_DRAWN in Window{8e51b7a u0 com.deace.deacear/com.deace.deacear.MainActivity}
2025-05-02 23:52:16.041  2786-2860  GameSDK@LifeCycle       system_server                        I  noteResumeComponent(): package name  : com.deace.deacear
2025-05-02 23:52:16.042 22827-22827 Edge.ActivityUtils      com.sec.android.app.launcher         I  HomePackage : com.sec.android.app.launcher, resumePackageName : com.deace.deacear
2025-05-02 23:52:16.043  2786-2860  SGM:GameManager         system_server                        D  identifyGamePackage. com.deace.deacear, mCurrentUserId: 0, callerUserId: 0, callingMethodInfo: com.samsung.android.game.SemGameManager.isGamePackage(SemGameManager.java:111)
2025-05-02 23:52:16.043  2786-2860  SGM:PkgDataHelper       system_server                        D  getGamePkgData(). com.deace.deacear
2025-05-02 23:52:16.043  2786-2860  SGM:SemGameManager      system_server                        D  isGamePackage(), pkgName=com.deace.deacear, ret=false
2025-05-02 23:52:16.043  1712-1712  Layer                   surfaceflinger                       I  Layer::reparent [8e51b7a com.deace.deacear/com.deace.deacear.MainActivity#283214] newParent : Surface(name=8e51b7a com.deace.deacear/com.deace.deacear.MainActivity)/@0xe42f494 - animation-leash of starting_reveal#283222 , isRemovedFromCurrentState : 0 -------------------------
2025-05-02 23:52:16.043  2786-2860  SGM:FgCheckThread       system_server                        D  noteResumeComponent(), received pkgName: com.deace.deacear, userId: 0
2025-05-02 23:52:16.043  1712-1712  Layer                   surfaceflinger                       I  Layer::reparent [8e51b7a com.deace.deacear/com.deace.deacear.MainActivity#283214] End -------------------------
2025-05-02 23:52:16.043  1712-1712  Layer                   surfaceflinger                       I  Layer [com.deace.deacear/com.deace.deacear.MainActivity$_17736#283215] hidden!! flag(0)
2025-05-02 23:52:16.043  2786-3726  SGM:FgCheckThread       system_server                        D  onLooperPrepared(), msg: MSG_APP_RESUME, pkgName: com.deace.deacear, userid: 0
2025-05-02 23:52:16.043  2786-3726  SGM:FgCheckThread       system_server                        D    handleResume(). pkgName: com.deace.deacear, userId: 0, isTunableApp: null
2025-05-02 23:52:16.043  2786-3726  SGM:FgCheckThread       system_server                        D  notifyFocusInOut(). of pkg: com.deace.deacear, type: 4, isTunableApp: false, userId: 0
2025-05-02 23:52:16.044  2786-8004  WindowManager           system_server                        V  Relayout Window{5c08345 u0 Splash Screen com.deace.deacear}: viewVisibility=0 req=1080x2316 ty=3 d0
2025-05-02 23:52:16.044  2786-8004  WindowManager           system_server                        D  updateSystemBarAttributes: displayId=0, win=Window{8e51b7a u0 com.deace.deacear/com.deace.deacear.MainActivity}, navColorWin=Window{5c08345 u0 Splash Screen com.deace.deacear}, focusedCanBeNavColorWin=false, behavior=1, appearance=0, statusBarAppearanceRegions=[AppearanceRegion{ bounds=[0,0][1080,2316]}], requestedVisibilities=-9, from=com.android.server.wm.DisplayPolicy.finishPostLayoutPolicyLw:2339 com.android.server.wm.DisplayContent.applySurfaceChangesTransaction:6181 com.android.server.wm.RootWindowContainer.applySurfaceChangesTransaction:1183 
2025-05-02 23:52:16.044  4359-4359  SamsungLig...trolHelper com.android.systemui                 D  onStatusBarAppearanceChanged() -  sbModeChanged:false, statusBarMode:0, barState:MODE_TRANSPARENT, isKeyguardShowing:false, navbarColorManagedByIme:false, stackAppearanceChanged:true, (AppearanceRegion{ bounds=[0,0][1080,2316]}, ), packageName:com.deace.deacear
2025-05-02 23:52:16.044  4359-4359  NavigationBar           com.android.systemui                 D  onSystemBarAttributesChanged() -  displayId:0, appearance:0, packageName: com.deace.deacear, navbarColorManagedByIme:false
2025-05-02 23:52:16.045  4359-4359  SamsungLig...trolHelper com.android.systemui                 D  updateNavigationBar (WHITE button) isLightOpaque:false, hasLightNavigationBarFlag:false, packageName:com.deace.deacear, DirectReplying:false, NavBarColorMangedByIme:false, ForceDarkForScrim:false, QsCustomizing:false, PanelHasWhiteBg:false
2025-05-02 23:52:16.045 17736-17736 MainActivity            com.deace.deacear                    W  Camera permission denied.
2025-05-02 23:52:16.045  4359-4359  SamsungLig...trolHelper com.android.systemui                 D  updateStatusBar (WHITE icon) numStacks:1, StatusBarMode:MODE_TRANSPARENT, lightBarBounds:[], StatusBarState:SHADE, packageName:com.deace.deacear, isWhiteKeyguardStatusBar:false
2025-05-02 23:52:16.045  2786-8039  InputDispatcher         system_server                        D  Focus request (0): 8e51b7a com.deace.deacear/com.deace.deacear.MainActivity but waiting because NOT_VISIBLE
2025-05-02 23:52:16.045 17736-17736 Dialog                  com.deace.deacear                    I  mIsDeviceDefault = false, mIsSamsungBasicInteraction = false, isMetaDataInActivity = false
2025-05-02 23:52:16.046  4359-4441  BLASTBufferQueue_Java   com.android.systemui                 I  update, w= 1080 h= 2316 mName = ViewRootImpl@29b3edd[deacear] mNativeObject= 0xb40000761a070a00 sc.mNativeObject= 0xb40000761343a6c0 format= -3 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3074 android.view.ViewRootImpl.relayoutWindow:10224 android.view.ViewRootImpl.performTraversals:4167 android.view.ViewRootImpl.doTraversal:3345 android.view.ViewRootImpl$TraversalRunnable.run:11437 android.view.Choreographer$CallbackRecord.run:1690 
2025-05-02 23:52:16.046  2786-5397  InputDispatcher         system_server                        D  Focus entered window (0): 8e51b7a com.deace.deacear/com.deace.deacear.MainActivity
2025-05-02 23:52:16.046  4359-4441  ViewRootIm...d[deacear] com.android.systemui                 I  Relayout returned: old=(0,0,1080,2316) new=(0,0,1080,2316) relayoutAsync=false req=(1080,2316)0 dur=3 res=0x400 s={true 0xb400007613aaf800} ch=false seqId=0
2025-05-02 23:52:16.046  4359-4441  ViewRootIm...d[deacear] com.android.systemui                 I  registerCallbackForPendingTransactions
2025-05-02 23:52:16.047  4359-5763  ViewRootIm...d[deacear] com.android.systemui                 I  mWNT: t=0xb4000075fccd3380 mBlastBufferQueue=0xb40000761a070a00 fn= 2 mRenderHdrSdrRatio=1.0 caller= android.view.SyncRtSurfaceTransactionApplier.applyTransaction:96 android.view.SyncRtSurfaceTransactionApplier.lambda$scheduleApply$0:69 android.view.SyncRtSurfaceTransactionApplier.$r8$lambda$SgowXC58rj3PR958kHUfRgLZmvE:0 
2025-05-02 23:52:16.047  4359-5763  ViewRootIm...d[deacear] com.android.systemui                 I  mWNT: t=0xb4000075fccd3500 mBlastBufferQueue=0xb40000761a070a00 fn= 2 mRenderHdrSdrRatio=1.0 caller= android.view.SyncRtSurfaceTransactionApplier.applyTransaction:96 android.view.SyncRtSurfaceTransactionApplier.lambda$scheduleApply$0:69 android.view.SyncRtSurfaceTransactionApplier.$r8$lambda$SgowXC58rj3PR958kHUfRgLZmvE:0 
2025-05-02 23:52:16.047  4359-5763  ViewRootIm...d[deacear] com.android.systemui                 I  mWNT: t=0xb4000075fccd3680 mBlastBufferQueue=0xb40000761a070a00 fn= 2 mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$6.onFrameDraw:5705 android.view.ViewRootImpl$2.onFrameDraw:2190 android.view.ThreadedRenderer$1.onFrameDraw:792 
2025-05-02 23:52:16.047  1712-1712  SurfaceFlinger          surfaceflinger                       D  Display 4630947093241269891 HWC layers:
                                                                                                         DEVICE | 0xb40000732b21bf00 | 0100 | RGBA_8888    |    0.0    0.0 1080.0 2316.0 |    0    0 1080 2316 | com.deace.deacear/com.deace.deacear.MainActivity$_17736#283215
                                                                                                         DEVICE | 0xb40000732b21bd80 | 0100 | RGBA_8888    |    0.0    0.0 1080.0 2316.0 |    0    0 1080 2316 | Splash Screen com.deace.deacear$_4359#283205
                                                                                                         DEVICE | 0xb40000730a7ac000 | 0100 | RGBA_8888    |    0.0    0.0 1080.0   75.0 |    0    0 1080   75 | StatusBar$_4359#112
                                                                                                         DEVICE | 0xb40000732b2cab80 | 0100 | RGBA_8888    |    0.0    0.0   67.0  342.0 | 1013  698 1080 1040 | com.sec.android.app.launcher/com.sam[...]dge.CocktailBarService$_22827#283190
                                                                                                         DEVICE | 0xb40000732b2ca680 | 0100 | RGBA_8888    |    0.0    0.0 1080.0  135.0 |    0 2181 1080 2316 | NavigationBar0$_4359#103
2025-05-02 23:52:16.050 17736-17736 DecorView               com.deace.deacear                    I  setWindowBackground: isPopOver=false color=fff6f6f8 d=android.graphics.drawable.InsetDrawable@bc8546d
2025-05-02 23:52:16.051  4359-5762  ViewRootIm...d[deacear] com.android.systemui                 I  mWNT: t=0xb4000075fcd04680 mBlastBufferQueue=0xb40000761a070a00 fn= 3 mRenderHdrSdrRatio=1.0 caller= android.view.SyncRtSurfaceTransactionApplier.applyTransaction:96 android.view.SyncRtSurfaceTransactionApplier.lambda$scheduleApply$0:69 android.view.SyncRtSurfaceTransactionApplier.$r8$lambda$SgowXC58rj3PR958kHUfRgLZmvE:0 
2025-05-02 23:52:16.060  4359-5763  ViewRootIm...d[deacear] com.android.systemui                 I  mWNT: t=0xb4000075fcd07380 mBlastBufferQueue=0xb40000761a070a00 fn= 4 mRenderHdrSdrRatio=1.0 caller= android.view.SyncRtSurfaceTransactionApplier.applyTransaction:96 android.view.SyncRtSurfaceTransactionApplier.lambda$scheduleApply$0:69 android.view.SyncRtSurfaceTransactionApplier.$r8$lambda$SgowXC58rj3PR958kHUfRgLZmvE:0 
2025-05-02 23:52:16.066 17736-17736 ScrollView              com.deace.deacear                    D  initGoToTop
2025-05-02 23:52:16.068  4359-5762  ViewRootIm...d[deacear] com.android.systemui                 I  mWNT: t=0xb4000075fcd25780 mBlastBufferQueue=0xb40000761a070a00 fn= 5 mRenderHdrSdrRatio=1.0 caller= android.view.SyncRtSurfaceTransactionApplier.applyTransaction:96 android.view.SyncRtSurfaceTransactionApplier.lambda$scheduleApply$0:69 android.view.SyncRtSurfaceTransactionApplier.$r8$lambda$SgowXC58rj3PR958kHUfRgLZmvE:0 
2025-05-02 23:52:16.073 17736-17736 WindowManager           com.deace.deacear                    I  WindowManagerGlobal#addView, ty=2, view=DecorView@119b07f[MainActivity], caller=android.view.WindowManagerImpl.addView:150 android.app.Dialog.show:506 androidx.appcompat.app.AlertDialog$Builder.show:1008 
2025-05-02 23:52:16.075 17736-17771 NativeCust...ncyManager com.deace.deacear                    D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-05-02 23:52:16.076  4359-5763  ViewRootIm...d[deacear] com.android.systemui                 I  mWNT: t=0xb4000075fcd27b80 mBlastBufferQueue=0xb40000761a070a00 fn= 6 mRenderHdrSdrRatio=1.0 caller= android.view.SyncRtSurfaceTransactionApplier.applyTransaction:96 android.view.SyncRtSurfaceTransactionApplier.lambda$scheduleApply$0:69 android.view.SyncRtSurfaceTransactionApplier.$r8$lambda$SgowXC58rj3PR958kHUfRgLZmvE:0 
2025-05-02 23:52:16.078  1712-3722  SurfaceFlinger          surfaceflinger                       I  id=283223 createSurf, flag=84004, b1740fb com.deace.deacear/com.deace.deacear.MainActivity#283223
2025-05-02 23:52:16.079  2786-8004  WindowManager           system_server                        V  Changing focus from Window{8e51b7a u0 com.deace.deacear/com.deace.deacear.MainActivity} to Window{b1740fb u0 com.deace.deacear/com.deace.deacear.MainActivity} displayId=0 Callers=com.android.server.wm.RootWindowContainer.updateFocusedWindowLocked:581 com.android.server.wm.WindowManagerService.updateFocusedWindowLocked:6910 com.android.server.wm.WindowManagerService.addWindow:1958 com.android.server.wm.Session.addToDisplayAsUser:240 
2025-05-02 23:52:16.080  2786-8004  InsetsSourceProvider    system_server                        D  updateControlForTarget: control=InsetsSourceControl: {58b90001 mType=navigationBars initiallyVisible mSurfacePosition=Point(0, 2181) mInsetsHint=Insets{left=0, top=0, right=0, bottom=135}}, target=Window{b1740fb u0 com.deace.deacear/com.deace.deacear.MainActivity}, from=com.android.server.wm.InsetsStateController.onControlTargetChanged:363 com.android.server.wm.InsetsStateController.onBarControlTargetChanged:332 com.android.server.wm.InsetsPolicy.updateBarControlTarget:173 com.android.server.wm.InsetsPolicy.updateSystemBars:843 com.android.server.wm.DisplayPolicy.updateSystemBarsLw:3484 
2025-05-02 23:52:16.080  2786-8004  InsetsSourceProvider    system_server                        D  updateControlForTarget: control=InsetsSourceControl: {d8380000 mType=statusBars initiallyVisible mSurfacePosition=Point(0, 0) mInsetsHint=Insets{left=0, top=75, right=0, bottom=0}}, target=Window{b1740fb u0 com.deace.deacear/com.deace.deacear.MainActivity}, from=com.android.server.wm.InsetsStateController.onControlTargetChanged:363 com.android.server.wm.InsetsStateController.onBarControlTargetChanged:329 com.android.server.wm.InsetsPolicy.updateBarControlTarget:173 com.android.server.wm.InsetsPolicy.updateSystemBars:843 com.android.server.wm.DisplayPolicy.updateSystemBarsLw:3484 
2025-05-02 23:52:16.081  2786-8004  SystemKeyManager        system_server                        V  updateFocusedWindow() is called, com.deace.deacear/com.deace.deacear.MainActivity
2025-05-02 23:52:16.081  2786-8004  SystemKeyManager        system_server                        I  requested systemKeyInfo size=1 focusedWindow=com.deace.deacear/com.deace.deacear.MainActivity
2025-05-02 23:52:16.081  2786-8004  SystemKeyManager        system_server                        I  requested systemKeyInfo size=3 focusedWindow=com.deace.deacear/com.deace.deacear.MainActivity
2025-05-02 23:52:16.082 17736-17736 InsetsController        com.deace.deacear                    I  onStateChanged: host=com.deace.deacear/com.deace.deacear.MainActivity, from=android.view.ViewRootImpl.setView:1797, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2316), mDisplayCutout=DisplayCutout{insets=Rect(0, 75 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(513, 0 - 567, 75), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2316 physicalDisplayWidth=1080 physicalDisplayHeight=2316 density={2.8125} cutoutSpec={M 0,0 H -9.466666666666667 V 26.66666666666667‬ H 9.466666666666667 V 0 H 0 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=8, center=Point(8, 8)}, RoundedCorner{position=TopRight, radius=8, center=Point(1072, 8)}, RoundedCorner{position=BottomRight, radius=8, center=Point(1072, 2308)}, RoundedCorner{position=BottomLeft, radius=8, center=Point(8, 2308)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2316), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(956, 0 - 1080, 75) rotation=0}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1440 displayHeight=3088 physicalPixelDisplaySizeRatio=1.0 rotation=0 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {d8380000 mType=statusBars mFrame=[0,0][1080,75] mVisible=true mFlags=[]}, InsetsSource: {d8380005 mType=mandatorySystemGestures mFrame=[0,0][1080,109] mVisible=true mFlags=[]}, InsetsSource: {d8380006 mType=tappableElement mFrame=[0,0][1080,75] mVisible=true mFlags=[]}, InsetsSource: {27 mType=displayCutout mFrame=[0,0][1080,75] mVisible=true mFlags=[]}, InsetsSource: {58b90001 mType=navigationBars mFrame=[0,2181][1080,2316] mVisible=true mFlags=[]}, InsetsSource: {58b90004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags=[]}, InsetsSource: {58b90005 mType=mandatorySystemGestures mFrame=[0,2181][1080,2316] mVisible=true mFlags=[]}, InsetsSource: {58b90006 mType=tappableElement mFrame=[0,2181][1080,2316] mVisible=true mFlags=[]}, InsetsSource: {58b90024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags=[]} }
2025-05-02 23:52:16.083 17736-17736 ViewRootIm...nActivity] com.deace.deacear                    I  synced displayState. AttachInfo displayState=2
2025-05-02 23:52:16.083 17736-17736 ViewRootIm...nActivity] com.deace.deacear                    I  setView = com.android.internal.policy.DecorView@119b07f TM=true
2025-05-02 23:52:16.084 17736-17736 MainActivity            com.deace.deacear                    D  onResume.
2025-05-02 23:52:16.084 17736-17736 MainActivity            com.deace.deacear                    D  Starting resumeARSession - attempt 0
2025-05-02 23:52:16.084 17736-17736 ARSessionManager        com.deace.deacear                    D  Session paused state recorded
2025-05-02 23:52:16.085  4359-5762  ViewRootIm...d[deacear] com.android.systemui                 I  mWNT: t=0xb4000075fcd4b780 mBlastBufferQueue=0xb40000761a070a00 fn= 7 mRenderHdrSdrRatio=1.0 caller= android.view.SyncRtSurfaceTransactionApplier.applyTransaction:96 android.view.SyncRtSurfaceTransactionApplier.lambda$scheduleApply$0:69 android.view.SyncRtSurfaceTransactionApplier.$r8$lambda$SgowXC58rj3PR958kHUfRgLZmvE:0 
2025-05-02 23:52:16.085 17736-17736 MainActivity            com.deace.deacear                    D  GLSurfaceView resumed
2025-05-02 23:52:16.086 17736-20860 ARRenderer              com.deace.deacear                    D  Initializing GL resources for AR objects
2025-05-02 23:52:16.087 17736-20860 ARPlaneVisualizer       com.deace.deacear                    D  GL resources created successfully
2025-05-02 23:52:16.087 17736-20860 ARReticle               com.deace.deacear                    D  GL resources created successfully
2025-05-02 23:52:16.087 17736-20860 ARRenderer              com.deace.deacear                    D  OpenGL resources initialized successfully
2025-05-02 23:52:16.087 17736-20860 MainActivity            com.deace.deacear                    D  Session is paused during render cycle
2025-05-02 23:52:16.089 17736-20860 native                  com.deace.deacear                    E  E0000 00:00:1746222736.089010   20860 error_policy_util.cc:263] 
                                                                                                    ################ ARCore Native Error ##################
                                                                                                    BUILD_CHANGELIST:724143902
                                                                                                    BUILD_BASELINE_CHANGELIST:722469352
                                                                                                    ################### Stack Trace Begin ################
                                                                                                    ARCoreError: third_party/arcore/ar/core/session.cc:1913	https://cs.corp.google.com/piper///depot/google3/third_party/arcore/ar/core/session.cc?g=0&l=1913
                                                                                                    ARCoreError: third_party/arcore/ar/core/c_api/session_lite_c_api.cc:76	https://cs.corp.google.com/piper///depot/google3/third_party/arcore/ar/core/c_api/session_lite_c_api.cc?g=0&l=76
                                                                                                    ################### Stack Trace End #################
                                                                                                    
                                                                                                    ################### Undecorated Trace Begin  #################
                                                                                                    FAILED_PRECONDITION: 
                                                                                                    ARCoreError: third_party/arcore/ar/core/session.cc:1913
                                                                                                     at third_party/arcore/ar/core/session.cc:1915Cannot update frame, session is paused. [type.googleapis.com/util.ErrorSpacePayload='ArStatusErrorSpace::AR_ERROR_SESSION_PAUSED']
                                                                                                    === Source Location Trace: === 
                                                                                                    third_party/arcore/ar/core/status.cc:165
                                                                                                    third_party/arcore/ar/core/session.cc:1913
                                                                                                    
                                                                                                    ################### Undecorated Trace End  #################
2025-05-02 23:52:16.089 17736-20860 ARRenderer              com.deace.deacear                    D  Camera not ready despite session appearing active