#Sun May 25 22:44:32 CEST 2025
base.0=C\:\\_DEV\\DeaceAR\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=C\:\\_DEV\\DeaceAR\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.2=C\:\\_DEV\\DeaceAR\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\11\\classes.dex
base.3=C\:\\_DEV\\DeaceAR\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\13\\classes.dex
base.4=C\:\\_DEV\\DeaceAR\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\15\\classes.dex
base.5=C\:\\_DEV\\DeaceAR\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\2\\classes.dex
base.6=C\:\\_DEV\\DeaceAR\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\3\\classes.dex
base.7=C\:\\_DEV\\DeaceAR\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
base.8=C\:\\_DEV\\DeaceAR\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes3.dex
path.0=classes.dex
path.1=0/classes.dex
path.2=11/classes.dex
path.3=13/classes.dex
path.4=15/classes.dex
path.5=2/classes.dex
path.6=3/classes.dex
path.7=classes2.dex
path.8=classes3.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
renamed.8=classes9.dex
