// Generated by view binder compiler. Do not edit!
package com.deace.deacear.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.deace.deacear.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogTrackingHelpBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnCloseTrackingHelp;

  private DialogTrackingHelpBinding(@NonNull LinearLayout rootView,
      @NonNull Button btnCloseTrackingHelp) {
    this.rootView = rootView;
    this.btnCloseTrackingHelp = btnCloseTrackingHelp;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogTrackingHelpBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogTrackingHelpBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_tracking_help, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogTrackingHelpBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_close_tracking_help;
      Button btnCloseTrackingHelp = ViewBindings.findChildViewById(rootView, id);
      if (btnCloseTrackingHelp == null) {
        break missingId;
      }

      return new DialogTrackingHelpBinding((LinearLayout) rootView, btnCloseTrackingHelp);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
