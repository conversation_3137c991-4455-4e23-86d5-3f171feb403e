Key Features of ARImage.kt

    Texture Loading:

        Loads a Bitmap and creates an OpenGL texture from it

        Handles texture coordinates and rendering

    Aspect Ratio Handling:

        Automatically adjusts the rendered image to maintain its original aspect ratio

        Can scale uniformly or non-uniformly based on user preference

    Transformation Support:

        Translation (moving the image on the plane)

        Scaling (with aspect ratio preservation option)

        Rotation (around the Z-axis)

    Opacity Control:

        Supports adjustable opacity with a uniform shader variable

        Defaults to 50% opacity as specified in requirements

    Rendering:

        Uses a simple vertex and fragment shader for texture mapping

        Properly blends with the AR environment using alpha blending

    Memory Management:

        Includes a release() method to clean up OpenGL resources

        Deletes the texture when no longer needed

Integration Points

    With ARSessionManager:

        The ARSessionManager creates and manages ARImage instances

        Handles placement and anchoring of images in AR space

    With ARRenderer:

        <PERSON><PERSON><PERSON><PERSON> calls the draw() method with proper projection and view matrices

        Manages the rendering order (images should be drawn after grids)

    With ImagePanel:

        UI controls in ImagePanel affect the ARImage properties (opacity, position, etc.)

        User interactions are translated into ARImage transformations

Usage Example

Here's how you would typically use this class from ARSessionManager:
kotlin

```kotlin
// When loading an image
val bitmap = loadImageFromUri(uri)
currentImage = ARImage(context, bitmap).apply {
    setOpacity(settingsManager.defaultImageOpacity)
    updateModelMatrix(hitResult.hitPose)
}
arRenderer.setImage(currentImage!!)

// When updating opacity from UI
currentImage?.setOpacity(newOpacity)

// When moving the image
currentImage?.translate(deltaX, deltaY)

// When scaling the image
currentImage?.scale(scaleFactor, preserveAspectRatio)

```
This implementation provides all the functionality needed for the image projection aspect of your Graffiti AR Projection Assistant, following the specifications you provided.