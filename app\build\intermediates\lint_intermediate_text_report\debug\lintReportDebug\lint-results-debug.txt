C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\SplashActivity.kt:9: Warning: The application should not provide its own launch screen [CustomSplashScreen]
class SplashActivity : AppCompatActivity() {
      ~~~~~~~~~~~~~~

   Explanation for issues of type "CustomSplashScreen":
   Starting in Android 12 (API 31+), the application's Launch Screen is
   provided by the system and the application should not create its own,
   otherwise the user will see two splashscreens. Please check the
   SplashScreen class to check how the Splash Screen can be controlled and
   customized.

   https://developer.android.com/guide/topics/ui/splash-screen

C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\utils\MessageUtils.kt:27: Warning: Avoid passing null as the view root (needed to resolve layout parameters on the inflated layout's root element) [InflateParams]
            val layout = inflater.inflate(R.layout.custom_toast, null)
                                                                 ~~~~

   Explanation for issues of type "InflateParams":
   When inflating a layout, avoid passing in null as the parent view, since
   otherwise any layout parameters on the root of the inflated layout will be
   ignored.

   https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/

C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:11: Warning: Your app is currently not handling Selected Photos Access introduced in Android 14+ [SelectedPhotoAccess]
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SelectedPhotoAccess":
   Selected Photo Access is a new ability for users to share partial access to
   their photo library when apps request access to their device storage on
   Android 14+.

   Instead of letting the system manage the selection lifecycle, we recommend
   you adapt your app to handle partial access to the photo library.

   https://developer.android.com/about/versions/14/changes/partial-photo-video-access

C:\_DEV\DeaceAR\app\src\main\res\drawable\deace_logo_sticker.xml:3: Warning: Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more [VectorRaster]
    android:width="240dp"
                   ~~~~~

   Explanation for issues of type "VectorRaster":
   Vector icons require API 21 or API 24 depending on used features, but when
   minSdkVersion is less than 21 or 24 and Android Gradle plugin 1.4 or higher
   is used, a vector drawable placed in the drawable folder is automatically
   moved to drawable-anydpi-v21 or drawable-anydpi-v24 and bitmap images are
   generated for different screen resolutions for backwards compatibility.

   However, there are some limitations to this raster image generation, and
   this lint check flags elements and attributes that are not fully supported.
   You should manually check whether the generated output is acceptable for
   those older devices.

C:\_DEV\DeaceAR\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.9.2 is available: 8.10.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.10.0 is difficult: 8.9.3) [AndroidGradlePluginVersion]
agp = "8.9.2"
      ~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.9.2 is available: 8.10.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.10.0 is difficult: 8.9.3) [AndroidGradlePluginVersion]
agp = "8.9.2"
      ~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.9.2 is available: 8.10.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.10.0 is difficult: 8.9.3) [AndroidGradlePluginVersion]
agp = "8.9.2"
      ~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

C:\_DEV\DeaceAR\app\build.gradle.kts:62: Warning: A newer version of com.google.android.material:material than 1.11.0 is available: 1.12.0 [GradleDependency]
    implementation("com.google.android.material:material:1.11.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0 [GradleDependency]
coreKtx = "1.10.1"
          ~~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0 [GradleDependency]
coreKtx = "1.10.1"
          ~~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0 [GradleDependency]
coreKtx = "1.10.1"
          ~~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:6: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
junitVersion = "1.1.5"
               ~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:7: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
espressoCore = "3.5.1"
               ~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:8: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.9.0 [GradleDependency]
lifecycleRuntimeKtx = "2.6.1"
                      ~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:8: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.9.0 [GradleDependency]
lifecycleRuntimeKtx = "2.6.1"
                      ~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:8: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.9.0 [GradleDependency]
lifecycleRuntimeKtx = "2.6.1"
                      ~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:9: Warning: A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.6.1 is available: 2.9.0 [GradleDependency]
lifecycleViewmodelCompose = "2.6.1"
                            ~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:9: Warning: A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.6.1 is available: 2.9.0 [GradleDependency]
lifecycleViewmodelCompose = "2.6.1"
                            ~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:9: Warning: A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.6.1 is available: 2.9.0 [GradleDependency]
lifecycleViewmodelCompose = "2.6.1"
                            ~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:10: Warning: A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1 [GradleDependency]
activityCompose = "1.8.0"
                  ~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:10: Warning: A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1 [GradleDependency]
activityCompose = "1.8.0"
                  ~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:10: Warning: A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1 [GradleDependency]
activityCompose = "1.8.0"
                  ~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:11: Warning: A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.05.01 [GradleDependency]
composeBom = "2024.09.00"
             ~~~~~~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:11: Warning: A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.05.01 [GradleDependency]
composeBom = "2024.09.00"
             ~~~~~~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:11: Warning: A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.05.01 [GradleDependency]
composeBom = "2024.09.00"
             ~~~~~~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:15: Warning: A newer version of com.google.ar:core than 1.48.0 is available: 1.49.0 [GradleDependency]
core = "1.48.0"
       ~~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:15: Warning: A newer version of com.google.ar:core than 1.48.0 is available: 1.49.0 [GradleDependency]
core = "1.48.0"
       ~~~~~~~~
C:\_DEV\DeaceAR\gradle\libs.versions.toml:15: Warning: A newer version of com.google.ar:core than 1.48.0 is available: 1.49.0 [GradleDependency]
core = "1.48.0"
       ~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\ImagePanel.kt:327: Warning: Unnecessary; SDK_INT is always >= 29 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

C:\_DEV\DeaceAR\app\src\main\res\drawable\ic_settings.xml:9: Warning: Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
      android:pathData="M19.14,12.94c0.04,-0.3 0.06,-0.61 0.06,-0.94c0,-0.32 -0.02,-0.64 -0.07,-0.94l2.03,-1.58c0.18,-0.14 0.23,-0.41 0.12,-0.61l-1.92,-3.32c-0.12,-0.22 -0.37,-0.29 -0.59,-0.22l-2.39,0.96c-0.5,-0.38 -1.03,-0.7 -1.62,-0.94L14.4,2.81c-0.04,-0.24 -0.24,-0.41 -0.48,-0.41h-3.84c-0.24,0 -0.43,0.17 -0.47,0.41L9.25,5.35C8.66,5.59 8.12,5.92 7.63,6.29L5.24,5.33c-0.22,-0.08 -0.47,0 -0.59,0.22L2.74,8.87C2.62,9.08 2.66,9.34 2.86,9.48l2.03,1.58C4.84,11.36 4.8,11.69 4.8,12s0.02,0.64 0.07,0.94l-2.03,1.58c-0.18,0.14 -0.23,0.41 -0.12,0.61l1.92,3.32c0.12,0.22 0.37,0.29 0.59,0.22l2.39,-0.96c0.5,0.38 1.03,0.7 1.62,0.94l0.36,2.54c0.05,0.24 0.24,0.41 0.48,0.41h3.84c0.24,0 0.44,-0.17 0.47,-0.41l0.36,-2.54c0.59,-0.24 1.13,-0.56 1.62,-0.94l2.39,0.96c0.22,0.08 0.47,0 0.59,-0.22l1.92,-3.32c0.12,-0.22 0.07,-0.47 -0.12,-0.61L19.14,12.94zM12,15.6c-1.98,0 -3.6,-1.62 -3.6,-3.6s1.62,-3.6 3.6,-3.6s3.6,1.62 3.6,3.6S13.98,15.6 12,15.6z"/>
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "VectorPath":
   Using long vector paths is bad for performance. There are several ways to
   make the pathData shorter:
   * Using less precision
   * Removing some minor details
   * Using the Android Studio vector conversion tool
   * Rasterizing the image (converting to PNG)

C:\_DEV\DeaceAR\app\src\main\res\values\arrays.xml:3: Warning: The resource R.array.grid_formats appears to be unused [UnusedResources]
    <string-array name="grid_formats">
                  ~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\arrays.xml:9: Warning: The resource R.array.line_thickness_options appears to be unused [UnusedResources]
    <string-array name="line_thickness_options">
                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\drawable\baseline_grid_on_24.xml:1: Warning: The resource R.drawable.baseline_grid_on_24 appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_grid_on_black_18.png: Warning: The resource R.drawable.baseline_grid_on_black_18 appears to be unused [UnusedResources]
C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_grid_on_black_20.png: Warning: The resource R.drawable.baseline_grid_on_black_20 appears to be unused [UnusedResources]
C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_grid_on_black_24.png: Warning: The resource R.drawable.baseline_grid_on_black_24 appears to be unused [UnusedResources]
C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_grid_on_black_36.png: Warning: The resource R.drawable.baseline_grid_on_black_36 appears to be unused [UnusedResources]
C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_grid_on_black_48.png: Warning: The resource R.drawable.baseline_grid_on_black_48 appears to be unused [UnusedResources]
C:\_DEV\DeaceAR\app\src\main\res\drawable\baseline_image_20.xml:1: Warning: The resource R.drawable.baseline_image_20 appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\_DEV\DeaceAR\app\src\main\res\drawable\baseline_image_24.xml:1: Warning: The resource R.drawable.baseline_image_24 appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_image_black_18.png: Warning: The resource R.drawable.baseline_image_black_18 appears to be unused [UnusedResources]
C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_image_black_20.png: Warning: The resource R.drawable.baseline_image_black_20 appears to be unused [UnusedResources]
C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_image_black_24.png: Warning: The resource R.drawable.baseline_image_black_24 appears to be unused [UnusedResources]
C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_image_black_36.png: Warning: The resource R.drawable.baseline_image_black_36 appears to be unused [UnusedResources]
C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_image_black_48.png: Warning: The resource R.drawable.baseline_image_black_48 appears to be unused [UnusedResources]
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:4: Warning: The resource R.color.colorPrimary appears to be unused [UnusedResources]
    <color name="colorPrimary">#6200EE</color>  <!-- Purple 500 -->
           ~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:5: Warning: The resource R.color.colorPrimaryDark appears to be unused [UnusedResources]
    <color name="colorPrimaryDark">#3700B3</color>  <!-- Purple 700 -->
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:6: Warning: The resource R.color.colorAccent appears to be unused [UnusedResources]
    <color name="colorAccent">#03DAC6</color>  <!-- Teal 200 -->
           ~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:9: Warning: The resource R.color.statusBarBackground appears to be unused [UnusedResources]
    <color name="statusBarBackground">@color/colorPrimaryDark</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:10: Warning: The resource R.color.navigationBarBackground appears to be unused [UnusedResources]
    <color name="navigationBarBackground">@color/black</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:13: Warning: The resource R.color.background appears to be unused [UnusedResources]
    <color name="background">#121212</color>  <!-- Dark background for AR view -->
           ~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:14: Warning: The resource R.color.surface appears to be unused [UnusedResources]
    <color name="surface">#1E1E1E</color>  <!-- Cards/dialogs -->
           ~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:15: Warning: The resource R.color.error appears to be unused [UnusedResources]
    <color name="error">#CF6679</color>  <!-- Error color -->
           ~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:18: Warning: The resource R.color.textPrimary appears to be unused [UnusedResources]
    <color name="textPrimary">#FFFFFF</color>
           ~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:19: Warning: The resource R.color.textSecondary appears to be unused [UnusedResources]
    <color name="textSecondary">#B3B3B3</color>
           ~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:20: Warning: The resource R.color.textHint appears to be unused [UnusedResources]
    <color name="textHint">#666666</color>
           ~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:23: Warning: The resource R.color.buttonNormal appears to be unused [UnusedResources]
    <color name="buttonNormal">@color/colorPrimary</color>
           ~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:24: Warning: The resource R.color.buttonPressed appears to be unused [UnusedResources]
    <color name="buttonPressed">#7F3FD4</color>  <!-- Lighter purple -->
           ~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:25: Warning: The resource R.color.buttonDisabled appears to be unused [UnusedResources]
    <color name="buttonDisabled">#424242</color>
           ~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:28: Warning: The resource R.color.ar_grid_color appears to be unused [UnusedResources]
    <color name="ar_grid_color">#4DFFFFFF</color>  <!-- White grid with 30% opacity -->
           ~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:29: Warning: The resource R.color.ar_plane_color appears to be unused [UnusedResources]
    <color name="ar_plane_color">#4D00E5FF</color>  <!-- Cyan plane with 30% opacity -->
           ~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:30: Warning: The resource R.color.ar_reticle_color appears to be unused [UnusedResources]
    <color name="ar_reticle_color">#FF4081</color>  <!-- Pink reticle -->
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:31: Warning: The resource R.color.ar_anchor_color appears to be unused [UnusedResources]
    <color name="ar_anchor_color">#FFD600</color>  <!-- Yellow anchor -->
           ~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:36: Warning: The resource R.color.transparent appears to be unused [UnusedResources]
    <color name="transparent">#00000000</color>
           ~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:39: Warning: The resource R.color.notification_success appears to be unused [UnusedResources]
    <color name="notification_success">#4CAF50</color>  <!-- Green -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:40: Warning: The resource R.color.notification_warning appears to be unused [UnusedResources]
    <color name="notification_warning">#FFC107</color>  <!-- Amber -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:41: Warning: The resource R.color.notification_error appears to be unused [UnusedResources]
    <color name="notification_error">#F44336</color>  <!-- Red -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:44: Warning: The resource R.color.gradient_start appears to be unused [UnusedResources]
    <color name="gradient_start">@color/colorPrimary</color>
           ~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:45: Warning: The resource R.color.gradient_end appears to be unused [UnusedResources]
    <color name="gradient_end">@color/colorAccent</color>
           ~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:48: Warning: The resource R.color.panel_background appears to be unused [UnusedResources]
    <color name="panel_background">#CC1E1E1E</color>  <!-- Semi-transparent dark -->
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:49: Warning: The resource R.color.panel_divider appears to be unused [UnusedResources]
    <color name="panel_divider">#33FFFFFF</color>  <!-- Very light divider -->
           ~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:52: Warning: The resource R.color.item_selected appears to be unused [UnusedResources]
    <color name="item_selected">#33FFFFFF</color>  <!-- White with 20% opacity -->
           ~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml:53: Warning: The resource R.color.item_pressed appears to be unused [UnusedResources]
    <color name="item_pressed">#1AFFFFFF</color>  <!-- White with 10% opacity -->
           ~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\config_panel.xml:2: Warning: The resource R.layout.config_panel appears to be unused [UnusedResources]
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\_DEV\DeaceAR\app\src\main\res\values\dimens.xml:6: Warning: The resource R.dimen.main_menu_minimized_width appears to be unused [UnusedResources]
    <dimen name="main_menu_minimized_width">32dp</dimen> <!-- Adjusted to match new button size -->
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\export_panel.xml:2: Warning: The resource R.layout.export_panel appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\_DEV\DeaceAR\app\src\main\res\layout\grid_panel.xml:2: Warning: The resource R.layout.grid_panel appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\_DEV\DeaceAR\app\src\main\res\drawable\ic_maximize.xml:2: Warning: The resource R.drawable.ic_maximize appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\_DEV\DeaceAR\app\src\main\res\drawable\ic_minimize.xml:1: Warning: The resource R.drawable.ic_minimize appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\_DEV\DeaceAR\app\src\main\res\layout\image_panel.xml:2: Warning: The resource R.layout.image_panel appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\_DEV\DeaceAR\app\src\main\res\drawable\save_alt.xml:1: Warning: The resource R.drawable.save_alt appears to be unused [UnusedResources]
<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24"><path d="M19 12v7H5v-7H3v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7h-2zm-6 .67l2.59-2.58L17 11.5l-5 5-5-5 1.41-1.41L11 12.67V3h2z"/><path d="M0 0h24v24H0z" fill="none"/></svg>
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:15: Warning: The resource R.string.opacity_value appears to be unused [UnusedResources]
    <string name="opacity_value">%d%%</string>
            ~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:22: Warning: The resource R.string.deace_tab appears to be unused [UnusedResources]
    <string name="deace_tab">Deace</string>
            ~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:23: Warning: The resource R.string.minimize_menu appears to be unused [UnusedResources]
    <string name="minimize_menu">Minimize menu</string>
            ~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:30: Warning: The resource R.string.baking_title appears to be unused [UnusedResources]
    <string name="baking_title">Baking Assistant</string>
            ~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:31: Warning: The resource R.string.prompt_placeholder appears to be unused [UnusedResources]
    <string name="prompt_placeholder">Describe what you want to bake</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:32: Warning: The resource R.string.results_placeholder appears to be unused [UnusedResources]
    <string name="results_placeholder">Results will show here</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:40: Warning: The resource R.string.release_grid appears to be unused [UnusedResources]
    <string name="release_grid">Release Grid</string>
            ~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:42: Warning: The resource R.string.choose_color appears to be unused [UnusedResources]
    <string name="choose_color">Choose Color</string>
            ~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:49: Warning: The resource R.string.image_opacity appears to be unused [UnusedResources]
    <string name="image_opacity">Image Opacity</string>
            ~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:53: Warning: The resource R.string.preserve_aspect_ratio appears to be unused [UnusedResources]
    <string name="preserve_aspect_ratio">Preserve Aspect Ratio</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:59: Warning: The resource R.string.export_instructions appears to be unused [UnusedResources]
    <string name="export_instructions">Exports the current AR view without UI elements</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:77: Warning: The resource R.string.reset_ar_session appears to be unused [UnusedResources]
    <string name="reset_ar_session">Reset AR Session</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:80: Warning: The resource R.string.visualize_detected_planes appears to be unused [UnusedResources]
    <string name="visualize_detected_planes">Visualize Detected Planes</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:82: Warning: The resource R.string.default_format appears to be unused [UnusedResources]
    <string name="default_format">Default Format</string>
            ~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:85: Warning: The resource R.string.grid_color appears to be unused [UnusedResources]
    <string name="grid_color">Grid Color</string>
            ~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:86: Warning: The resource R.string.line_thickness appears to be unused [UnusedResources]
    <string name="line_thickness">Line Thickness</string>
            ~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:87: Warning: The resource R.string.projected_image_options appears to be unused [UnusedResources]
    <string name="projected_image_options">Projected Image Options</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:89: Warning: The resource R.string.image_quality appears to be unused [UnusedResources]
    <string name="image_quality">Image Quality</string>
            ~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:90: Warning: The resource R.string.normal appears to be unused [UnusedResources]
    <string name="normal">Normal</string>
            ~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:92: Warning: The resource R.string.maximum appears to be unused [UnusedResources]
    <string name="maximum">Maximum</string>
            ~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:94: Warning: The resource R.string.file_format appears to be unused [UnusedResources]
    <string name="file_format">File Format</string>
            ~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:95: Warning: The resource R.string.png appears to be unused [UnusedResources]
    <string name="png">PNG</string>
            ~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:97: Warning: The resource R.string.jpg appears to be unused [UnusedResources]
    <string name="jpg">JPG</string>
            ~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:99: Warning: The resource R.string.filename_pattern appears to be unused [UnusedResources]
    <string name="filename_pattern">Filename Pattern</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:100: Warning: The resource R.string.save_directory appears to be unused [UnusedResources]
    <string name="save_directory">Save Directory</string>
            ~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:101: Warning: The resource R.string.choose_folder appears to be unused [UnusedResources]
    <string name="choose_folder">Choose Folder</string>
            ~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:102: Warning: The resource R.string.general appears to be unused [UnusedResources]
    <string name="general">General</string>
            ~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:115: Warning: The resource R.string.tracking appears to be unused [UnusedResources]
    <string name="tracking">Tracking</string>
            ~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:116: Warning: The resource R.string.tracking_good appears to be unused [UnusedResources]
    <string name="tracking_good">Good</string>
            ~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:120: Warning: The resource R.string.tracking_move_device appears to be unused [UnusedResources]
    <string name="tracking_move_device">Move your device slowly to scan walls</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:123: Warning: The resource R.string.tracking_ready appears to be unused [UnusedResources]
    <string name="tracking_ready">Tracking ready</string>
            ~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\styles.xml:11: Warning: The resource R.style.WhiteSwitch appears to be unused [UnusedResources]
    <style name="WhiteSwitch" parent="Widget.AppCompat.CompoundButton.Switch">
           ~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:7: Warning: Replace "..." with ellipsis character (…, &#8230;) ? [TypographyEllipsis]
    <string name="initializing_ar">Initializing AR...</string>
                                   ~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:8: Warning: Replace "..." with ellipsis character (…, &#8230;) ? [TypographyEllipsis]
    <string name="requesting_permissions">Requesting permissions...</string>
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml:27: Warning: Replace "..." with ellipsis character (…, &#8230;) ? [TypographyEllipsis]
    <string name="app_restarting">App is restarting...</string>
                                  ~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "TypographyEllipsis":
   You can replace the string "..." with a dedicated ellipsis character,
   ellipsis character (u2026, &#8230;). This can help make the text more
   readable.

   https://en.wikipedia.org/wiki/Ellipsis

C:\_DEV\DeaceAR\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp: Warning: The following images appear both as density independent .xml files and as bitmap files: srcmainresmipmap-anydpiic_launcher.xml, srcmainresmipmap-hdpiic_launcher.webp [IconXmlAndPng]
C:\_DEV\DeaceAR\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp: Warning: The following images appear both as density independent .xml files and as bitmap files: srcmainresmipmap-anydpiic_launcher_round.xml, srcmainresmipmap-hdpiic_launcher_round.webp [IconXmlAndPng]

   Explanation for issues of type "IconXmlAndPng":
   If a drawable resource appears as an .xml file in the drawable/ folder,
   it's usually not intentional for it to also appear as a bitmap using the
   same name; generally you expect the drawable XML file to define states and
   each state has a corresponding drawable bitmap.

C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_grid_on_black_18.png: Warning: The following unrelated icon files have identical contents: baseline_grid_on_black_24.png, baseline_grid_on_black_36.png, baseline_grid_on_black_18.png [IconDuplicates]
C:\_DEV\DeaceAR\app\src\main\res\drawable-xxhdpi\baseline_grid_on_black_18.png: Warning: The following unrelated icon files have identical contents: baseline_grid_on_black_36.png, baseline_grid_on_black_18.png [IconDuplicates]
C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_grid_on_black_18.png: Warning: The following unrelated icon files have identical contents: baseline_grid_on_black_48.png, baseline_grid_on_black_36.png, baseline_grid_on_black_24.png, baseline_grid_on_black_18.png [IconDuplicates]
C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_grid_on_black_24.png: Warning: The following unrelated icon files have identical contents: baseline_grid_on_black_48.png, baseline_grid_on_black_24.png [IconDuplicates]
C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_grid_on_black_24.png: Warning: The following unrelated icon files have identical contents: baseline_grid_on_black_48.png, baseline_grid_on_black_24.png [IconDuplicates]
C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_grid_on_black_36.png: Warning: The following unrelated icon files have identical contents: baseline_grid_on_black_48.png, baseline_grid_on_black_36.png [IconDuplicates]
C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_image_black_18.png: Warning: The following unrelated icon files have identical contents: baseline_image_black_24.png, baseline_image_black_36.png, baseline_image_black_18.png [IconDuplicates]
C:\_DEV\DeaceAR\app\src\main\res\drawable-xxhdpi\baseline_image_black_18.png: Warning: The following unrelated icon files have identical contents: baseline_image_black_36.png, baseline_image_black_18.png [IconDuplicates]
C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_image_black_18.png: Warning: The following unrelated icon files have identical contents: baseline_image_black_48.png, baseline_image_black_36.png, baseline_image_black_24.png, baseline_image_black_18.png [IconDuplicates]
C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_image_black_24.png: Warning: The following unrelated icon files have identical contents: baseline_image_black_48.png, baseline_image_black_24.png [IconDuplicates]
C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_image_black_24.png: Warning: The following unrelated icon files have identical contents: baseline_image_black_48.png, baseline_image_black_24.png [IconDuplicates]
C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_image_black_36.png: Warning: The following unrelated icon files have identical contents: baseline_image_black_48.png, baseline_image_black_36.png [IconDuplicates]

   Explanation for issues of type "IconDuplicates":
   If an icon is repeated under different names, you can consolidate and just
   use one of the icons and delete the others to make your application
   smaller. However, duplicated icons usually are not intentional and can
   sometimes point to icons that were accidentally overwritten or accidentally
   not updated.

C:\_DEV\DeaceAR\app\src\main\res\layout\config_panel.xml:355: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\config_panel.xml:362: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~

   Explanation for issues of type "ButtonStyle":
   Button bars typically use a borderless style for the buttons. Set the
   style="?android:attr/buttonBarButtonStyle" attribute on each of the
   buttons, and set style="?android:attr/buttonBarStyle" on the parent layout

   https://d.android.com/r/studio-ui/designer/material/dialogs

C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ar\ARRenderer.kt:502: Warning: Use the KTX function createBitmap instead? [UseKtx]
            val bitmap = Bitmap.createBitmap(screenWidth, screenHeight, Bitmap.Config.ARGB_8888)
                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ar\ARRenderer.kt:525: Warning: Use the KTX function createBitmap instead? [UseKtx]
            val flippedBitmap = Bitmap.createBitmap(
                                ^
C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ar\ARRenderer.kt:531: Warning: Use the KTX extension function Bitmap.get instead? [UseKtx]
                    flippedBitmap.setPixel(x, bitmap.height - y - 1, bitmap.getPixel(x, y))
                                                                     ~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ar\ARRenderer.kt:531: Warning: Use the KTX extension function Bitmap.set instead? [UseKtx]
                    flippedBitmap.setPixel(x, bitmap.height - y - 1, bitmap.getPixel(x, y))
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ar\ARSessionManager.kt:2127: Warning: Use the KTX extension function Bitmap.scale instead? [UseKtx]
        val scaledBitmap = Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, true)
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\ConfigPanel.kt:260: Warning: Use the KTX extension property View.isVisible instead? [UseKtx]
        return scrollView.visibility == View.VISIBLE
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\DeacePanel.kt:105: Warning: Use the KTX extension property View.isVisible instead? [UseKtx]
        return panelLayout.visibility == View.VISIBLE
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\ExportPanel.kt:229: Warning: Use the KTX extension property View.isVisible instead? [UseKtx]
        return panelLayout.visibility == View.VISIBLE
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\GridPanel.kt:339: Warning: Use the KTX extension property View.isVisible instead? [UseKtx]
        return panelLayout.visibility == View.VISIBLE
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\ImagePanel.kt:571: Warning: Use the KTX extension function Bitmap.scale instead? [UseKtx]
                        val scaledBitmap = Bitmap.createScaledBitmap(loadedBitmap, thumbnailSize, thumbnailSize, true)
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\ImagePanel.kt:750: Warning: Use the KTX extension property View.isVisible instead? [UseKtx]
        return panelLayout.visibility == View.VISIBLE
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\MainActivity.kt:1762: Warning: Use the KTX extension property View.isVisible instead? [UseKtx]
                Log.d("ImageDebug", "Button state: enabled=${loadButton.isEnabled}, visible=${loadButton.visibility == View.VISIBLE}")
                                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\MainActivity.kt:1884: Warning: Use the KTX extension property View.isVisible instead? [UseKtx]
            } else if (!shouldShow && binding.quickLockImageButton.visibility == View.VISIBLE) {
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseKtx":
   The Android KTX libraries decorates the Android platform SDK as well as
   various libraries with more convenient extension functions available from
   Kotlin, allowing you to use default parameters, named parameters, and
   more.

   Available options:

   **remove-defaults** (default is true):
   Whether to skip arguments that match the defaults provided by the extension.

   Extensions often provide default values for some of the parameters. For example:
   ```kotlin
   fun Path.readLines(charset: Charset = Charsets.UTF_8): List<String> { return Files.readAllLines(this, charset) }
   ```
   This lint check will by default automatically omit parameters that match the default, so if your code was calling ```kotlin
   Files.readAllLines(file, Charset.UTF_8)
   ```
   lint would replace this with
   ```kotlin
   file.readLines()
   ```
   rather than
   ```kotlin
   file.readLines(Charset.UTF_8
   ```
   You can turn this behavior off using this option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UseKtx">
           <option name="remove-defaults" value="true" />
       </issue>
   </lint>
   ```

   **require-present** (default is true):
   Whether to only offer extensions already available.

   This option lets you only have lint suggest extension replacements if those extensions are already available on the class path (in other words, you're already depending on the library containing the extension method.)

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UseKtx">
           <option name="require-present" value="true" />
       </issue>
   </lint>
   ```

C:\_DEV\DeaceAR\app\build.gradle.kts:62: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.android.material:material:1.11.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\MainActivity.kt:1174: Warning: Custom view `GLSurfaceView` has setOnTouchListener called on it but does not override performClick [ClickableViewAccessibility]
        binding.arSurfaceView.setOnTouchListener { _, event ->
        ^
C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\MainActivity.kt:1174: Warning: onTouch lambda should call View#performClick when a click is detected [ClickableViewAccessibility]
        binding.arSurfaceView.setOnTouchListener { _, event ->
                                                 ^

   Explanation for issues of type "ClickableViewAccessibility":
   If a View that overrides onTouchEvent or uses an OnTouchListener does not
   also implement performClick and call it when clicks are detected, the View
   may not handle accessibility actions properly. Logic handling the click
   actions should ideally be placed in View#performClick as some accessibility
   services invoke performClick when a click action should occur.

C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\GridPanel.kt:83: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            opacityValueText.text = "$initialProgress%"
                                    ~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\GridPanel.kt:157: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                opacityValueText.text = "$progress%"
                                        ~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\GridPanel.kt:269: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            opacityValueText.text = "$currentProgress%"
                                    ~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\GridPanel.kt:275: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            opacityValueText.text = "${opacitySeekBar.progress}%"
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\ImagePanel.kt:132: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                imageSelectedStatus.text = "No image selected"
                                            ~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\ImagePanel.kt:768: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            imageSelectedStatus.text = "No image selected"
                                        ~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml:202: Warning: Hardcoded string "50%", should use @string resource [HardcodedText]
            android:text="50%"
            ~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml:250: Warning: Hardcoded string "No image selected", should use @string resource [HardcodedText]
            android:text="No image selected"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml:279: Warning: Hardcoded string "TEST: Place", should use @string resource [HardcodedText]
            android:text="TEST: Place" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml:662: Warning: Hardcoded string "Lock Image", should use @string resource [HardcodedText]
        android:text="Lock Image"
        ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\config_panel.xml:303: Warning: Hardcoded string "Graffiti_AR_{NNN}", should use @string resource [HardcodedText]
                android:hint="Graffiti_AR_{NNN}" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\config_panel.xml:325: Warning: Hardcoded string "/Pictures/Graffiti_AR/", should use @string resource [HardcodedText]
                android:text="/Pictures/Graffiti_AR/" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml:12: Warning: Hardcoded string "Tracking Help", should use @string resource [HardcodedText]
        android:text="Tracking Help"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml:34: Warning: Hardcoded string "Red: Tracking lost. Move your device slowly to scan the environment.", should use @string resource [HardcodedText]
            android:text="Red: Tracking lost. Move your device slowly to scan the environment."
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml:55: Warning: Hardcoded string "Yellow: Limited tracking. Continue moving to improve tracking quality.", should use @string resource [HardcodedText]
            android:text="Yellow: Limited tracking. Continue moving to improve tracking quality."
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml:76: Warning: Hardcoded string "Green: Good tracking. Tap on a wall to place content.", should use @string resource [HardcodedText]
            android:text="Green: Good tracking. Tap on a wall to place content."
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml:85: Warning: Hardcoded string "Tips for Detecting Vertical Walls:", should use @string resource [HardcodedText]
        android:text="Tips for Detecting Vertical Walls:"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml:94: Warning: Hardcoded string "• Move your device VERY SLOWLY in a figure-8 patternn• Point camera directly at walls (not at an angle)n• Scan textured walls with patterns or objectsn• Avoid plain white or reflective surfacesn• Keep 0.5-1.5m distance from wallsn• Ensure bright, even lightingn• Make sure camera lens is cleann• Try different walls if one isn't detected", should use @string resource [HardcodedText]
        android:text="• Move your device VERY SLOWLY in a figure-8 pattern\n• Point camera directly at walls (not at an angle)\n• Scan textured walls with patterns or objects\n• Avoid plain white or reflective surfaces\n• Keep 0.5-1.5m distance from walls\n• Ensure bright, even lighting\n• Make sure camera lens is clean\n• Try different walls if one isn't detected"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml:103: Warning: Hardcoded string "Got it", should use @string resource [HardcodedText]
        android:text="Got it"
        ~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\image_panel.xml:48: Warning: Hardcoded string "TEST: Place", should use @string resource [HardcodedText]
            android:text="TEST: Place"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\image_panel.xml:119: Warning: Hardcoded string "Image Rotation", should use @string resource [HardcodedText]
        android:text="Image Rotation"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\image_panel.xml:136: Warning: Hardcoded string "↺ 90°", should use @string resource [HardcodedText]
            android:text="↺ 90°"
            ~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\image_panel.xml:149: Warning: Hardcoded string "↻ 90°", should use @string resource [HardcodedText]
            android:text="↻ 90°"
            ~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\image_panel.xml:161: Warning: Hardcoded string "180°", should use @string resource [HardcodedText]
            android:text="180°"
            ~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\image_panel.xml:182: Warning: Hardcoded string "↺ 15°", should use @string resource [HardcodedText]
            android:text="↺ 15°"
            ~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\image_panel.xml:195: Warning: Hardcoded string "↻ 15°", should use @string resource [HardcodedText]
            android:text="↻ 15°"
            ~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\image_panel.xml:207: Warning: Hardcoded string "Flip H", should use @string resource [HardcodedText]
            android:text="Flip H"
            ~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml:42: Warning: Consider replacing android:layout_alignParentLeft with android:layout_alignParentStart="true" to better support right-to-left layouts [RtlHardcoded]
        android:layout_alignParentLeft="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml:122: Warning: Consider replacing android:layout_toRightOf with android:layout_toEndOf="@id/main_menu_layout" to better support right-to-left layouts [RtlHardcoded]
        android:layout_toRightOf="@id/main_menu_layout"
        ~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml:213: Warning: Consider replacing android:layout_toRightOf with android:layout_toEndOf="@id/main_menu_layout" to better support right-to-left layouts [RtlHardcoded]
        android:layout_toRightOf="@id/main_menu_layout"
        ~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml:331: Warning: Consider replacing android:layout_toRightOf with android:layout_toEndOf="@id/main_menu_layout" to better support right-to-left layouts [RtlHardcoded]
        android:layout_toRightOf="@id/main_menu_layout"
        ~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml:358: Warning: Consider replacing android:layout_toRightOf with android:layout_toEndOf="@id/main_menu_layout" to better support right-to-left layouts [RtlHardcoded]
        android:layout_toRightOf="@id/main_menu_layout"
        ~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml:577: Warning: Consider replacing android:layout_alignParentLeft with android:layout_alignParentStart="true" to better support right-to-left layouts [RtlHardcoded]
        android:layout_alignParentLeft="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml:604: Warning: Consider replacing android:layout_marginLeft with android:layout_marginStart="8dp" to better support right-to-left layouts [RtlHardcoded]
                android:layout_marginLeft="8dp"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml:644: Warning: Consider replacing android:layout_alignParentRight with android:layout_alignParentEnd="true" to better support right-to-left layouts [RtlHardcoded]
        android:layout_alignParentRight="true"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml:657: Warning: Consider replacing android:layout_toLeftOf with android:layout_toStartOf="@id/help_button" to better support right-to-left layouts [RtlHardcoded]
        android:layout_toLeftOf="@id/help_button"
        ~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml:659: Warning: Consider replacing android:layout_marginRight with android:layout_marginEnd="16dp" to better support right-to-left layouts [RtlHardcoded]
        android:layout_marginRight="16dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml:37: Warning: Consider replacing android:layout_marginLeft with android:layout_marginStart="8dp" to better support right-to-left layouts [RtlHardcoded]
            android:layout_marginLeft="8dp" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml:58: Warning: Consider replacing android:layout_marginLeft with android:layout_marginStart="8dp" to better support right-to-left layouts [RtlHardcoded]
            android:layout_marginLeft="8dp" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml:79: Warning: Consider replacing android:layout_marginLeft with android:layout_marginStart="8dp" to better support right-to-left layouts [RtlHardcoded]
            android:layout_marginLeft="8dp" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RtlHardcoded":
   Using Gravity#LEFT and Gravity#RIGHT can lead to problems when a layout is
   rendered in locales where text flows from right to left. Use Gravity#START
   and Gravity#END instead. Similarly, in XML gravity and layout_gravity
   attributes, use start rather than left.

   For XML attributes such as paddingLeft and layout_marginLeft, use
   paddingStart and layout_marginStart. NOTE: If your minSdkVersion is less
   than 17, you should add both the older left/right attributes as well as the
   new start/end attributes. On older platforms, where RTL is not supported
   and the start/end attributes are unknown and therefore ignored, you need
   the older left/right attributes. There is a separate lint check which
   catches that type of error.

   (Note: For Gravity#LEFT and Gravity#START, you can use these constants even
   when targeting older platforms, because the start bitmask is a superset of
   the left bitmask. Therefore, you can use gravity="start" rather than
   gravity="left|start".)

0 errors, 192 warnings
