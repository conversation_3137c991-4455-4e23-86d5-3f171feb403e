package com.deace.deacear.ui

import android.Manifest
import android.app.AlertDialog
import android.content.Context
import android.content.pm.PackageManager
import android.os.Handler
import android.os.Looper
import android.os.Environment
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import android.widget.Toast
import com.deace.deacear.utils.MessageUtils
import androidx.core.content.ContextCompat
import com.deace.deacear.MainActivity
import com.deace.deacear.R
import com.deace.deacear.ar.ARSessionManager
import com.deace.deacear.utils.FileUtils

import java.io.File
import java.text.SimpleDateFormat
import java.util.*

class ExportPanel(
    private val panelLayout: LinearLayout,
    private val context: Context,
    private val settingsManager: com.deace.deacear.utils.SettingsManager = com.deace.deacear.utils.SettingsManager(context)
) {
    private lateinit var exportButton: Button
    private var arSessionManager: ARSessionManager? = null
    private val fileUtils = FileUtils(context)
    private val dateFormatter = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())

    // Auto-hide timer constants and components
    private val AUTO_HIDE_DELAY_MS = 5000L // 5 seconds
    private val handler = Handler(Looper.getMainLooper())
    private val hideRunnable = Runnable {
        hide()
        Log.d("ExportPanel", "Auto-hiding export panel after inactivity")
    }

    init {
        Log.d("ExportPanel", "Initializing...")
        // Post to the UI thread to ensure views are fully inflated
        panelLayout.post {
            initializeViews()
            setupListeners()
        }
    }

    private fun initializeViews() {
        exportButton = panelLayout.findViewById(R.id.export_button)
    }

    private fun setupListeners() {
        exportButton.setOnClickListener {
            resetAutoHideTimer() // Reset auto-hide timer on user interaction
            checkPermissionsAndExport()
        }
    }

    private fun checkPermissionsAndExport() {
        if (ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            exportARView()
        } else {
            requestStoragePermission()
        }
    }

    private fun requestStoragePermission() {
        // Show a dialog explaining why we need storage permission
        AlertDialog.Builder(context)
            .setTitle(R.string.storage_permission_required)
            .setMessage(R.string.storage_permission_explanation)
            .setPositiveButton(R.string.grant_permission) { _, _ ->
                // Request the permission through MainActivity
                if (context is MainActivity) {
                    // Show a toast to further guide the user about the system dialog
                    MessageUtils.showToast(
                        context,
                        "Please approve the permission in the system dialog",
                        Toast.LENGTH_LONG
                    )

                    // Request the permission
                    context.requestStoragePermission()
                } else {
                    MessageUtils.showToast(
                        context,
                        context.getString(R.string.storage_permission_required),
                        Toast.LENGTH_LONG
                    )
                }
            }
            .setNegativeButton(R.string.cancel, null)
            .show()
    }

    private fun exportARView() {
        arSessionManager?.let { manager ->
            val bitmap = manager.captureARView()
            if (bitmap != null) {
                saveBitmapToFile(bitmap)
            } else {
                MessageUtils.showToast(
                    context,
                    context.getString(R.string.failed_to_capture_image),
                    Toast.LENGTH_SHORT
                )
            }
        } ?: run {
            MessageUtils.showToast(
                context,
                context.getString(R.string.ar_session_not_ready),
                Toast.LENGTH_SHORT
            )
        }
    }

    private fun saveBitmapToFile(bitmap: android.graphics.Bitmap) {
        try {
            // Use save directory from settings
            val saveDir = settingsManager.saveDirectory
            val folder = File(
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES),
                saveDir.substringAfterLast('/')  // Extract the last part of the path
            )

            if (!folder.exists()) {
                folder.mkdirs()
            }

            val filename = generateFilename()
            val file = File(folder, filename)

            // Use file format from settings
            val fileFormat = when (settingsManager.fileFormat) {
                com.deace.deacear.utils.SettingsManager.FILE_FORMAT_PNG -> android.graphics.Bitmap.CompressFormat.PNG
                com.deace.deacear.utils.SettingsManager.FILE_FORMAT_JPG -> android.graphics.Bitmap.CompressFormat.JPEG
                else -> android.graphics.Bitmap.CompressFormat.PNG
            }

            // Use quality setting from settings
            val quality = when (settingsManager.imageQuality) {
                com.deace.deacear.utils.SettingsManager.IMAGE_QUALITY_MAX -> 100
                else -> 90 // Normal quality
            }

            if (fileUtils.saveBitmap(bitmap, file, fileFormat, quality)) {
                showExportSuccessDialog(file.absolutePath)
            } else {
                MessageUtils.showToast(
                    context,
                    context.getString(R.string.failed_to_save_image),
                    Toast.LENGTH_SHORT
                )
            }
        } catch (e: Exception) {
            MessageUtils.showToast(
                context,
                context.getString(R.string.export_error, e.localizedMessage),
                Toast.LENGTH_LONG
            )
        }
    }

    private fun generateFilename(): String {
        val timestamp = dateFormatter.format(Date())

        // Use filename pattern from settings, replacing timestamp placeholder
        val filenamePattern = settingsManager.filenamePattern
        val extension = settingsManager.getFileExtension()

        return filenamePattern.replace("{timestamp}", timestamp) + extension
    }

    private fun showExportSuccessDialog(filePath: String) {
        AlertDialog.Builder(context)
            .setTitle(R.string.export_success)
            .setMessage(context.getString(R.string.image_saved_to, filePath))
            .setPositiveButton(R.string.ok, null)
            .show()
    }

    fun setARSessionManager(manager: ARSessionManager) {
        this.arSessionManager = manager
    }

    fun show() {
        panelLayout.visibility = View.VISIBLE
        resetAutoHideTimer()
        Log.d("ExportPanel", "Panel shown")
    }

    fun hide() {
        panelLayout.visibility = View.GONE
        cancelAutoHideTimer()
        Log.d("ExportPanel", "Panel hidden")
    }

    /**
     * Resets the auto-hide timer
     */
    fun resetAutoHideTimer() {
        // Cancel any existing timer
        cancelAutoHideTimer()
        // Start a new timer
        handler.postDelayed(hideRunnable, AUTO_HIDE_DELAY_MS)
        Log.d("ExportPanel", "Auto-hide timer reset. Panel will hide in ${AUTO_HIDE_DELAY_MS/1000} seconds if no interaction")
    }

    /**
     * Cancels the auto-hide timer
     */
    private fun cancelAutoHideTimer() {
        handler.removeCallbacks(hideRunnable)
    }

    /**
     * Returns whether this panel is currently active (visible or should be visible)
     */
    fun isActive(): Boolean {
        return panelLayout.visibility == View.VISIBLE
    }

    /**
     * Retry export after permissions have been granted
     */
    fun retryExport() {
        exportARView()
    }
}