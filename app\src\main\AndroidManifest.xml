<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.deace.deacear">

    <!-- AR Core requires camera permission -->
    <uses-permission android:name="android.permission.CAMERA" />
    <!-- Required for saving/loading images -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <!-- AR features require OpenGL ES -->
    <uses-feature android:glEsVersion="0x00030000" android:required="true" />
    <!-- Indicates AR required -->
    <uses-feature android:name="android.hardware.camera.ar" android:required="true" />
    <!-- Camera hardware feature -->
    <uses-feature android:name="android.hardware.camera" android:required="false" />

    <queries>
        <package android:name="com.google.ar.core" />
    </queries>

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.DeaceAR"
        tools:targetApi="31">

        <!-- Required for ARCore -->
        <meta-data
            android:name="com.google.ar.core"
            android:value="required" />

        <activity
            android:name=".SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.SplashScreen">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".MainActivity"
            android:exported="false"
            android:configChanges="orientation|screenSize"
            android:theme="@style/Theme.DeaceAR">
        </activity>
    </application>

</manifest>