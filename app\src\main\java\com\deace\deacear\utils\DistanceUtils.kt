package com.deace.deacear.utils

import com.google.ar.core.Pose
import kotlin.math.sqrt

/**
 * Utility class for distance calculations in AR
 */
object DistanceUtils {
    /**
     * Calculate the distance between two poses
     * @param pose1 First pose
     * @param pose2 Second pose
     * @return Distance in meters
     */
    fun calculateDistance(pose1: Pose, pose2: Pose): Float {
        val dx = pose1.tx() - pose2.tx()
        val dy = pose1.ty() - pose2.ty()
        val dz = pose1.tz() - pose2.tz()
        return sqrt(dx * dx + dy * dy + dz * dz)
    }
}
