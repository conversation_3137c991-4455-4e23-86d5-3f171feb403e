<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- AR Settings Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ar_settings"
                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- Reset Session and other buttons on same line -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/reset_session_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/reset_ar_session" />
        </LinearLayout>

        <!-- Grid Options -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/grid_options"
                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- Grid Format -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/default_format"
                android:layout_marginEnd="8dp" />

            <androidx.appcompat.widget.AppCompatSpinner
                android:id="@+id/default_grid_format_spinner"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/grid_formats" />
        </LinearLayout>

        <!-- Grid Opacity -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/default_opacity"
                android:layout_marginEnd="8dp" />

            <androidx.appcompat.widget.AppCompatSeekBar
                android:id="@+id/default_grid_opacity_seekbar"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:max="100"
                android:progress="50" />
        </LinearLayout>

        <!-- Grid Color and Line Thickness -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/grid_color"
                android:layout_marginEnd="8dp" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/grid_color_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/choose_color" />

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/line_thickness"
                android:layout_marginEnd="8dp" />

            <androidx.appcompat.widget.AppCompatSpinner
                android:id="@+id/grid_line_thickness_spinner"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:entries="@array/line_thickness_options" />
        </LinearLayout>

        <!-- Image Options -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/projected_image_options"
                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- Image Opacity -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/default_opacity"
                android:layout_marginEnd="8dp" />

            <androidx.appcompat.widget.AppCompatSeekBar
                android:id="@+id/default_image_opacity_seekbar"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:max="100"
                android:progress="50" />
        </LinearLayout>

        <!-- Preserve Aspect Ratio -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/preserve_aspect_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="8dp"
                android:text="@string/preserve_aspect_ratio" />
        </LinearLayout>

        <!-- Export Settings -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/export_settings"
                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- Image Quality -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/image_quality"
                android:layout_marginEnd="8dp" />

            <RadioGroup
                android:id="@+id/image_quality_radio_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/normal_quality_radio"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/normal" />

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/max_quality_radio"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="@string/maximum" />
            </RadioGroup>
        </LinearLayout>

        <!-- File Format -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/file_format"
                android:layout_marginEnd="8dp" />

            <RadioGroup
                android:id="@+id/file_format_radio_group"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/png_format_radio"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/png" />

                <androidx.appcompat.widget.AppCompatRadioButton
                    android:id="@+id/jpg_format_radio"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="@string/jpg" />
            </RadioGroup>
        </LinearLayout>

        <!-- Filename Pattern -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/filename_pattern"
                android:layout_marginEnd="8dp" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/filename_pattern_edittext"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="Graffiti_AR_{NNN}" />
        </LinearLayout>

        <!-- Save Directory -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/save_directory"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/save_directory_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="/Pictures/Graffiti_AR/" />

            <Button
                android:id="@+id/save_directory_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/choose_folder" />
        </LinearLayout>

        <!-- General -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/general"
                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/reset_settings_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/reset_settings" />

            <Button
                android:id="@+id/help_about_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="@string/help_about" />
        </LinearLayout>
    </LinearLayout>
</ScrollView>