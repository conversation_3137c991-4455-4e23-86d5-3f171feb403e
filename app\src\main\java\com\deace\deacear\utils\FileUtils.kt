package com.deace.deacear.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Environment
import android.provider.OpenableColumns
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class FileUtils(private val context: Context) {
    companion object {
        private const val MAX_IMAGE_DIMENSION = 2048
        private const val COMPRESSION_QUALITY = 90

        fun uriToBitmap(context: Context, uri: Uri): Bitmap? {
            return try {
                context.contentResolver.openInputStream(uri)?.use { inputStream ->
                    FileUtils(context).decodeSampledBitmapFromStream(inputStream)
                }
            } catch (e: IOException) {
                null
            }
        }
    }

    private fun decodeSampledBitmapFromStream(inputStream: InputStream): Bitmap {
        // First decode with inJustDecodeBounds=true to check dimensions
        val options = BitmapFactory.Options().apply {
            inJustDecodeBounds = true
        }
        BitmapFactory.decodeStream(inputStream, null, options)

        // Calculate inSampleSize
        options.inSampleSize = calculateInSampleSize(
            options.outWidth,
            options.outHeight
        )

        // Reset the input stream
        inputStream.reset()

        // Decode bitmap with inSampleSize set
        options.inJustDecodeBounds = false
        return BitmapFactory.decodeStream(inputStream, null, options)
            ?: throw IOException("Failed to decode bitmap")
    }

    private fun calculateInSampleSize(width: Int, height: Int): Int {
        var inSampleSize = 1

        if (height > MAX_IMAGE_DIMENSION || width > MAX_IMAGE_DIMENSION) {
            val halfHeight = height / 2
            val halfWidth = width / 2

            // Calculate the largest inSampleSize value that keeps both
            // height and width larger than MAX_IMAGE_DIMENSION
            while (halfHeight / inSampleSize >= MAX_IMAGE_DIMENSION &&
                halfWidth / inSampleSize >= MAX_IMAGE_DIMENSION) {
                inSampleSize *= 2
            }
        }

        return inSampleSize
    }

    fun saveBitmap(bitmap: Bitmap, file: File, format: Bitmap.CompressFormat? = null, quality: Int = COMPRESSION_QUALITY): Boolean {
        return try {
            FileOutputStream(file).use { out ->
                // Use provided format or determine from filename
                val compressFormat = format ?: getCompressFormat(file.name)
                bitmap.compress(
                    compressFormat,
                    quality,
                    out
                )
                true
            }
        } catch (e: IOException) {
            false
        }
    }

    private fun getCompressFormat(filename: String): Bitmap.CompressFormat {
        return when {
            filename.endsWith(".png", ignoreCase = true) ->
                Bitmap.CompressFormat.PNG
            filename.endsWith(".webp", ignoreCase = true) ->
                Bitmap.CompressFormat.WEBP
            else -> Bitmap.CompressFormat.JPEG
        }
    }

    fun getFileName(uri: Uri): String? {
        return context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                val columnIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                if (columnIndex >= 0) {
                    cursor.getString(columnIndex)
                } else {
                    // Fallback to parsing from URI if column not found
                    uri.lastPathSegment?.substringAfterLast('/')
                }
            } else {
                null
            }
        }
    }

    fun createImageFile(): File {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return File.createTempFile(
            "JPEG_${timeStamp}_",
            ".jpg",
            storageDir
        )
    }

    fun getPublicPicturesDirectory(subfolder: String): File {
        val dir = File(
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES),
            subfolder
        )
        if (!dir.exists()) {
            dir.mkdirs()
        }
        return dir
    }
}