<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\_DEV\DeaceAR\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\_DEV\DeaceAR\app\src\main\res"><file name="pulse" path="C:\_DEV\DeaceAR\app\src\main\res\anim\pulse.xml" qualifiers="" type="anim"/><file name="baseline_grid_on_24" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\baseline_grid_on_24.xml" qualifiers="" type="drawable"/><file name="baseline_image_20" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\baseline_image_20.xml" qualifiers="" type="drawable"/><file name="baseline_image_24" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\baseline_image_24.xml" qualifiers="" type="drawable"/><file name="circle_shape" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\circle_shape.xml" qualifiers="" type="drawable"/><file name="deace_logo_sticker" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\deace_logo_sticker.xml" qualifiers="" type="drawable"/><file name="ic_export" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\ic_export.xml" qualifiers="" type="drawable"/><file name="ic_grid" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\ic_grid.xml" qualifiers="" type="drawable"/><file name="ic_grid_horizontal" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\ic_grid_horizontal.xml" qualifiers="" type="drawable"/><file name="ic_grid_square" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\ic_grid_square.xml" qualifiers="" type="drawable"/><file name="ic_grid_vertical" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\ic_grid_vertical.xml" qualifiers="" type="drawable"/><file name="ic_help" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\ic_help.xml" qualifiers="" type="drawable"/><file name="ic_image" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\ic_image.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_maximize" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\ic_maximize.xml" qualifiers="" type="drawable"/><file name="ic_minimize" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\ic_minimize.xml" qualifiers="" type="drawable"/><file name="ic_reticle" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\ic_reticle.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="rounded_button_background" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\rounded_button_background.xml" qualifiers="" type="drawable"/><file name="rounded_panel_background" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\rounded_panel_background.xml" qualifiers="" type="drawable"/><file name="save_alt" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\save_alt.xml" qualifiers="" type="drawable"/><file name="selected_button_background" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\selected_button_background.xml" qualifiers="" type="drawable"/><file name="splash_background" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\splash_background.xml" qualifiers="" type="drawable"/><file name="toast_background" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\toast_background.xml" qualifiers="" type="drawable"/><file name="tracking_progress_background" path="C:\_DEV\DeaceAR\app\src\main\res\drawable\tracking_progress_background.xml" qualifiers="" type="drawable"/><file name="baseline_grid_on_black_18" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_grid_on_black_18.png" qualifiers="hdpi-v4" type="drawable"/><file name="baseline_grid_on_black_20" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_grid_on_black_20.png" qualifiers="hdpi-v4" type="drawable"/><file name="baseline_grid_on_black_24" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_grid_on_black_24.png" qualifiers="hdpi-v4" type="drawable"/><file name="baseline_grid_on_black_36" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_grid_on_black_36.png" qualifiers="hdpi-v4" type="drawable"/><file name="baseline_grid_on_black_48" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_grid_on_black_48.png" qualifiers="hdpi-v4" type="drawable"/><file name="baseline_image_black_18" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_image_black_18.png" qualifiers="hdpi-v4" type="drawable"/><file name="baseline_image_black_20" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_image_black_20.png" qualifiers="hdpi-v4" type="drawable"/><file name="baseline_image_black_24" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_image_black_24.png" qualifiers="hdpi-v4" type="drawable"/><file name="baseline_image_black_36" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_image_black_36.png" qualifiers="hdpi-v4" type="drawable"/><file name="baseline_image_black_48" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_image_black_48.png" qualifiers="hdpi-v4" type="drawable"/><file name="baseline_grid_on_black_18" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-mdpi\baseline_grid_on_black_18.png" qualifiers="mdpi-v4" type="drawable"/><file name="baseline_grid_on_black_20" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-mdpi\baseline_grid_on_black_20.png" qualifiers="mdpi-v4" type="drawable"/><file name="baseline_grid_on_black_24" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-mdpi\baseline_grid_on_black_24.png" qualifiers="mdpi-v4" type="drawable"/><file name="baseline_grid_on_black_36" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-mdpi\baseline_grid_on_black_36.png" qualifiers="mdpi-v4" type="drawable"/><file name="baseline_grid_on_black_48" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-mdpi\baseline_grid_on_black_48.png" qualifiers="mdpi-v4" type="drawable"/><file name="baseline_image_black_18" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-mdpi\baseline_image_black_18.png" qualifiers="mdpi-v4" type="drawable"/><file name="baseline_image_black_20" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-mdpi\baseline_image_black_20.png" qualifiers="mdpi-v4" type="drawable"/><file name="baseline_image_black_24" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-mdpi\baseline_image_black_24.png" qualifiers="mdpi-v4" type="drawable"/><file name="baseline_image_black_36" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-mdpi\baseline_image_black_36.png" qualifiers="mdpi-v4" type="drawable"/><file name="baseline_image_black_48" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-mdpi\baseline_image_black_48.png" qualifiers="mdpi-v4" type="drawable"/><file name="baseline_grid_on_black_18" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_grid_on_black_18.png" qualifiers="xhdpi-v4" type="drawable"/><file name="baseline_grid_on_black_20" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_grid_on_black_20.png" qualifiers="xhdpi-v4" type="drawable"/><file name="baseline_grid_on_black_24" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_grid_on_black_24.png" qualifiers="xhdpi-v4" type="drawable"/><file name="baseline_grid_on_black_36" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_grid_on_black_36.png" qualifiers="xhdpi-v4" type="drawable"/><file name="baseline_grid_on_black_48" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_grid_on_black_48.png" qualifiers="xhdpi-v4" type="drawable"/><file name="baseline_image_black_18" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_image_black_18.png" qualifiers="xhdpi-v4" type="drawable"/><file name="baseline_image_black_20" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_image_black_20.png" qualifiers="xhdpi-v4" type="drawable"/><file name="baseline_image_black_24" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_image_black_24.png" qualifiers="xhdpi-v4" type="drawable"/><file name="baseline_image_black_36" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_image_black_36.png" qualifiers="xhdpi-v4" type="drawable"/><file name="baseline_image_black_48" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_image_black_48.png" qualifiers="xhdpi-v4" type="drawable"/><file name="baseline_grid_on_black_18" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxhdpi\baseline_grid_on_black_18.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="baseline_grid_on_black_20" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxhdpi\baseline_grid_on_black_20.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="baseline_grid_on_black_24" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxhdpi\baseline_grid_on_black_24.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="baseline_grid_on_black_36" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxhdpi\baseline_grid_on_black_36.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="baseline_grid_on_black_48" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxhdpi\baseline_grid_on_black_48.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="baseline_image_black_18" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxhdpi\baseline_image_black_18.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="baseline_image_black_20" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxhdpi\baseline_image_black_20.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="baseline_image_black_24" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxhdpi\baseline_image_black_24.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="baseline_image_black_36" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxhdpi\baseline_image_black_36.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="baseline_image_black_48" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxhdpi\baseline_image_black_48.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="baseline_grid_on_black_18" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_grid_on_black_18.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="baseline_grid_on_black_20" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_grid_on_black_20.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="baseline_grid_on_black_24" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_grid_on_black_24.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="baseline_grid_on_black_36" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_grid_on_black_36.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="baseline_grid_on_black_48" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_grid_on_black_48.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="baseline_image_black_18" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_image_black_18.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="baseline_image_black_20" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_image_black_20.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="baseline_image_black_24" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_image_black_24.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="baseline_image_black_36" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_image_black_36.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="baseline_image_black_48" path="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_image_black_48.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="activity_main" path="C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="config_panel" path="C:\_DEV\DeaceAR\app\src\main\res\layout\config_panel.xml" qualifiers="" type="layout"/><file name="custom_toast" path="C:\_DEV\DeaceAR\app\src\main\res\layout\custom_toast.xml" qualifiers="" type="layout"/><file name="dialog_tracking_help" path="C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml" qualifiers="" type="layout"/><file name="export_panel" path="C:\_DEV\DeaceAR\app\src\main\res\layout\export_panel.xml" qualifiers="" type="layout"/><file name="grid_panel" path="C:\_DEV\DeaceAR\app\src\main\res\layout\grid_panel.xml" qualifiers="" type="layout"/><file name="image_panel" path="C:\_DEV\DeaceAR\app\src\main\res\layout\image_panel.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\_DEV\DeaceAR\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\_DEV\DeaceAR\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\_DEV\DeaceAR\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\_DEV\DeaceAR\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\_DEV\DeaceAR\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\_DEV\DeaceAR\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\_DEV\DeaceAR\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\_DEV\DeaceAR\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\_DEV\DeaceAR\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\_DEV\DeaceAR\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\_DEV\DeaceAR\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\_DEV\DeaceAR\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\_DEV\DeaceAR\app\src\main\res\values\arrays.xml" qualifiers=""><string-array name="grid_formats">
        <item>Vertical (9:16)</item>
        <item>Square (1:1)</item>
        <item>Horizontal (16:9)</item>
    </string-array><string-array name="line_thickness_options">
        <item>Fine</item>
        <item>Medium</item>
        <item>Thick</item>
    </string-array></file><file path="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml" qualifiers=""><color name="colorPrimary">#6200EE</color><color name="colorPrimaryDark">#3700B3</color><color name="colorAccent">#03DAC6</color><color name="statusBarBackground">@color/colorPrimaryDark</color><color name="navigationBarBackground">@color/black</color><color name="background">#121212</color><color name="surface">#1E1E1E</color><color name="error">#CF6679</color><color name="textPrimary">#FFFFFF</color><color name="textSecondary">#B3B3B3</color><color name="textHint">#666666</color><color name="buttonNormal">@color/colorPrimary</color><color name="buttonPressed">#7F3FD4</color><color name="buttonDisabled">#424242</color><color name="ar_grid_color">#4DFFFFFF</color><color name="ar_plane_color">#4D00E5FF</color><color name="ar_reticle_color">#FF4081</color><color name="ar_anchor_color">#FFD600</color><color name="white">#FFFFFFFF</color><color name="black">#FF000000</color><color name="transparent">#00000000</color><color name="notification_success">#4CAF50</color><color name="notification_warning">#FFC107</color><color name="notification_error">#F44336</color><color name="gradient_start">@color/colorPrimary</color><color name="gradient_end">@color/colorAccent</color><color name="panel_background">#CC1E1E1E</color><color name="panel_divider">#33FFFFFF</color><color name="item_selected">#33FFFFFF</color><color name="item_pressed">#1AFFFFFF</color></file><file path="C:\_DEV\DeaceAR\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="main_menu_minimized_width">32dp</dimen></file><file path="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Graffiti AR Deace</string><string name="scanning_environment" ns1:ignore="DuplicateDefinition">Point your device at a wall and move it slowly</string><string name="installing_arcore">Installing ARCore</string><string name="initializing_ar">Initializing AR...</string><string name="requesting_permissions">Requesting permissions...</string><string name="arcore_not_supported">ARCore is not supported on this device</string><string name="permission_required">Permission Required</string><string name="camera_permission_required_message">This app requires camera permission to function. Please enable it in settings.</string><string name="settings">Settings</string><string name="exit">Exit</string><string name="opacity_format">%d%%</string><string name="opacity_value">%d%%</string><string name="grid_tab">Grid</string><string name="image_tab">Image</string><string name="export_tab">Export</string><string name="config_tab">Config</string><string name="deace_tab">Deace</string><string name="minimize_menu">Minimize menu</string><string name="confirm_reinit_app">RESTART APP</string><string name="app_restarting">App is restarting...</string><string name="baking_title">Baking Assistant</string><string name="prompt_placeholder">Describe what you want to bake</string><string name="results_placeholder">Results will show here</string><string name="grid_format">Grid</string><string name="vertical">Vertical (9:16)</string><string name="square">Square (1:1)</string><string name="horizontal">Horizontal (16:9)</string><string name="fix_grid">Fix Grid</string><string name="release_grid">Release Grid</string><string name="grid_opacity">Grid Opacity</string><string name="choose_color">Choose Color</string><string name="load_image">Load Image</string><string name="fix_image">Fix Image</string><string name="release_image">Release Image</string><string name="image_opacity">Image Opacity</string><string name="no_image_loaded">No image loaded</string><string formatted="false" name="image_size_format">Image size: %d×%d</string><string name="opacity_format_50">50%</string><string name="preserve_aspect_ratio">Preserve Aspect Ratio</string><string name="failed_to_load_image">Failed to load image</string><string name="image_load_error">Error loading image: %s</string><string name="export_ar_view">Export AR View</string><string name="export_instructions">Exports the current AR view without UI elements</string><string name="storage_permission_required">Storage permission is required to save images</string><string name="storage_permission_explanation">To save images from the app, we need permission to access your device storage.\n\nAfter clicking \"GRANT PERMISSION\", please also approve the permission in the system dialog that will appear.</string><string name="storage_permission_denied">Cannot save images without storage permission</string><string name="grant_permission">Grant Permission</string><string name="cancel">Cancel</string><string name="failed_to_capture_image">Failed to capture AR view</string><string name="ar_session_not_ready">AR session is not ready</string><string name="failed_to_save_image">Failed to save image</string><string name="export_success">Export Successful</string><string name="image_saved_to">Image saved to:\n%s</string><string name="export_error">Error exporting image: %s</string><string name="ar_session_reset">AR Session Reset</string><string name="settings_reset">Settings Reset</string><string name="ok">OK</string><string name="ar_settings">AR Settings</string><string name="reset_ar_session">Reset AR Session</string><string name="reset_session">Reset Session</string><string name="show_planes">Show Planes</string><string name="visualize_detected_planes">Visualize Detected Planes</string><string name="grid_options">Grid Options</string><string name="default_format">Default Format</string><string name="default_grid_format">Default Grid Format</string><string name="default_opacity">Default Opacity</string><string name="grid_color">Grid Color</string><string name="line_thickness">Line Thickness</string><string name="projected_image_options">Projected Image Options</string><string name="export_settings">Export Settings</string><string name="image_quality">Image Quality</string><string name="normal">Normal</string><string name="normal_quality">Normal Quality</string><string name="maximum">Maximum</string><string name="max_quality">Maximum Quality</string><string name="file_format">File Format</string><string name="png">PNG</string><string name="png_format">PNG Format</string><string name="jpg">JPG</string><string name="jpg_format">JPG Format</string><string name="filename_pattern">Filename Pattern</string><string name="save_directory">Save Directory</string><string name="choose_folder">Choose Folder</string><string name="general">General</string><string name="app_settings">App Settings</string><string name="reset_settings">Reset Settings</string><string name="help_about">Help / About</string><string name="about_text">Graffiti AR Deace v0.1 2025. ©DEACE : Author member of ADAGP. Authorization is required for any use of the works (www.adagp.fr). More on www.deace.com</string><string-array name="grid_formats_array">
        <item>Vertical (9:16)</item>
        <item>Square (1:1)</item>
        <item>Horizontal (16:9)</item>
    </string-array><string name="tracking">Tracking</string><string name="tracking_good">Good</string><string name="tracking_limited">Limited</string><string name="tracking_lost">Lost</string><string name="tracking_initializing">Initializing tracking…</string><string name="tracking_move_device">Move your device slowly to scan walls</string><string name="tracking_looking_for_surfaces">Looking for surfaces…</string><string name="tracking_found_surfaces">Surfaces detected</string><string name="tracking_ready">Tracking ready</string><string name="tracking_help_lost">Move device slowly to scan environment</string><string name="tracking_help_limited">Continue moving to improve tracking</string><string name="tracking_help_good">Good tracking - tap on a wall to place</string><string name="help">Help</string></file><file path="C:\_DEV\DeaceAR\app\src\main\res\values\styles.xml" qualifiers=""><style name="WhiteSeekBar" parent="Widget.AppCompat.SeekBar">
        <item name="android:progressTint">@android:color/white</item>
        <item name="android:thumbTint">@android:color/white</item>
        <item name="android:progressBackgroundTint">@android:color/darker_gray</item>
    </style><style name="WhiteSwitch" parent="Widget.AppCompat.CompoundButton.Switch">
        <item name="colorControlActivated">@android:color/white</item>
        <item name="android:colorControlActivated">@android:color/white</item>
        <item name="android:thumbTint">@android:color/white</item>
        <item name="android:trackTint">@android:color/white</item>
    </style></file><file path="C:\_DEV\DeaceAR\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.DeaceAR" parent="Theme.AppCompat.Light.NoActionBar">
        
        <item name="colorPrimary">@color/black</item>
        <item name="colorPrimaryDark">@color/black</item>
        <item name="colorAccent">@color/black</item>
        <item name="android:windowBackground">@android:color/white</item>
    </style><style name="Theme.SplashScreen" parent="Theme.DeaceAR">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowFullscreen">true</item>
    </style><style name="Theme.DeaceAR.Dialog" parent="Theme.AppCompat.Dialog.Alert">
        <item name="colorPrimary">@color/black</item>
        <item name="colorAccent">@color/black</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:background">@drawable/rounded_panel_background</item>
        <item name="buttonBarPositiveButtonStyle">@style/DialogButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/DialogButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">@style/DialogButtonStyle</item>
    </style><style name="DialogButtonStyle" parent="Widget.AppCompat.Button.ButtonBar.AlertDialog">
        <item name="android:textColor">@color/white</item>
    </style></file><file name="backup_rules" path="C:\_DEV\DeaceAR\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\_DEV\DeaceAR\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\_DEV\DeaceAR\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\_DEV\DeaceAR\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\_DEV\DeaceAR\app\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\_DEV\DeaceAR\app\build\generated\res\resValues\release"/></dataSet><mergedItems/></merger>