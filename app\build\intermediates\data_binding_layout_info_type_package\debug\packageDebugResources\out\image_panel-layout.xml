<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="image_panel" modulePackage="com.deace.deacear" filePath="app\src\main\res\layout\image_panel.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/image_panel_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="211" endOffset="14"/></Target><Target id="@+id/load_image_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="7" startOffset="4" endLine="13" endOffset="43"/></Target><Target id="@+id/image_size_text" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="15" startOffset="4" endLine="21" endOffset="72"/></Target><Target id="@+id/fix_image_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="29" startOffset="8" endLine="36" endOffset="46"/></Target><Target id="@+id/test_image_placement_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="39" startOffset="8" endLine="50" endOffset="46"/></Target><Target id="@+id/release_image_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="52" startOffset="8" endLine="61" endOffset="50"/></Target><Target id="@+id/image_opacity_seekbar" view="androidx.appcompat.widget.AppCompatSeekBar"><Expressions/><location startLine="77" startOffset="8" endLine="83" endOffset="35"/></Target><Target id="@+id/image_opacity_value" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="85" startOffset="8" endLine="90" endOffset="54"/></Target><Target id="@+id/preserve_aspect_switch" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="99" startOffset="8" endLine="103" endOffset="36"/></Target><Target id="@+id/rotate_left_90_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="128" startOffset="8" endLine="138" endOffset="46"/></Target><Target id="@+id/rotate_right_90_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="140" startOffset="8" endLine="151" endOffset="46"/></Target><Target id="@+id/rotate_180_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="153" startOffset="8" endLine="163" endOffset="46"/></Target><Target id="@+id/rotate_left_15_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="174" startOffset="8" endLine="184" endOffset="46"/></Target><Target id="@+id/rotate_right_15_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="186" startOffset="8" endLine="197" endOffset="46"/></Target><Target id="@+id/flip_horizontal_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="199" startOffset="8" endLine="209" endOffset="46"/></Target></Targets></Layout>