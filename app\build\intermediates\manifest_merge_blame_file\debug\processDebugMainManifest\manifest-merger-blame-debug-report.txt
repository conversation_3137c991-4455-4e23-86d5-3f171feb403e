1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.deace.deacear"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
9        android:targetSdkVersion="35" />
10
11    <!-- AR Core requires camera permission -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:7:5-65
12-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:7:22-62
13    <!-- Required for saving/loading images -->
14    <uses-permission
14-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:9:5-10:38
15        android:name="android.permission.READ_EXTERNAL_STORAGE"
15-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:9:22-77
16        android:maxSdkVersion="32" />
16-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:10:9-35
17    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
17-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:11:5-76
17-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:11:22-73
18    <uses-permission
18-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:12:5-13:38
19        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
19-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:12:22-78
20        android:maxSdkVersion="28" />
20-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:13:9-35
21
22    <!-- AR features require OpenGL ES -->
23    <uses-feature
23-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:16:5-78
24        android:glEsVersion="0x00030000"
24-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:16:19-51
25        android:required="true" />
25-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:16:52-75
26    <!-- Indicates AR required -->
27    <uses-feature
27-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:18:5-87
28        android:name="android.hardware.camera.ar"
28-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:18:19-60
29        android:required="true" />
29-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:18:61-84
30    <!-- Camera hardware feature -->
31    <uses-feature
31-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:20:5-85
32        android:name="android.hardware.camera"
32-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:20:19-57
33        android:required="false" />
33-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:20:58-82
34
35    <queries>
35-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:22:5-24:15
36        <package android:name="com.google.ar.core" />
36-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:23:9-54
36-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:23:18-51
37        <package android:name="com.android.vending" />
37-->[com.google.ar:core:1.48.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d3a9e4a1ac0a7dfaa2a05d1d85bb1b7\transformed\core-1.48.0\AndroidManifest.xml:22:9-55
37-->[com.google.ar:core:1.48.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d3a9e4a1ac0a7dfaa2a05d1d85bb1b7\transformed\core-1.48.0\AndroidManifest.xml:22:18-52
38
39        <intent>
39-->[com.google.ar:core:1.48.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d3a9e4a1ac0a7dfaa2a05d1d85bb1b7\transformed\core-1.48.0\AndroidManifest.xml:24:9-26:18
40            <action android:name="com.google.android.play.core.install.BIND_INSTALL_SERVICE" />
40-->[com.google.ar:core:1.48.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d3a9e4a1ac0a7dfaa2a05d1d85bb1b7\transformed\core-1.48.0\AndroidManifest.xml:25:13-96
40-->[com.google.ar:core:1.48.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d3a9e4a1ac0a7dfaa2a05d1d85bb1b7\transformed\core-1.48.0\AndroidManifest.xml:25:21-93
41        </intent>
42    </queries>
43
44    <uses-permission android:name="android.permission.INTERNET" />
44-->[com.google.ai.client.generativeai:generativeai:0.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3c36383787c59533e49319a11bb550a\transformed\generativeai-0.9.0\AndroidManifest.xml:22:5-67
44-->[com.google.ai.client.generativeai:generativeai:0.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3c36383787c59533e49319a11bb550a\transformed\generativeai-0.9.0\AndroidManifest.xml:22:22-64
45
46    <permission
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38d3a933af6ee7c8873273933d358b25\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
47        android:name="com.deace.deacear.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38d3a933af6ee7c8873273933d358b25\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
48        android:protectionLevel="signature" />
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38d3a933af6ee7c8873273933d358b25\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
49
50    <uses-permission android:name="com.deace.deacear.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38d3a933af6ee7c8873273933d358b25\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38d3a933af6ee7c8873273933d358b25\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
51
52    <application
52-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:26:5-58:19
53        android:allowBackup="true"
53-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:27:9-35
54        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38d3a933af6ee7c8873273933d358b25\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
55        android:dataExtractionRules="@xml/data_extraction_rules"
55-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:28:9-65
56        android:debuggable="true"
57        android:extractNativeLibs="false"
58        android:fullBackupContent="@xml/backup_rules"
58-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:29:9-54
59        android:icon="@mipmap/ic_launcher"
59-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:30:9-43
60        android:label="@string/app_name"
60-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:31:9-41
61        android:roundIcon="@mipmap/ic_launcher_round"
61-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:32:9-54
62        android:supportsRtl="true"
62-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:33:9-35
63        android:testOnly="true"
64        android:theme="@style/Theme.DeaceAR" >
64-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:34:9-45
65
66        <!-- Required for ARCore -->
67        <meta-data
67-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:38:9-40:40
68            android:name="com.google.ar.core"
68-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:39:13-46
69            android:value="required" />
69-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:40:13-37
70
71        <activity
71-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:42:9-50:20
72            android:name="com.deace.deacear.SplashActivity"
72-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:43:13-43
73            android:exported="true"
73-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:44:13-36
74            android:theme="@style/Theme.SplashScreen" >
74-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:45:13-54
75            <intent-filter>
75-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:46:13-49:29
76                <action android:name="android.intent.action.MAIN" />
76-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:47:17-69
76-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:47:25-66
77
78                <category android:name="android.intent.category.LAUNCHER" />
78-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:48:17-77
78-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:48:27-74
79            </intent-filter>
80        </activity>
81        <activity
81-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:52:9-57:20
82            android:name="com.deace.deacear.MainActivity"
82-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:53:13-41
83            android:configChanges="orientation|screenSize"
83-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:55:13-59
84            android:exported="false"
84-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:54:13-37
85            android:theme="@style/Theme.DeaceAR" >
85-->C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml:56:13-49
86        </activity>
87        <activity
87-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1c2c4edfb41ee161dcd1248ce713ab0\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
88            android:name="androidx.compose.ui.tooling.PreviewActivity"
88-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1c2c4edfb41ee161dcd1248ce713ab0\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
89            android:exported="true" />
89-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1c2c4edfb41ee161dcd1248ce713ab0\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
90        <activity
90-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\678ddbea4e488a705d3a59ea742b9914\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
91            android:name="androidx.activity.ComponentActivity"
91-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\678ddbea4e488a705d3a59ea742b9914\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
92            android:exported="true" />
92-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\678ddbea4e488a705d3a59ea742b9914\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
93
94        <provider
94-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\340e93631b2af3e5164b76efc0a657e6\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
95            android:name="androidx.startup.InitializationProvider"
95-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\340e93631b2af3e5164b76efc0a657e6\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
96            android:authorities="com.deace.deacear.androidx-startup"
96-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\340e93631b2af3e5164b76efc0a657e6\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
97            android:exported="false" >
97-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\340e93631b2af3e5164b76efc0a657e6\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
98            <meta-data
98-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\340e93631b2af3e5164b76efc0a657e6\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
99                android:name="androidx.emoji2.text.EmojiCompatInitializer"
99-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\340e93631b2af3e5164b76efc0a657e6\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
100                android:value="androidx.startup" />
100-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\340e93631b2af3e5164b76efc0a657e6\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
101            <meta-data
101-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e90c633f1537cb1e5a77ac30442fd6b0\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
102-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e90c633f1537cb1e5a77ac30442fd6b0\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
103                android:value="androidx.startup" />
103-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e90c633f1537cb1e5a77ac30442fd6b0\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
104            <meta-data
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
105                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
106                android:value="androidx.startup" />
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
107        </provider>
108
109        <receiver
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
110            android:name="androidx.profileinstaller.ProfileInstallReceiver"
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
111            android:directBootAware="false"
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
112            android:enabled="true"
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
113            android:exported="true"
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
114            android:permission="android.permission.DUMP" >
114-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
115            <intent-filter>
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
116                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
117            </intent-filter>
118            <intent-filter>
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
119                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
120            </intent-filter>
121            <intent-filter>
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
122                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
123            </intent-filter>
124            <intent-filter>
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
125                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
126            </intent-filter>
127        </receiver> <!-- The minimal version code of ARCore APK required for an app using this SDK. -->
128        <meta-data
128-->[com.google.ar:core:1.48.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d3a9e4a1ac0a7dfaa2a05d1d85bb1b7\transformed\core-1.48.0\AndroidManifest.xml:32:9-34:41
129            android:name="com.google.ar.core.min_apk_version"
129-->[com.google.ar:core:1.48.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d3a9e4a1ac0a7dfaa2a05d1d85bb1b7\transformed\core-1.48.0\AndroidManifest.xml:33:13-62
130            android:value="250210000" /> <!-- This activity is critical for installing ARCore when it is not already present. -->
130-->[com.google.ar:core:1.48.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d3a9e4a1ac0a7dfaa2a05d1d85bb1b7\transformed\core-1.48.0\AndroidManifest.xml:34:13-38
131        <activity
131-->[com.google.ar:core:1.48.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d3a9e4a1ac0a7dfaa2a05d1d85bb1b7\transformed\core-1.48.0\AndroidManifest.xml:36:9-42:80
132            android:name="com.google.ar.core.InstallActivity"
132-->[com.google.ar:core:1.48.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d3a9e4a1ac0a7dfaa2a05d1d85bb1b7\transformed\core-1.48.0\AndroidManifest.xml:37:13-62
133            android:configChanges="keyboardHidden|orientation|screenSize"
133-->[com.google.ar:core:1.48.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d3a9e4a1ac0a7dfaa2a05d1d85bb1b7\transformed\core-1.48.0\AndroidManifest.xml:38:13-74
134            android:excludeFromRecents="true"
134-->[com.google.ar:core:1.48.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d3a9e4a1ac0a7dfaa2a05d1d85bb1b7\transformed\core-1.48.0\AndroidManifest.xml:39:13-46
135            android:exported="false"
135-->[com.google.ar:core:1.48.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d3a9e4a1ac0a7dfaa2a05d1d85bb1b7\transformed\core-1.48.0\AndroidManifest.xml:40:13-37
136            android:launchMode="singleTop"
136-->[com.google.ar:core:1.48.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d3a9e4a1ac0a7dfaa2a05d1d85bb1b7\transformed\core-1.48.0\AndroidManifest.xml:41:13-43
137            android:theme="@android:style/Theme.Material.Light.Dialog.Alert" />
137-->[com.google.ar:core:1.48.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d3a9e4a1ac0a7dfaa2a05d1d85bb1b7\transformed\core-1.48.0\AndroidManifest.xml:42:13-77
138    </application>
139
140</manifest>
