<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Custom SeekBar style with white progress and thumb -->
    <style name="WhiteSeekBar" parent="Widget.AppCompat.SeekBar">
        <item name="android:progressTint">@android:color/white</item>
        <item name="android:thumbTint">@android:color/white</item>
        <item name="android:progressBackgroundTint">@android:color/darker_gray</item>
    </style>

    <!-- Custom Switch style with white thumb and track -->
    <style name="WhiteSwitch" parent="Widget.AppCompat.CompoundButton.Switch">
        <item name="colorControlActivated">@android:color/white</item>
        <item name="android:colorControlActivated">@android:color/white</item>
        <item name="android:thumbTint">@android:color/white</item>
        <item name="android:trackTint">@android:color/white</item>
    </style>
</resources>
