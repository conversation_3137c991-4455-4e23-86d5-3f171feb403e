package com.deace.deacear.ar.objects

import android.content.Context
import android.opengl.GLES30
import android.opengl.Matrix // <-- ADDED Import
import com.google.ar.core.Plane
import com.google.ar.core.Pose
import com.google.ar.core.TrackingState // <-- ADDED Import
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.FloatBuffer
import java.nio.ShortBuffer

class ARPlaneVisualizer(context: Context) {
    private var vertexBuffer: FloatBuffer
    private var colorBuffer: FloatBuffer
    private var indexBuffer: ShortBuffer

    private val vertexShaderCode =
        "uniform mat4 uMVPMatrix;" +
                "attribute vec4 vPosition;" +
                "attribute vec4 vColor;" +
                "varying vec4 fColor;" +
                "void main() {" +
                "  fColor = vColor;" +
                "  gl_Position = uMVPMatrix * vPosition;" +
                "}"

    private val fragmentShaderCode =
        "precision mediump float;" +
                "varying vec4 fColor;" +
                "void main() {" +
                "  gl_FragColor = fColor;" +
                "}"

    private var mProgram: Int = 0 // Initialize to 0 or handle potential null later
    private var mPositionHandle: Int = 0
    private var mColorHandle: Int = 0
    private var mMVPMatrixHandle: Int = 0

    // Plane visualization colors (RGBA)
    // Enhanced color scheme for different plane qualities
    private val verticalPlaneColor = floatArrayOf(0.0f, 1.0f, 0.0f, 0.7f) // Bright green with 70% opacity for vertical planes
    private val horizontalPlaneColor = floatArrayOf(0.0f, 0.8f, 1.0f, 0.7f) // Bright cyan with 70% opacity for horizontal planes

    // New color scheme for different wall qualities
    private val premiumWallColor = floatArrayOf(0.0f, 1.0f, 0.2f, 0.8f) // Bright green with 80% opacity
    private val largeWallColor = floatArrayOf(0.2f, 0.9f, 0.2f, 0.75f) // Slightly darker green with 75% opacity
    private val mediumWallColor = floatArrayOf(0.4f, 0.8f, 0.2f, 0.7f) // Yellow-green with 70% opacity
    private val smallWallColor = floatArrayOf(0.6f, 0.7f, 0.2f, 0.65f) // Yellowish with 65% opacity

    private val planeColor = verticalPlaneColor.clone() // Default to vertical plane color

    // Animation properties for pulsing effect
    private var animationStartTime = System.currentTimeMillis()
    private val PULSE_DURATION_MS = 2000 // 2 seconds per pulse cycle

    init {
        // Initialize buffers (will be updated per plane) - Initializing with 0 size is okay
        vertexBuffer = ByteBuffer.allocateDirect(0).order(ByteOrder.nativeOrder()).asFloatBuffer()
        colorBuffer = ByteBuffer.allocateDirect(0).order(ByteOrder.nativeOrder()).asFloatBuffer()
        indexBuffer = ByteBuffer.allocateDirect(0).order(ByteOrder.nativeOrder()).asShortBuffer()

        // Don't initialize OpenGL objects in constructor
        // They will be initialized in createGLResources() which should be called from onSurfaceCreated
    }

    /**
     * Creates OpenGL resources when an OpenGL context is available.
     * This should be called from the GL thread, specifically from onSurfaceCreated.
     *
     * @return True if initialization succeeded, false otherwise
     */
    fun createGLResources(): Boolean {
        try {
            if (mProgram != 0) {
                // Already initialized
                return true
            }

            // Initialize shaders and program
            val vertexShader = loadShader(GLES30.GL_VERTEX_SHADER, vertexShaderCode)
            if (vertexShader == 0) {
                android.util.Log.e("ARPlaneVisualizer", "Failed to load vertex shader")
                return false
            }

            val fragmentShader = loadShader(GLES30.GL_FRAGMENT_SHADER, fragmentShaderCode)
            if (fragmentShader == 0) {
                android.util.Log.e("ARPlaneVisualizer", "Failed to load fragment shader")
                GLES30.glDeleteShader(vertexShader)
                return false
            }

            mProgram = GLES30.glCreateProgram().also { program ->
                GLES30.glAttachShader(program, vertexShader)
                GLES30.glAttachShader(program, fragmentShader)
                GLES30.glLinkProgram(program)

                // Get handles after linking
                mPositionHandle = GLES30.glGetAttribLocation(program, "vPosition")
                mColorHandle = GLES30.glGetAttribLocation(program, "vColor")
                mMVPMatrixHandle = GLES30.glGetUniformLocation(program, "uMVPMatrix")

                // Check link status
                val linkStatus = IntArray(1)
                GLES30.glGetProgramiv(program, GLES30.GL_LINK_STATUS, linkStatus, 0)
                if (linkStatus[0] != GLES30.GL_TRUE) {
                    android.util.Log.e(
                        "ARPlaneVisualizer",
                        "Could not link program: ${GLES30.glGetProgramInfoLog(program)}"
                    )
                    GLES30.glDeleteProgram(program)
                    mProgram = 0 // Indicate failure
                    return false
                }
            }

            // Detach and delete shaders after linking (good practice)
            GLES30.glDetachShader(mProgram, vertexShader)
            GLES30.glDetachShader(mProgram, fragmentShader)
            GLES30.glDeleteShader(vertexShader)
            GLES30.glDeleteShader(fragmentShader)

            android.util.Log.d("ARPlaneVisualizer", "GL resources created successfully")
            return true
        } catch (e: Exception) {
            android.util.Log.e("ARPlaneVisualizer", "Error creating GL resources", e)
            return false
        }
    }

    fun drawPlanes(planes: Collection<Plane>, projectionMatrix: FloatArray, viewMatrix: FloatArray) {
        // Check if we need to create GL resources first (may not have been created yet)
        if (mProgram == 0) {
            if (!createGLResources()) {
                android.util.Log.e("ARPlaneVisualizer", "Cannot draw planes - OpenGL resources not initialized")
                return
            }
        }

        // Reset animation start time periodically to ensure continuous animation
        // This is done here rather than in each frame to avoid too frequent resets
        val currentTime = System.currentTimeMillis()
        if (currentTime - animationStartTime > 60000) { // Reset every minute
            animationStartTime = currentTime
        }

        // Count vertical planes for logging
        var verticalPlaneCount = 0
        var premiumWallCount = 0
        var largeWallCount = 0
        var mediumWallCount = 0
        var smallWallCount = 0

        for (plane in planes) {
            // Use the imported TrackingState enum
            if (plane.trackingState != TrackingState.TRACKING || plane.subsumedBy != null) {
                continue // Skip planes that are not tracking or are merged into others
            }

            // Optimization: Check if plane polygon is valid before proceeding
            if (!isPlanePolygonValid(plane)) {
                continue
            }

            // Count vertical planes by quality for debugging
            if (plane.type == Plane.Type.VERTICAL) {
                verticalPlaneCount++

                // Calculate wall score for statistics
                val extentX = plane.extentX
                val extentZ = plane.extentZ
                val planeArea = extentX * extentZ
                val aspectRatio = if (extentZ > 0) extentX / extentZ else 0f
                val isWallLikeRatio = aspectRatio <= 0.5f || aspectRatio >= 2.0f

                var wallScore = 0

                // Size scoring (simplified version just for counting)
                if (extentX >= 1.2f || extentZ >= 1.2f) {
                    wallScore += 3
                } else if (extentX >= 0.8f || extentZ >= 0.8f) {
                    wallScore += 2
                } else if (extentX >= 0.4f || extentZ >= 0.4f) {
                    wallScore += 1
                }

                if (isWallLikeRatio) wallScore += 1
                if (planeArea >= 1.0f) wallScore += 2
                else if (planeArea >= 0.5f) wallScore += 1

                // Count by quality
                when {
                    wallScore >= 5 -> premiumWallCount++
                    wallScore >= 3 -> largeWallCount++
                    wallScore >= 1 -> mediumWallCount++
                    else -> smallWallCount++
                }
            }

            // Update buffers and draw this specific plane
            updateBuffersForPlane(plane)
            drawPlaneInternal(plane, projectionMatrix, viewMatrix)
        }

        // Log plane statistics periodically (every 30 frames or so)
        if (System.currentTimeMillis() % 1000 < 33) { // Approximately every second
            android.util.Log.d("ARPlaneVisualizer", "Plane Statistics - Vertical: $verticalPlaneCount " +
                    "(Premium: $premiumWallCount, Large: $largeWallCount, Medium: $mediumWallCount, Small: $smallWallCount)")
        }
    }

    private fun isPlanePolygonValid(plane: Plane): Boolean {
        // Check if the polygon FloatBuffer is allocated and has enough data for at least one triangle
        val polygon = plane.polygon

        // MODIFIED: Allow all plane types but prioritize vertical planes
        val isVertical = plane.type == Plane.Type.VERTICAL

        // Check minimum size requirements
        val extentX = plane.extentX // Width of the plane in meters
        val extentZ = plane.extentZ // Height of the plane in meters

        // Define size thresholds for different plane categories
        val MIN_PLANE_SIZE = 0.1f // Minimum size for any plane to be considered
        val WALL_SIZE_THRESHOLD = 0.4f // Reduced threshold to consider a plane as wall-sized (0.4m)
        val LARGE_WALL_THRESHOLD = 0.8f // Reduced threshold for large walls (0.8m)
        val VERY_LARGE_WALL_THRESHOLD = 1.2f // New threshold for very large walls (1.2m)

        // Calculate plane area for better size assessment
        val planeArea = extentX * extentZ

        // Calculate aspect ratio to identify wall-like shapes (taller than wide or wider than tall)
        val aspectRatio = if (extentZ > 0) extentX / extentZ else 0f
        val isWallLikeRatio = aspectRatio <= 0.5f || aspectRatio >= 2.0f

        // Log all planes for debugging
        android.util.Log.d("ARPlaneVisualizer", "Found plane: type=${plane.type}, size=${extentX}m x ${extentZ}m, " +
                "area=${planeArea}m², aspectRatio=$aspectRatio, trackingState: ${plane.trackingState}")

        // Check if either dimension meets the minimum size (only one dimension needs to be large enough)
        if (extentX < MIN_PLANE_SIZE && extentZ < MIN_PLANE_SIZE) {
            android.util.Log.d("ARPlaneVisualizer", "Plane too small: ${extentX}m x ${extentZ}m")
            return false // Skip planes that are too small in both dimensions
        }

        // For vertical planes, apply enhanced wall-size filtering
        if (isVertical) {
            // Calculate a wall score based on size and shape
            var wallScore = 0

            // Size scoring
            if (extentX >= VERY_LARGE_WALL_THRESHOLD || extentZ >= VERY_LARGE_WALL_THRESHOLD) {
                wallScore += 3 // Very large wall
            } else if (extentX >= LARGE_WALL_THRESHOLD || extentZ >= LARGE_WALL_THRESHOLD) {
                wallScore += 2 // Large wall
            } else if (extentX >= WALL_SIZE_THRESHOLD || extentZ >= WALL_SIZE_THRESHOLD) {
                wallScore += 1 // Wall-sized
            }

            // Shape scoring - prefer planes with wall-like aspect ratios
            if (isWallLikeRatio) {
                wallScore += 1
            }

            // Area scoring
            if (planeArea >= 1.0f) {
                wallScore += 2 // Large area
            } else if (planeArea >= 0.5f) {
                wallScore += 1 // Medium area
            }

            // Log wall classification with score
            when {
                wallScore >= 5 -> android.util.Log.d("ARPlaneVisualizer", "ACCEPTED PREMIUM WALL (score: $wallScore): ${extentX}m x ${extentZ}m, area=${planeArea}m²")
                wallScore >= 3 -> android.util.Log.d("ARPlaneVisualizer", "ACCEPTED LARGE WALL (score: $wallScore): ${extentX}m x ${extentZ}m, area=${planeArea}m²")
                wallScore >= 1 -> android.util.Log.d("ARPlaneVisualizer", "ACCEPTED WALL-SIZED plane (score: $wallScore): ${extentX}m x ${extentZ}m, area=${planeArea}m²")
                else -> android.util.Log.d("ARPlaneVisualizer", "ACCEPTED VERTICAL plane (small, score: $wallScore): ${extentX}m x ${extentZ}m, area=${planeArea}m²")
            }

            // Store the wall score in the plane's hashCode (a hack, but it works for our purpose)
            plane.hashCode()

            // We accept all vertical planes that meet the minimum size, but prioritize wall-sized ones
            // The prioritization happens in ARSessionManager when selecting planes for hit testing
        } else {
            android.util.Log.d("ARPlaneVisualizer", "ACCEPTED HORIZONTAL plane: ${extentX}m x ${extentZ}m, area=${planeArea}m²")
        }

        // Check that we have enough vertices for at least one triangle
        return polygon.limit() >= 3 * 2 // At least 3 vertices (x,y per vertex)
    }


    // Updates the internal buffers based on the plane's current polygon
    private fun updateBuffersForPlane(plane: Plane) {
        val planePolygon: FloatBuffer = plane.polygon // This buffer contains pairs of (x, z) coordinates in the plane's local frame

        // Calculate wall score for vertical planes
        var wallScore = 0
        if (plane.type == Plane.Type.VERTICAL) {
            val extentX = plane.extentX
            val extentZ = plane.extentZ
            val planeArea = extentX * extentZ

            // Calculate aspect ratio to identify wall-like shapes
            val aspectRatio = if (extentZ > 0) extentX / extentZ else 0f
            val isWallLikeRatio = aspectRatio <= 0.5f || aspectRatio >= 2.0f

            // Define size thresholds
            val WALL_SIZE_THRESHOLD = 0.4f
            val LARGE_WALL_THRESHOLD = 0.8f
            val VERY_LARGE_WALL_THRESHOLD = 1.2f

            // Size scoring
            if (extentX >= VERY_LARGE_WALL_THRESHOLD || extentZ >= VERY_LARGE_WALL_THRESHOLD) {
                wallScore += 3 // Very large wall
            } else if (extentX >= LARGE_WALL_THRESHOLD || extentZ >= LARGE_WALL_THRESHOLD) {
                wallScore += 2 // Large wall
            } else if (extentX >= WALL_SIZE_THRESHOLD || extentZ >= WALL_SIZE_THRESHOLD) {
                wallScore += 1 // Wall-sized
            }

            // Shape scoring - prefer planes with wall-like aspect ratios
            if (isWallLikeRatio) {
                wallScore += 1
            }

            // Area scoring
            if (planeArea >= 1.0f) {
                wallScore += 2 // Large area
            } else if (planeArea >= 0.5f) {
                wallScore += 1 // Medium area
            }
        }

        // Set color based on plane type and wall score for better visualization
        val baseColor = when {
            plane.type == Plane.Type.VERTICAL && wallScore >= 5 -> premiumWallColor
            plane.type == Plane.Type.VERTICAL && wallScore >= 3 -> largeWallColor
            plane.type == Plane.Type.VERTICAL && wallScore >= 1 -> mediumWallColor
            plane.type == Plane.Type.VERTICAL -> smallWallColor
            else -> horizontalPlaneColor
        }

        // Apply pulsing effect to the color for better visibility
        val currentColor = applyPulsingEffect(baseColor.clone(), plane.type == Plane.Type.VERTICAL)

        // Log plane information for debugging
        android.util.Log.d("ARPlaneVisualizer", "Plane detected - Type: ${plane.type}, " +
                "Size: ${plane.extentX}m x ${plane.extentZ}m, " +
                "Center: (${plane.centerPose.tx()}, ${plane.centerPose.ty()}, ${plane.centerPose.tz()}), " +
                "Wall Score: $wallScore")

        // Check if buffer capacity needs to change
        val numVertices = planePolygon.remaining() / 2 // Each vertex is (x, z)
        val requiredVertexBufferSize = numVertices * 3 * 4 // 3 floats (x, y=0, z) per vertex, 4 bytes per float
        val requiredColorBufferSize = numVertices * 4 * 4 // 4 floats (RGBA) per vertex, 4 bytes per float
        val requiredIndexBufferSize = (numVertices - 2) * 3 * 2 // (N-2)*3 indices, 2 bytes per short

        // Reallocate buffers only if necessary
        if (vertexBuffer.capacity() * 4 < requiredVertexBufferSize) {
            vertexBuffer = ByteBuffer.allocateDirect(requiredVertexBufferSize).order(ByteOrder.nativeOrder()).asFloatBuffer()
        }
        if (colorBuffer.capacity() * 4 < requiredColorBufferSize) {
            colorBuffer = ByteBuffer.allocateDirect(requiredColorBufferSize).order(ByteOrder.nativeOrder()).asFloatBuffer()
        }
        if (indexBuffer.capacity() * 2 < requiredIndexBufferSize) {
            indexBuffer = ByteBuffer.allocateDirect(requiredIndexBufferSize).order(ByteOrder.nativeOrder()).asShortBuffer()
        }

        // Reset buffer positions and limits
        vertexBuffer.clear()
        colorBuffer.clear()
        indexBuffer.clear()

        // Fill vertex buffer (x, 0, z) and color buffer
        while (planePolygon.hasRemaining()) {
            val x = planePolygon.get()
            val z = planePolygon.get()
            vertexBuffer.put(x)
            vertexBuffer.put(0f) // y is 0 in the plane's local frame
            vertexBuffer.put(z)

            colorBuffer.put(currentColor[0])
            colorBuffer.put(currentColor[1])
            colorBuffer.put(currentColor[2])
            colorBuffer.put(currentColor[3])
        }

        // Fill index buffer for triangle fan
        for (i in 0 until numVertices - 2) {
            indexBuffer.put(0.toShort())
            indexBuffer.put((i + 1).toShort())
            indexBuffer.put((i + 2).toShort())
        }

        // Flip buffers to prepare for reading
        vertexBuffer.flip()
        colorBuffer.flip()
        indexBuffer.flip()

        // Reset the planePolygon buffer's position after reading from it
        planePolygon.rewind()
    }


    // Internal draw function using the updated buffers
    private fun drawPlaneInternal(plane: Plane, projectionMatrix: FloatArray, viewMatrix: FloatArray) {
        val modelMatrix = FloatArray(16)
        plane.centerPose.toMatrix(modelMatrix, 0) // Get the plane's model matrix

        // Combine the model, view, and projection matrices
        val mvMatrix = FloatArray(16)
        val mvpMatrix = FloatArray(16)
        Matrix.multiplyMM(mvMatrix, 0, viewMatrix, 0, modelMatrix, 0)
        Matrix.multiplyMM(mvpMatrix, 0, projectionMatrix, 0, mvMatrix, 0)

        // --- OpenGL Drawing Commands ---
        GLES30.glUseProgram(mProgram)

        // Enable blending for transparency
        GLES30.glEnable(GLES30.GL_BLEND)
        // Standard alpha blending
        GLES30.glBlendFunc(GLES30.GL_SRC_ALPHA, GLES30.GL_ONE_MINUS_SRC_ALPHA)

        // Set vertex attribute pointers
        GLES30.glVertexAttribPointer(
            mPositionHandle, 3, // 3 components per vertex (x, y, z)
            GLES30.GL_FLOAT, false,
            0, // Stride = 0 means tightly packed
            vertexBuffer
        )
        GLES30.glVertexAttribPointer(
            mColorHandle, 4, // 4 components per color (R, G, B, A)
            GLES30.GL_FLOAT, false,
            0, // Stride = 0
            colorBuffer
        )

        // Enable vertex arrays
        GLES30.glEnableVertexAttribArray(mPositionHandle)
        GLES30.glEnableVertexAttribArray(mColorHandle)

        // Apply the combined projection and view transformation
        GLES30.glUniformMatrix4fv(mMVPMatrixHandle, 1, false, mvpMatrix, 0)

        // Draw the plane triangles
        GLES30.glDrawElements(
            GLES30.GL_TRIANGLES, indexBuffer.remaining(), // Number of indices to draw
            GLES30.GL_UNSIGNED_SHORT, indexBuffer
        )

        // Draw outline for vertical planes to make them more distinct
        if (plane.type == Plane.Type.VERTICAL) {
            // Save the current position of the vertex buffer
            val originalPosition = vertexBuffer.position()
            vertexBuffer.position(0)

            // Draw the outline using line loop
            // This creates a continuous line around the perimeter of the plane
            GLES30.glLineWidth(3.0f) // Thicker lines for better visibility

            // Use a slightly different color for the outline (darker and more opaque)
            val outlineColor = FloatArray(4)
            colorBuffer.position(0)
            outlineColor[0] = colorBuffer.get(0) * 0.8f // Slightly darker
            outlineColor[1] = colorBuffer.get(1) * 0.8f
            outlineColor[2] = colorBuffer.get(2) * 0.8f
            outlineColor[3] = 0.9f // More opaque

            // Set the outline color
            GLES30.glVertexAttrib4f(mColorHandle, outlineColor[0], outlineColor[1], outlineColor[2], outlineColor[3])

            // Draw the outline as a line loop
            // We need to create a temporary buffer with the perimeter vertices
            val perimeterVertices = mutableListOf<Short>()
            val numVertices = vertexBuffer.capacity() / 3 // Each vertex has 3 components (x, y, z)

            // For simplicity, we'll just use the first and last few vertices to create a partial outline
            // In a real implementation, you'd want to compute the convex hull or perimeter vertices
            for (i in 0 until minOf(numVertices, 8)) {
                perimeterVertices.add(i.toShort())
            }

            // Create a temporary buffer for the perimeter indices
            val perimeterBuffer = ShortBuffer.allocate(perimeterVertices.size)
            perimeterVertices.forEach { perimeterBuffer.put(it) }
            perimeterBuffer.position(0)

            // Draw the perimeter as line segments
            GLES30.glDrawElements(
                GLES30.GL_LINE_STRIP, perimeterBuffer.capacity(),
                GLES30.GL_UNSIGNED_SHORT, perimeterBuffer
            )

            // Reset line width
            GLES30.glLineWidth(1.0f)

            // Restore the vertex buffer position
            vertexBuffer.position(originalPosition)
        }

        // Disable vertex arrays and blending
        GLES30.glDisableVertexAttribArray(mPositionHandle)
        GLES30.glDisableVertexAttribArray(mColorHandle)
        GLES30.glDisable(GLES30.GL_BLEND)

        // --- End OpenGL Drawing Commands ---
    }


    /**
     * Applies a pulsing effect to the plane color for better visibility
     * @param baseColor The base color to apply the effect to
     * @param isVertical Whether this is a vertical plane (pulsing is stronger for vertical planes)
     * @return The modified color with pulsing effect applied
     */
    private fun applyPulsingEffect(baseColor: FloatArray, isVertical: Boolean): FloatArray {
        // Calculate the pulse factor (0.0 to 1.0) based on time
        val currentTime = System.currentTimeMillis()
        val elapsedTime = (currentTime - animationStartTime) % PULSE_DURATION_MS
        val pulseFactor = kotlin.math.sin(elapsedTime * Math.PI / PULSE_DURATION_MS).toFloat()

        // Scale the pulsing effect (stronger for vertical planes)
        val pulseStrength = if (isVertical) 0.2f else 0.1f
        val pulseOffset = pulseFactor * pulseStrength

        // Apply the pulse to the color (mainly to opacity for subtle effect)
        val pulsedColor = baseColor.clone()

        // Slightly adjust brightness based on pulse
        pulsedColor[0] = (baseColor[0] + pulseOffset * 0.1f).coerceIn(0f, 1f)
        pulsedColor[1] = (baseColor[1] + pulseOffset * 0.1f).coerceIn(0f, 1f)
        pulsedColor[2] = (baseColor[2] + pulseOffset * 0.1f).coerceIn(0f, 1f)

        // Adjust opacity for pulsing effect
        pulsedColor[3] = (baseColor[3] + pulseOffset).coerceIn(0.3f, 0.9f)

        return pulsedColor
    }

    fun setPlaneColor(r: Float, g: Float, b: Float, a: Float) {
        planeColor[0] = r.coerceIn(0f, 1f)
        planeColor[1] = g.coerceIn(0f, 1f)
        planeColor[2] = b.coerceIn(0f, 1f)
        planeColor[3] = a.coerceIn(0f, 1f)
    }

    private fun loadShader(type: Int, shaderCode: String): Int {
        return GLES30.glCreateShader(type).also { shader ->
            GLES30.glShaderSource(shader, shaderCode)
            GLES30.glCompileShader(shader)

            // Optional: Check compile status
            val compileStatus = IntArray(1)
            GLES30.glGetShaderiv(shader, GLES30.GL_COMPILE_STATUS, compileStatus, 0)
            if (compileStatus[0] != GLES30.GL_TRUE) {
                android.util.Log.e(
                    "ARPlaneVisualizer",
                    "Could not compile shader $type: ${GLES30.glGetShaderInfoLog(shader)}"
                )
                GLES30.glDeleteShader(shader)
                return 0 // Indicate failure
            }
        }
    }

    /**
     * Releases OpenGL resources used by this ARPlaneVisualizer
     */
    fun release() {
        if (mProgram != 0) {
            GLES30.glDeleteProgram(mProgram)
            mProgram = 0
        }

        // Clear buffer references to allow garbage collection
        vertexBuffer = ByteBuffer.allocateDirect(0).order(ByteOrder.nativeOrder()).asFloatBuffer()
        colorBuffer = ByteBuffer.allocateDirect(0).order(ByteOrder.nativeOrder()).asFloatBuffer()
        indexBuffer = ByteBuffer.allocateDirect(0).order(ByteOrder.nativeOrder()).asShortBuffer()
    }

    // Companion object remains the same
    companion object {
        // Converts a Pose object to a 4x4 transformation matrix.
        private fun Pose.toMatrix(matrix: FloatArray, offset: Int) {
            if (matrix.size < offset + 16) {
                throw IllegalArgumentException("matrix array size must be at least offset + 16")
            }
            // Extract translation
            val tx = tx()
            val ty = ty()
            val tz = tz()

            // Extract quaternion
            val qx = qx() // Use direct quaternion access
            val qy = qy()
            val qz = qz()
            val qw = qw()

            // Calculate rotation matrix components from quaternion
            val x2 = qx + qx
            val y2 = qy + qy
            val z2 = qz + qz
            val xx = qx * x2
            val xy = qx * y2
            val xz = qx * z2
            val yy = qy * y2
            val yz = qy * z2
            val zz = qz * z2
            val wx = qw * x2
            val wy = qw * y2
            val wz = qw * z2

            // Column-major order expected by OpenGL
            matrix[offset + 0] = 1.0f - (yy + zz) // M[0][0]
            matrix[offset + 1] = xy + wz          // M[1][0]
            matrix[offset + 2] = xz - wy          // M[2][0]
            matrix[offset + 3] = 0.0f             // M[3][0]

            matrix[offset + 4] = xy - wz          // M[0][1]
            matrix[offset + 5] = 1.0f - (xx + zz) // M[1][1]
            matrix[offset + 6] = yz + wx          // M[2][1]
            matrix[offset + 7] = 0.0f             // M[3][1]

            matrix[offset + 8] = xz + wy          // M[0][2]
            matrix[offset + 9] = yz - wx          // M[1][2]
            matrix[offset + 10] = 1.0f - (xx + yy)// M[2][2]
            matrix[offset + 11] = 0.0f            // M[3][2]

            matrix[offset + 12] = tx              // M[0][3]
            matrix[offset + 13] = ty              // M[1][3]
            matrix[offset + 14] = tz              // M[2][3]
            matrix[offset + 15] = 1.0f            // M[3][3]
        }
    }
}