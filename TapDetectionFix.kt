/**
 * TapDetectionFix.kt
 * 
 * This file contains fixes for the tap detection issue in the DeaceAR app.
 * The main problem is that image loading works and green zones appear on walls,
 * but tapping doesn't place images.
 */

/**
 * Fix 1: Improved Flag Management
 * 
 * The issue might be related to the imageReadyToPlace flag not being properly
 * calculated or the isPlacingImage flag not being properly synchronized.
 * 
 * Replace the following code in ARSessionManager.kt:
 */

// Original code:
val imageReadyToPlace = !isImageFixed && currentImage != null

// Replace with:
val imageReadyToPlace = currentImage != null && !isImageFixed
Log.d("TapDebug", "==== TAP DETECTION STATE ====")
Log.d("TapDebug", "Thread: ${Thread.currentThread().name}")
Log.d("TapDebug", "Image ready to place: $imageReadyToPlace")
Log.d("TapDebug", "isImageFixed: $isImageFixed")
Log.d("TapDebug", "currentImage: ${currentImage != null}")
Log.d("TapDebug", "Vertical planes: $verticalPlanes")
Log.d("TapDebug", "Tracking planes: $trackingPlanes")

/**
 * Fix 2: Enhanced Hit Testing
 * 
 * The hit test might not be properly detecting vertical surfaces.
 * Modify the performHitTest method in ARSessionManager.kt:
 */

// Add this code at the beginning of the performHitTest method:
Log.d("TapDebug", "Performing hit test at (${x}, ${y})")
val allPlanes = frame.getUpdatedTrackables(Plane::class.java)
val verticalPlanes = allPlanes.count { 
    it.trackingState == TrackingState.TRACKING && it.type == Plane.Type.VERTICAL 
}
Log.d("TapDebug", "Available vertical planes for hit test: $verticalPlanes")

// Add this code after getting all hits:
Log.d("TapDebug", "Total hits found: ${allHits.size}")
val verticalHits = allHits.filter { 
    val trackable = it.trackable
    trackable is Plane && trackable.type == Plane.Type.VERTICAL 
}
Log.d("TapDebug", "Vertical hits found: ${verticalHits.size}")

/**
 * Fix 3: Improved Anchor Creation
 * 
 * Ensure the anchor is properly created and the image is updated.
 * Modify the code in processTapEvent where it creates the anchor:
 */

// Replace the anchor creation code with:
try {
    // Create a new anchor at the hit location
    imageAnchor = hitResult.createAnchor()
    Log.d("TapDebug", "Created anchor at (${imageAnchor!!.pose.tx()}, ${imageAnchor!!.pose.ty()}, ${imageAnchor!!.pose.tz()})")
    
    // Update image position to match anchor
    currentImage?.updateModelMatrix(imageAnchor!!.pose)
    Log.d("TapDebug", "Updated image model matrix with anchor pose")
    
    // Make sure the image is visible in the renderer - CRITICAL STEP
    arRenderer.setImageVisibility(true)
    Log.d("TapDebug", "Set image visibility to true in renderer")
    
    // Force a redraw to ensure the image appears immediately
    arRenderer.requestRender()
    Log.d("TapDebug", "Requested immediate render to show image")
    
    // Request another render after a short delay to ensure the image appears
    Handler(Looper.getMainLooper()).postDelayed({
        arRenderer.requestRender()
        Log.d("TapDebug", "Requested delayed render to ensure image appears")
    }, 100)
    
    // Notify listener that image was placed
    sessionListener?.onImagePlaced()
    Log.d("TapDebug", "Notified listener that image was placed")
    
    // Show success message
    Handler(Looper.getMainLooper()).post {
        MessageUtils.showToast(
            context,
            "Image placed on wall! You can adjust it with gestures.",
            Toast.LENGTH_LONG
        )
    }
} catch (e: Exception) {
    Log.e("TapDebug", "Error creating anchor: ${e.message}")
    Log.e("TapDebug", "Stack trace: ${e.stackTraceToString()}")
    
    // Reset the image anchor if it was created
    imageAnchor?.detach()
    imageAnchor = null
}

/**
 * Fix 4: Improved Renderer Integration
 * 
 * Ensure the ARRenderer properly handles the image visibility.
 * Add this method to ARRenderer.kt:
 */

/**
 * Ensures the image is properly set up and visible
 * @param image The image to display
 * @param pose The pose to position the image at
 * @return True if successful, false otherwise
 */
fun ensureImageSetup(image: ARImage, pose: Pose): Boolean {
    try {
        Log.d("TapDebug", "Ensuring image setup in renderer")
        
        // Set the image in the renderer
        setImage(image)
        
        // Update the image position
        image.updateModelMatrix(pose)
        
        // Ensure visibility
        setImageVisibility(true)
        
        // Request immediate render
        requestRender()
        
        // Verify setup was successful
        val success = hasActiveImage()
        Log.d("TapDebug", "Image setup successful: $success")
        return success
    } catch (e: Exception) {
        Log.e("TapDebug", "Error setting up image: ${e.message}")
        return false
    }
}

/**
 * Fix 5: Test Function
 * 
 * Add a test function to ARSessionManager.kt to help diagnose and fix the issue:
 */

/**
 * Test function to place an image on the closest vertical plane
 * This bypasses the normal tap detection pipeline
 */
fun testImagePlacement() {
    if (currentImage == null) {
        Log.d("TapDebug", "TEST: No image loaded")
        return
    }
    
    if (isImageFixed) {
        Log.d("TapDebug", "TEST: Image is already fixed")
        return
    }
    
    try {
        val frame = arSession.update()
        val cameraPose = frame.camera.pose
        
        // Find all vertical planes
        val verticalPlanes = frame.getUpdatedTrackables(Plane::class.java)
            .filter { it.trackingState == TrackingState.TRACKING && it.type == Plane.Type.VERTICAL }
        
        Log.d("TapDebug", "TEST: Found ${verticalPlanes.size} vertical planes")
        
        if (verticalPlanes.isEmpty()) {
            Log.d("TapDebug", "TEST: No vertical planes available")
            return
        }
        
        // Find the closest plane
        var closestPlane: Plane? = null
        var minDistance = Float.MAX_VALUE
        
        for (plane in verticalPlanes) {
            val distance = DistanceUtils.calculateDistance(
                cameraPose.tx(), cameraPose.ty(), cameraPose.tz(),
                plane.centerPose.tx(), plane.centerPose.ty(), plane.centerPose.tz()
            )
            
            if (distance < minDistance) {
                minDistance = distance
                closestPlane = plane
            }
        }
        
        if (closestPlane != null) {
            Log.d("TapDebug", "TEST: Found closest plane at distance $minDistance")
            
            // Create a position 2 meters in front of the camera
            val translation = floatArrayOf(0f, 0f, -2f)
            val rotation = floatArrayOf(0f, 0f, 0f, 1f)
            val pose = Pose(translation, rotation)
            
            // Create anchor on the closest plane
            imageAnchor?.detach()
            imageAnchor = closestPlane.createAnchor(closestPlane.centerPose)
            
            // Update image with anchor
            currentImage?.updateModelMatrix(imageAnchor!!.pose)
            
            // Update renderer
            arRenderer.setImageVisibility(true)
            arRenderer.requestRender()
            
            Log.d("TapDebug", "TEST: Image placed successfully on closest plane")
            
            // Notify listener
            sessionListener?.onImagePlaced()
            
            // Show toast
            Handler(Looper.getMainLooper()).post {
                MessageUtils.showToast(
                    context,
                    "TEST: Image placed on wall! You can adjust it with gestures.",
                    Toast.LENGTH_LONG
                )
            }
        }
    } catch (e: Exception) {
        Log.e("TapDebug", "TEST: Error placing image: ${e.message}")
    }
}
