// Generated by view binder compiler. Do not edit!
package com.deace.deacear.databinding;

import android.opengl.GLSurfaceView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatImageButton;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatSeekBar;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.SwitchCompat;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.deace.deacear.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final GLSurfaceView arSurfaceView;

  @NonNull
  public final LinearLayout configPanelLayout;

  @NonNull
  public final ScrollView configPanelScroll;

  @NonNull
  public final AppCompatImageButton configTabButton;

  @NonNull
  public final AppCompatButton confirmReinitButton;

  @NonNull
  public final AppCompatImageView deaceLogo;

  @NonNull
  public final LinearLayout deacePanelLayout;

  @NonNull
  public final Spinner defaultGridFormatSpinner;

  @NonNull
  public final AppCompatSeekBar defaultGridOpacitySeekbar;

  @NonNull
  public final AppCompatButton exportButton;

  @NonNull
  public final LinearLayout exportPanelLayout;

  @NonNull
  public final AppCompatImageButton exportTabButton;

  @NonNull
  public final RadioGroup fileFormatRadioGroup;

  @NonNull
  public final AppCompatButton fixGridButton;

  @NonNull
  public final AppCompatButton fixImageButton;

  @NonNull
  public final AppCompatSeekBar gridOpacitySeekbar;

  @NonNull
  public final AppCompatTextView gridOpacityValue;

  @NonNull
  public final LinearLayout gridPanelLayout;

  @NonNull
  public final AppCompatImageButton gridTabButton;

  @NonNull
  public final Button helpAboutButton;

  @NonNull
  public final AppCompatImageButton helpButton;

  @NonNull
  public final ImageButton horizontalFormatButton;

  @NonNull
  public final AppCompatSeekBar imageOpacitySeekbar;

  @NonNull
  public final AppCompatTextView imageOpacityValue;

  @NonNull
  public final LinearLayout imagePanelLayout;

  @NonNull
  public final RadioGroup imageQualityRadioGroup;

  @NonNull
  public final AppCompatTextView imageSelectedStatus;

  @NonNull
  public final AppCompatTextView imageSizeText;

  @NonNull
  public final AppCompatImageButton imageTabButton;

  @NonNull
  public final AppCompatImageView imageThumbnail;

  @NonNull
  public final RadioButton jpgFormatRadio;

  @NonNull
  public final AppCompatButton loadImageButton;

  @NonNull
  public final LinearLayout loadingLayout;

  @NonNull
  public final AppCompatTextView loadingText;

  @NonNull
  public final LinearLayout mainMenuLayout;

  @NonNull
  public final RadioButton maxQualityRadio;

  @NonNull
  public final LinearLayout menuIconsContainer;

  @NonNull
  public final RadioButton normalQualityRadio;

  @NonNull
  public final RadioButton pngFormatRadio;

  @NonNull
  public final SwitchCompat preserveAspectSwitch;

  @NonNull
  public final AppCompatButton quickLockImageButton;

  @NonNull
  public final AppCompatButton releaseImageButton;

  @NonNull
  public final Button resetSessionButton;

  @NonNull
  public final Button resetSettingsButton;

  @NonNull
  public final AppCompatImageView reticle;

  @NonNull
  public final SwitchCompat showPlanesSwitch;

  @NonNull
  public final ImageButton squareFormatButton;

  @NonNull
  public final AppCompatButton testImagePlacementButton;

  @NonNull
  public final LinearLayout trackingIndicatorContainer;

  @NonNull
  public final AppCompatTextView trackingInitText;

  @NonNull
  public final ProgressBar trackingProgressBar;

  @NonNull
  public final View trackingQualityIndicator;

  @NonNull
  public final AppCompatTextView trackingQualityText;

  @NonNull
  public final ImageButton verticalFormatButton;

  private ActivityMainBinding(@NonNull RelativeLayout rootView,
      @NonNull GLSurfaceView arSurfaceView, @NonNull LinearLayout configPanelLayout,
      @NonNull ScrollView configPanelScroll, @NonNull AppCompatImageButton configTabButton,
      @NonNull AppCompatButton confirmReinitButton, @NonNull AppCompatImageView deaceLogo,
      @NonNull LinearLayout deacePanelLayout, @NonNull Spinner defaultGridFormatSpinner,
      @NonNull AppCompatSeekBar defaultGridOpacitySeekbar, @NonNull AppCompatButton exportButton,
      @NonNull LinearLayout exportPanelLayout, @NonNull AppCompatImageButton exportTabButton,
      @NonNull RadioGroup fileFormatRadioGroup, @NonNull AppCompatButton fixGridButton,
      @NonNull AppCompatButton fixImageButton, @NonNull AppCompatSeekBar gridOpacitySeekbar,
      @NonNull AppCompatTextView gridOpacityValue, @NonNull LinearLayout gridPanelLayout,
      @NonNull AppCompatImageButton gridTabButton, @NonNull Button helpAboutButton,
      @NonNull AppCompatImageButton helpButton, @NonNull ImageButton horizontalFormatButton,
      @NonNull AppCompatSeekBar imageOpacitySeekbar, @NonNull AppCompatTextView imageOpacityValue,
      @NonNull LinearLayout imagePanelLayout, @NonNull RadioGroup imageQualityRadioGroup,
      @NonNull AppCompatTextView imageSelectedStatus, @NonNull AppCompatTextView imageSizeText,
      @NonNull AppCompatImageButton imageTabButton, @NonNull AppCompatImageView imageThumbnail,
      @NonNull RadioButton jpgFormatRadio, @NonNull AppCompatButton loadImageButton,
      @NonNull LinearLayout loadingLayout, @NonNull AppCompatTextView loadingText,
      @NonNull LinearLayout mainMenuLayout, @NonNull RadioButton maxQualityRadio,
      @NonNull LinearLayout menuIconsContainer, @NonNull RadioButton normalQualityRadio,
      @NonNull RadioButton pngFormatRadio, @NonNull SwitchCompat preserveAspectSwitch,
      @NonNull AppCompatButton quickLockImageButton, @NonNull AppCompatButton releaseImageButton,
      @NonNull Button resetSessionButton, @NonNull Button resetSettingsButton,
      @NonNull AppCompatImageView reticle, @NonNull SwitchCompat showPlanesSwitch,
      @NonNull ImageButton squareFormatButton, @NonNull AppCompatButton testImagePlacementButton,
      @NonNull LinearLayout trackingIndicatorContainer, @NonNull AppCompatTextView trackingInitText,
      @NonNull ProgressBar trackingProgressBar, @NonNull View trackingQualityIndicator,
      @NonNull AppCompatTextView trackingQualityText, @NonNull ImageButton verticalFormatButton) {
    this.rootView = rootView;
    this.arSurfaceView = arSurfaceView;
    this.configPanelLayout = configPanelLayout;
    this.configPanelScroll = configPanelScroll;
    this.configTabButton = configTabButton;
    this.confirmReinitButton = confirmReinitButton;
    this.deaceLogo = deaceLogo;
    this.deacePanelLayout = deacePanelLayout;
    this.defaultGridFormatSpinner = defaultGridFormatSpinner;
    this.defaultGridOpacitySeekbar = defaultGridOpacitySeekbar;
    this.exportButton = exportButton;
    this.exportPanelLayout = exportPanelLayout;
    this.exportTabButton = exportTabButton;
    this.fileFormatRadioGroup = fileFormatRadioGroup;
    this.fixGridButton = fixGridButton;
    this.fixImageButton = fixImageButton;
    this.gridOpacitySeekbar = gridOpacitySeekbar;
    this.gridOpacityValue = gridOpacityValue;
    this.gridPanelLayout = gridPanelLayout;
    this.gridTabButton = gridTabButton;
    this.helpAboutButton = helpAboutButton;
    this.helpButton = helpButton;
    this.horizontalFormatButton = horizontalFormatButton;
    this.imageOpacitySeekbar = imageOpacitySeekbar;
    this.imageOpacityValue = imageOpacityValue;
    this.imagePanelLayout = imagePanelLayout;
    this.imageQualityRadioGroup = imageQualityRadioGroup;
    this.imageSelectedStatus = imageSelectedStatus;
    this.imageSizeText = imageSizeText;
    this.imageTabButton = imageTabButton;
    this.imageThumbnail = imageThumbnail;
    this.jpgFormatRadio = jpgFormatRadio;
    this.loadImageButton = loadImageButton;
    this.loadingLayout = loadingLayout;
    this.loadingText = loadingText;
    this.mainMenuLayout = mainMenuLayout;
    this.maxQualityRadio = maxQualityRadio;
    this.menuIconsContainer = menuIconsContainer;
    this.normalQualityRadio = normalQualityRadio;
    this.pngFormatRadio = pngFormatRadio;
    this.preserveAspectSwitch = preserveAspectSwitch;
    this.quickLockImageButton = quickLockImageButton;
    this.releaseImageButton = releaseImageButton;
    this.resetSessionButton = resetSessionButton;
    this.resetSettingsButton = resetSettingsButton;
    this.reticle = reticle;
    this.showPlanesSwitch = showPlanesSwitch;
    this.squareFormatButton = squareFormatButton;
    this.testImagePlacementButton = testImagePlacementButton;
    this.trackingIndicatorContainer = trackingIndicatorContainer;
    this.trackingInitText = trackingInitText;
    this.trackingProgressBar = trackingProgressBar;
    this.trackingQualityIndicator = trackingQualityIndicator;
    this.trackingQualityText = trackingQualityText;
    this.verticalFormatButton = verticalFormatButton;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ar_surface_view;
      GLSurfaceView arSurfaceView = ViewBindings.findChildViewById(rootView, id);
      if (arSurfaceView == null) {
        break missingId;
      }

      id = R.id.config_panel_layout;
      LinearLayout configPanelLayout = ViewBindings.findChildViewById(rootView, id);
      if (configPanelLayout == null) {
        break missingId;
      }

      id = R.id.config_panel_scroll;
      ScrollView configPanelScroll = ViewBindings.findChildViewById(rootView, id);
      if (configPanelScroll == null) {
        break missingId;
      }

      id = R.id.config_tab_button;
      AppCompatImageButton configTabButton = ViewBindings.findChildViewById(rootView, id);
      if (configTabButton == null) {
        break missingId;
      }

      id = R.id.confirm_reinit_button;
      AppCompatButton confirmReinitButton = ViewBindings.findChildViewById(rootView, id);
      if (confirmReinitButton == null) {
        break missingId;
      }

      id = R.id.deace_logo;
      AppCompatImageView deaceLogo = ViewBindings.findChildViewById(rootView, id);
      if (deaceLogo == null) {
        break missingId;
      }

      id = R.id.deace_panel_layout;
      LinearLayout deacePanelLayout = ViewBindings.findChildViewById(rootView, id);
      if (deacePanelLayout == null) {
        break missingId;
      }

      id = R.id.default_grid_format_spinner;
      Spinner defaultGridFormatSpinner = ViewBindings.findChildViewById(rootView, id);
      if (defaultGridFormatSpinner == null) {
        break missingId;
      }

      id = R.id.default_grid_opacity_seekbar;
      AppCompatSeekBar defaultGridOpacitySeekbar = ViewBindings.findChildViewById(rootView, id);
      if (defaultGridOpacitySeekbar == null) {
        break missingId;
      }

      id = R.id.export_button;
      AppCompatButton exportButton = ViewBindings.findChildViewById(rootView, id);
      if (exportButton == null) {
        break missingId;
      }

      id = R.id.export_panel_layout;
      LinearLayout exportPanelLayout = ViewBindings.findChildViewById(rootView, id);
      if (exportPanelLayout == null) {
        break missingId;
      }

      id = R.id.export_tab_button;
      AppCompatImageButton exportTabButton = ViewBindings.findChildViewById(rootView, id);
      if (exportTabButton == null) {
        break missingId;
      }

      id = R.id.file_format_radio_group;
      RadioGroup fileFormatRadioGroup = ViewBindings.findChildViewById(rootView, id);
      if (fileFormatRadioGroup == null) {
        break missingId;
      }

      id = R.id.fix_grid_button;
      AppCompatButton fixGridButton = ViewBindings.findChildViewById(rootView, id);
      if (fixGridButton == null) {
        break missingId;
      }

      id = R.id.fix_image_button;
      AppCompatButton fixImageButton = ViewBindings.findChildViewById(rootView, id);
      if (fixImageButton == null) {
        break missingId;
      }

      id = R.id.grid_opacity_seekbar;
      AppCompatSeekBar gridOpacitySeekbar = ViewBindings.findChildViewById(rootView, id);
      if (gridOpacitySeekbar == null) {
        break missingId;
      }

      id = R.id.grid_opacity_value;
      AppCompatTextView gridOpacityValue = ViewBindings.findChildViewById(rootView, id);
      if (gridOpacityValue == null) {
        break missingId;
      }

      id = R.id.grid_panel_layout;
      LinearLayout gridPanelLayout = ViewBindings.findChildViewById(rootView, id);
      if (gridPanelLayout == null) {
        break missingId;
      }

      id = R.id.grid_tab_button;
      AppCompatImageButton gridTabButton = ViewBindings.findChildViewById(rootView, id);
      if (gridTabButton == null) {
        break missingId;
      }

      id = R.id.help_about_button;
      Button helpAboutButton = ViewBindings.findChildViewById(rootView, id);
      if (helpAboutButton == null) {
        break missingId;
      }

      id = R.id.help_button;
      AppCompatImageButton helpButton = ViewBindings.findChildViewById(rootView, id);
      if (helpButton == null) {
        break missingId;
      }

      id = R.id.horizontal_format_button;
      ImageButton horizontalFormatButton = ViewBindings.findChildViewById(rootView, id);
      if (horizontalFormatButton == null) {
        break missingId;
      }

      id = R.id.image_opacity_seekbar;
      AppCompatSeekBar imageOpacitySeekbar = ViewBindings.findChildViewById(rootView, id);
      if (imageOpacitySeekbar == null) {
        break missingId;
      }

      id = R.id.image_opacity_value;
      AppCompatTextView imageOpacityValue = ViewBindings.findChildViewById(rootView, id);
      if (imageOpacityValue == null) {
        break missingId;
      }

      id = R.id.image_panel_layout;
      LinearLayout imagePanelLayout = ViewBindings.findChildViewById(rootView, id);
      if (imagePanelLayout == null) {
        break missingId;
      }

      id = R.id.image_quality_radio_group;
      RadioGroup imageQualityRadioGroup = ViewBindings.findChildViewById(rootView, id);
      if (imageQualityRadioGroup == null) {
        break missingId;
      }

      id = R.id.image_selected_status;
      AppCompatTextView imageSelectedStatus = ViewBindings.findChildViewById(rootView, id);
      if (imageSelectedStatus == null) {
        break missingId;
      }

      id = R.id.image_size_text;
      AppCompatTextView imageSizeText = ViewBindings.findChildViewById(rootView, id);
      if (imageSizeText == null) {
        break missingId;
      }

      id = R.id.image_tab_button;
      AppCompatImageButton imageTabButton = ViewBindings.findChildViewById(rootView, id);
      if (imageTabButton == null) {
        break missingId;
      }

      id = R.id.image_thumbnail;
      AppCompatImageView imageThumbnail = ViewBindings.findChildViewById(rootView, id);
      if (imageThumbnail == null) {
        break missingId;
      }

      id = R.id.jpg_format_radio;
      RadioButton jpgFormatRadio = ViewBindings.findChildViewById(rootView, id);
      if (jpgFormatRadio == null) {
        break missingId;
      }

      id = R.id.load_image_button;
      AppCompatButton loadImageButton = ViewBindings.findChildViewById(rootView, id);
      if (loadImageButton == null) {
        break missingId;
      }

      id = R.id.loading_layout;
      LinearLayout loadingLayout = ViewBindings.findChildViewById(rootView, id);
      if (loadingLayout == null) {
        break missingId;
      }

      id = R.id.loading_text;
      AppCompatTextView loadingText = ViewBindings.findChildViewById(rootView, id);
      if (loadingText == null) {
        break missingId;
      }

      id = R.id.main_menu_layout;
      LinearLayout mainMenuLayout = ViewBindings.findChildViewById(rootView, id);
      if (mainMenuLayout == null) {
        break missingId;
      }

      id = R.id.max_quality_radio;
      RadioButton maxQualityRadio = ViewBindings.findChildViewById(rootView, id);
      if (maxQualityRadio == null) {
        break missingId;
      }

      id = R.id.menu_icons_container;
      LinearLayout menuIconsContainer = ViewBindings.findChildViewById(rootView, id);
      if (menuIconsContainer == null) {
        break missingId;
      }

      id = R.id.normal_quality_radio;
      RadioButton normalQualityRadio = ViewBindings.findChildViewById(rootView, id);
      if (normalQualityRadio == null) {
        break missingId;
      }

      id = R.id.png_format_radio;
      RadioButton pngFormatRadio = ViewBindings.findChildViewById(rootView, id);
      if (pngFormatRadio == null) {
        break missingId;
      }

      id = R.id.preserve_aspect_switch;
      SwitchCompat preserveAspectSwitch = ViewBindings.findChildViewById(rootView, id);
      if (preserveAspectSwitch == null) {
        break missingId;
      }

      id = R.id.quick_lock_image_button;
      AppCompatButton quickLockImageButton = ViewBindings.findChildViewById(rootView, id);
      if (quickLockImageButton == null) {
        break missingId;
      }

      id = R.id.release_image_button;
      AppCompatButton releaseImageButton = ViewBindings.findChildViewById(rootView, id);
      if (releaseImageButton == null) {
        break missingId;
      }

      id = R.id.reset_session_button;
      Button resetSessionButton = ViewBindings.findChildViewById(rootView, id);
      if (resetSessionButton == null) {
        break missingId;
      }

      id = R.id.reset_settings_button;
      Button resetSettingsButton = ViewBindings.findChildViewById(rootView, id);
      if (resetSettingsButton == null) {
        break missingId;
      }

      id = R.id.reticle;
      AppCompatImageView reticle = ViewBindings.findChildViewById(rootView, id);
      if (reticle == null) {
        break missingId;
      }

      id = R.id.show_planes_switch;
      SwitchCompat showPlanesSwitch = ViewBindings.findChildViewById(rootView, id);
      if (showPlanesSwitch == null) {
        break missingId;
      }

      id = R.id.square_format_button;
      ImageButton squareFormatButton = ViewBindings.findChildViewById(rootView, id);
      if (squareFormatButton == null) {
        break missingId;
      }

      id = R.id.test_image_placement_button;
      AppCompatButton testImagePlacementButton = ViewBindings.findChildViewById(rootView, id);
      if (testImagePlacementButton == null) {
        break missingId;
      }

      id = R.id.tracking_indicator_container;
      LinearLayout trackingIndicatorContainer = ViewBindings.findChildViewById(rootView, id);
      if (trackingIndicatorContainer == null) {
        break missingId;
      }

      id = R.id.tracking_init_text;
      AppCompatTextView trackingInitText = ViewBindings.findChildViewById(rootView, id);
      if (trackingInitText == null) {
        break missingId;
      }

      id = R.id.tracking_progress_bar;
      ProgressBar trackingProgressBar = ViewBindings.findChildViewById(rootView, id);
      if (trackingProgressBar == null) {
        break missingId;
      }

      id = R.id.tracking_quality_indicator;
      View trackingQualityIndicator = ViewBindings.findChildViewById(rootView, id);
      if (trackingQualityIndicator == null) {
        break missingId;
      }

      id = R.id.tracking_quality_text;
      AppCompatTextView trackingQualityText = ViewBindings.findChildViewById(rootView, id);
      if (trackingQualityText == null) {
        break missingId;
      }

      id = R.id.vertical_format_button;
      ImageButton verticalFormatButton = ViewBindings.findChildViewById(rootView, id);
      if (verticalFormatButton == null) {
        break missingId;
      }

      return new ActivityMainBinding((RelativeLayout) rootView, arSurfaceView, configPanelLayout,
          configPanelScroll, configTabButton, confirmReinitButton, deaceLogo, deacePanelLayout,
          defaultGridFormatSpinner, defaultGridOpacitySeekbar, exportButton, exportPanelLayout,
          exportTabButton, fileFormatRadioGroup, fixGridButton, fixImageButton, gridOpacitySeekbar,
          gridOpacityValue, gridPanelLayout, gridTabButton, helpAboutButton, helpButton,
          horizontalFormatButton, imageOpacitySeekbar, imageOpacityValue, imagePanelLayout,
          imageQualityRadioGroup, imageSelectedStatus, imageSizeText, imageTabButton,
          imageThumbnail, jpgFormatRadio, loadImageButton, loadingLayout, loadingText,
          mainMenuLayout, maxQualityRadio, menuIconsContainer, normalQualityRadio, pngFormatRadio,
          preserveAspectSwitch, quickLockImageButton, releaseImageButton, resetSessionButton,
          resetSettingsButton, reticle, showPlanesSwitch, squareFormatButton,
          testImagePlacementButton, trackingIndicatorContainer, trackingInitText,
          trackingProgressBar, trackingQualityIndicator, trackingQualityText, verticalFormatButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
