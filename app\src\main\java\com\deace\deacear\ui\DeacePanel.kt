package com.deace.deacear.ui

import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import android.widget.Toast
import com.deace.deacear.utils.MessageUtils
import com.deace.deacear.MainActivity
import com.deace.deacear.R

/**
 * Panel that appears when the Deace logo is clicked
 * Contains a button to restart the app
 */
class DeacePanel(
    private val panelLayout: LinearLayout,
    private val context: Context
) {
    private lateinit var confirmReinitButton: Button

    // Auto-hide timer
    private val AUTO_HIDE_DELAY_MS = 5000L // 5 seconds
    private val handler = Handler(Looper.getMainLooper())
    private val hideRunnable = Runnable {
        hide()
    }

    init {
        Log.d("DeacePanel", "Initializing...")
        try {
            // Initialize views
            initializeViews()
            setupListeners()
            Log.d("DeacePanel", "Initialization complete")
        } catch (e: Exception) {
            Log.e("DeacePanel", "Error during initialization", e)
        }
    }

    private fun initializeViews() {
        confirmReinitButton = panelLayout.findViewById(R.id.confirm_reinit_button)
    }

    private fun setupListeners() {
        confirmReinitButton.setOnClickListener {
            // Reset auto-hide timer on user interaction
            resetAutoHideTimer()

            // Show confirmation dialog
            AlertDialog.Builder(context)
                .setTitle(R.string.confirm_reinit_app)
                .setMessage("Are you sure you want to restart the app?")
                .setPositiveButton("Yes") { _, _ ->
                    restartApp()
                }
                .setNegativeButton("No", null)
                .show()
        }
    }

    /**
     * Restart the app by recreating the main activity
     */
    private fun restartApp() {
        MessageUtils.showToast(context, context.getString(R.string.app_restarting), Toast.LENGTH_SHORT)

        // Give the toast time to show before restarting
        Handler(Looper.getMainLooper()).postDelayed({
            if (context is MainActivity) {
                val intent = context.intent
                context.finish()
                context.startActivity(intent)
            }
        }, 500)
    }

    /**
     * Shows the panel and starts the auto-hide timer
     */
    fun show() {
        panelLayout.visibility = View.VISIBLE
        resetAutoHideTimer()
        Log.d("DeacePanel", "Panel shown")
    }

    /**
     * Hides the panel and cancels the auto-hide timer
     */
    fun hide() {
        panelLayout.visibility = View.GONE
        cancelAutoHideTimer()
        Log.d("DeacePanel", "Panel hidden")
    }

    /**
     * Returns whether this panel is currently active (visible)
     */
    fun isActive(): Boolean {
        return panelLayout.visibility == View.VISIBLE
    }

    /**
     * Resets the auto-hide timer
     */
    fun resetAutoHideTimer() {
        // Cancel any existing timer
        cancelAutoHideTimer()
        // Start a new timer
        handler.postDelayed(hideRunnable, AUTO_HIDE_DELAY_MS)
        Log.d("DeacePanel", "Auto-hide timer reset")
    }

    /**
     * Cancels the auto-hide timer
     */
    fun cancelAutoHideTimer() {
        handler.removeCallbacks(hideRunnable)
    }
}
