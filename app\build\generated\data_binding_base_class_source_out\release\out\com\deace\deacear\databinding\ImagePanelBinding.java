// Generated by view binder compiler. Do not edit!
package com.deace.deacear.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatSeekBar;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.SwitchCompat;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.deace.deacear.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ImagePanelBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final AppCompatButton fixImageButton;

  @NonNull
  public final AppCompatButton flipHorizontalButton;

  @NonNull
  public final AppCompatSeekBar imageOpacitySeekbar;

  @NonNull
  public final AppCompatTextView imageOpacityValue;

  @NonNull
  public final AppCompatTextView imageSizeText;

  @NonNull
  public final AppCompatButton loadImageButton;

  @NonNull
  public final SwitchCompat preserveAspectSwitch;

  @NonNull
  public final AppCompatButton releaseImageButton;

  @NonNull
  public final AppCompatButton rotate180Button;

  @NonNull
  public final AppCompatButton rotateLeft15Button;

  @NonNull
  public final AppCompatButton rotateLeft90Button;

  @NonNull
  public final AppCompatButton rotateRight15Button;

  @NonNull
  public final AppCompatButton rotateRight90Button;

  @NonNull
  public final AppCompatButton testImagePlacementButton;

  private ImagePanelBinding(@NonNull LinearLayout rootView, @NonNull AppCompatButton fixImageButton,
      @NonNull AppCompatButton flipHorizontalButton, @NonNull AppCompatSeekBar imageOpacitySeekbar,
      @NonNull AppCompatTextView imageOpacityValue, @NonNull AppCompatTextView imageSizeText,
      @NonNull AppCompatButton loadImageButton, @NonNull SwitchCompat preserveAspectSwitch,
      @NonNull AppCompatButton releaseImageButton, @NonNull AppCompatButton rotate180Button,
      @NonNull AppCompatButton rotateLeft15Button, @NonNull AppCompatButton rotateLeft90Button,
      @NonNull AppCompatButton rotateRight15Button, @NonNull AppCompatButton rotateRight90Button,
      @NonNull AppCompatButton testImagePlacementButton) {
    this.rootView = rootView;
    this.fixImageButton = fixImageButton;
    this.flipHorizontalButton = flipHorizontalButton;
    this.imageOpacitySeekbar = imageOpacitySeekbar;
    this.imageOpacityValue = imageOpacityValue;
    this.imageSizeText = imageSizeText;
    this.loadImageButton = loadImageButton;
    this.preserveAspectSwitch = preserveAspectSwitch;
    this.releaseImageButton = releaseImageButton;
    this.rotate180Button = rotate180Button;
    this.rotateLeft15Button = rotateLeft15Button;
    this.rotateLeft90Button = rotateLeft90Button;
    this.rotateRight15Button = rotateRight15Button;
    this.rotateRight90Button = rotateRight90Button;
    this.testImagePlacementButton = testImagePlacementButton;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ImagePanelBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ImagePanelBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.image_panel, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ImagePanelBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.fix_image_button;
      AppCompatButton fixImageButton = ViewBindings.findChildViewById(rootView, id);
      if (fixImageButton == null) {
        break missingId;
      }

      id = R.id.flip_horizontal_button;
      AppCompatButton flipHorizontalButton = ViewBindings.findChildViewById(rootView, id);
      if (flipHorizontalButton == null) {
        break missingId;
      }

      id = R.id.image_opacity_seekbar;
      AppCompatSeekBar imageOpacitySeekbar = ViewBindings.findChildViewById(rootView, id);
      if (imageOpacitySeekbar == null) {
        break missingId;
      }

      id = R.id.image_opacity_value;
      AppCompatTextView imageOpacityValue = ViewBindings.findChildViewById(rootView, id);
      if (imageOpacityValue == null) {
        break missingId;
      }

      id = R.id.image_size_text;
      AppCompatTextView imageSizeText = ViewBindings.findChildViewById(rootView, id);
      if (imageSizeText == null) {
        break missingId;
      }

      id = R.id.load_image_button;
      AppCompatButton loadImageButton = ViewBindings.findChildViewById(rootView, id);
      if (loadImageButton == null) {
        break missingId;
      }

      id = R.id.preserve_aspect_switch;
      SwitchCompat preserveAspectSwitch = ViewBindings.findChildViewById(rootView, id);
      if (preserveAspectSwitch == null) {
        break missingId;
      }

      id = R.id.release_image_button;
      AppCompatButton releaseImageButton = ViewBindings.findChildViewById(rootView, id);
      if (releaseImageButton == null) {
        break missingId;
      }

      id = R.id.rotate_180_button;
      AppCompatButton rotate180Button = ViewBindings.findChildViewById(rootView, id);
      if (rotate180Button == null) {
        break missingId;
      }

      id = R.id.rotate_left_15_button;
      AppCompatButton rotateLeft15Button = ViewBindings.findChildViewById(rootView, id);
      if (rotateLeft15Button == null) {
        break missingId;
      }

      id = R.id.rotate_left_90_button;
      AppCompatButton rotateLeft90Button = ViewBindings.findChildViewById(rootView, id);
      if (rotateLeft90Button == null) {
        break missingId;
      }

      id = R.id.rotate_right_15_button;
      AppCompatButton rotateRight15Button = ViewBindings.findChildViewById(rootView, id);
      if (rotateRight15Button == null) {
        break missingId;
      }

      id = R.id.rotate_right_90_button;
      AppCompatButton rotateRight90Button = ViewBindings.findChildViewById(rootView, id);
      if (rotateRight90Button == null) {
        break missingId;
      }

      id = R.id.test_image_placement_button;
      AppCompatButton testImagePlacementButton = ViewBindings.findChildViewById(rootView, id);
      if (testImagePlacementButton == null) {
        break missingId;
      }

      return new ImagePanelBinding((LinearLayout) rootView, fixImageButton, flipHorizontalButton,
          imageOpacitySeekbar, imageOpacityValue, imageSizeText, loadImageButton,
          preserveAspectSwitch, releaseImageButton, rotate180Button, rotateLeft15Button,
          rotateLeft90Button, rotateRight15Button, rotateRight90Button, testImagePlacementButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
