<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.9.2" type="conditional_incidents">

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="9"
            column="36"
            startOffset="397"
            endLine="9"
            endColumn="76"
            endOffset="437"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="32"/>
            <entry
                name="read"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="12"
            column="36"
            startOffset="591"
            endLine="12"
            endColumn="77"
            endOffset="632"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="28"/>
            <entry
                name="read"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="CustomSplashScreen"
        severity="warning"
        message="The application should not provide its own launch screen">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/SplashActivity.kt"
            line="9"
            column="7"
            startOffset="188"
            endLine="9"
            endColumn="21"
            endOffset="202"/>
        <map>
            <condition targetGE="31"/>
        </map>
    </incident>

    <incident
        id="QueryPermissionsNeeded"
        severity="warning"
        message="Consider adding a `&lt;queries>` declaration to your manifest when calling this method; see https://g.co/dev/packagevisibility for details">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/MainActivity.kt"
            line="1608"
            column="45"
            startOffset="79365"
            endLine="1608"
            endColumn="66"
            endOffset="79386"/>
        <map>
            <entry
                name="queryAll"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="QueryPermissionsNeeded"
        severity="warning"
        message="Consider adding a `&lt;queries>` declaration to your manifest when calling this method; see https://g.co/dev/packagevisibility for details">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/MainActivity.kt"
            line="1638"
            column="49"
            startOffset="81100"
            endLine="1638"
            endColumn="70"
            endOffset="81121"/>
        <map>
            <entry
                name="queryAll"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_grid_on_24.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="43"
            endOffset="223"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_grid_on_24.xml"
            line="8"
            column="26"
            startOffset="259"
            endLine="8"
            endColumn="46"
            endOffset="279"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_image_20.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="43"
            endOffset="223"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_image_20.xml"
            line="8"
            column="26"
            startOffset="259"
            endLine="8"
            endColumn="46"
            endOffset="279"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_image_24.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="43"
            endOffset="223"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_image_24.xml"
            line="8"
            column="26"
            startOffset="259"
            endLine="8"
            endColumn="46"
            endOffset="279"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_export.xml"
            line="8"
            column="26"
            startOffset="242"
            endLine="8"
            endColumn="46"
            endOffset="262"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_grid.xml"
            line="8"
            column="26"
            startOffset="242"
            endLine="8"
            endColumn="46"
            endOffset="262"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_image.xml"
            line="8"
            column="26"
            startOffset="242"
            endLine="8"
            endColumn="46"
            endOffset="262"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_minimize.xml"
            line="8"
            column="26"
            startOffset="242"
            endLine="8"
            endColumn="46"
            endOffset="262"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_reticle.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="43"
            endOffset="223"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_reticle.xml"
            line="8"
            column="26"
            startOffset="259"
            endLine="8"
            endColumn="46"
            endOffset="279"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml"
            line="8"
            column="26"
            startOffset="242"
            endLine="8"
            endColumn="46"
            endOffset="262"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="1543"
                endOffset="1563"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="35"
            column="9"
            startOffset="1543"
            endLine="35"
            endColumn="29"
            endOffset="1563"/>
        <map>
            <condition minGE="31-∞"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_alignParentLeft` with `android:layout_alignParentStart=&quot;true&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_alignParentStart=&quot;true&quot;"
            oldString="layout_alignParentLeft"
            replacement="layout_alignParentStart"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="42"
            column="9"
            startOffset="1558"
            endLine="42"
            endColumn="39"
            endOffset="1588"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_toRightOf` with `android:layout_toEndOf=&quot;@id/main_menu_layout&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_toEndOf=&quot;@id/main_menu_layout&quot;"
            oldString="layout_toRightOf"
            replacement="layout_toEndOf"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="122"
            column="9"
            startOffset="5165"
            endLine="122"
            endColumn="33"
            endOffset="5189"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_toRightOf` with `android:layout_toEndOf=&quot;@id/main_menu_layout&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_toEndOf=&quot;@id/main_menu_layout&quot;"
            oldString="layout_toRightOf"
            replacement="layout_toEndOf"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="213"
            column="9"
            startOffset="9009"
            endLine="213"
            endColumn="33"
            endOffset="9033"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_toRightOf` with `android:layout_toEndOf=&quot;@id/main_menu_layout&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_toEndOf=&quot;@id/main_menu_layout&quot;"
            oldString="layout_toRightOf"
            replacement="layout_toEndOf"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="331"
            column="9"
            startOffset="14096"
            endLine="331"
            endColumn="33"
            endOffset="14120"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_toRightOf` with `android:layout_toEndOf=&quot;@id/main_menu_layout&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_toEndOf=&quot;@id/main_menu_layout&quot;"
            oldString="layout_toRightOf"
            replacement="layout_toEndOf"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="358"
            column="9"
            startOffset="15274"
            endLine="358"
            endColumn="33"
            endOffset="15298"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_alignParentLeft` with `android:layout_alignParentStart=&quot;true&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_alignParentStart=&quot;true&quot;"
            oldString="layout_alignParentLeft"
            replacement="layout_alignParentStart"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="577"
            column="9"
            startOffset="24603"
            endLine="577"
            endColumn="39"
            endOffset="24633"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;8dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginStart=&quot;8dp&quot;"
            oldString="layout_marginLeft"
            replacement="layout_marginStart"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="604"
            column="17"
            startOffset="25761"
            endLine="604"
            endColumn="42"
            endOffset="25786"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_alignParentRight` with `android:layout_alignParentEnd=&quot;true&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_alignParentEnd=&quot;true&quot;"
            oldString="layout_alignParentRight"
            replacement="layout_alignParentEnd"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="644"
            column="9"
            startOffset="27363"
            endLine="644"
            endColumn="40"
            endOffset="27394"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_toLeftOf` with `android:layout_toStartOf=&quot;@id/help_button&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_toStartOf=&quot;@id/help_button&quot;"
            oldString="layout_toLeftOf"
            replacement="layout_toStartOf"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="657"
            column="9"
            startOffset="27963"
            endLine="657"
            endColumn="32"
            endOffset="27986"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginRight` with `android:layout_marginEnd=&quot;16dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginEnd=&quot;16dp&quot;"
            oldString="layout_marginRight"
            replacement="layout_marginEnd"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="659"
            column="9"
            startOffset="28058"
            endLine="659"
            endColumn="35"
            endOffset="28084"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;8dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginStart=&quot;8dp&quot;"
            oldString="layout_marginLeft"
            replacement="layout_marginStart"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_tracking_help.xml"
            line="37"
            column="13"
            startOffset="1340"
            endLine="37"
            endColumn="38"
            endOffset="1365"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;8dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginStart=&quot;8dp&quot;"
            oldString="layout_marginLeft"
            replacement="layout_marginStart"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_tracking_help.xml"
            line="58"
            column="13"
            startOffset="2095"
            endLine="58"
            endColumn="38"
            endOffset="2120"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;8dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginStart=&quot;8dp&quot;"
            oldString="layout_marginLeft"
            replacement="layout_marginStart"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_tracking_help.xml"
            line="79"
            column="13"
            startOffset="2833"
            endLine="79"
            endColumn="38"
            endOffset="2858"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

</incidents>
