package com.deace.deacear.ar

import com.google.ar.core.Pose
import kotlin.math.sqrt

/**
 * Utility class for distance calculations in AR
 */
object DistanceUtils {
    /**
     * Calculates the Euclidean distance between two poses in 3D space.
     * This is used for finding the closest plane to a point.
     * 
     * @param pose1 The first pose
     * @param pose2 The second pose
     * @return The Euclidean distance between the poses
     */
    fun calculateDistance(pose1: Pose, pose2: Pose): Float {
        val dx = pose1.tx() - pose2.tx()
        val dy = pose1.ty() - pose2.ty()
        val dz = pose1.tz() - pose2.tz()
        return sqrt(dx * dx + dy * dy + dz * dz)
    }
}
