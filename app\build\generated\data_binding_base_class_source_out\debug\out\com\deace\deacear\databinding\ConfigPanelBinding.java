// Generated by view binder compiler. Do not edit!
package com.deace.deacear.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatRadioButton;
import androidx.appcompat.widget.AppCompatSeekBar;
import androidx.appcompat.widget.AppCompatSpinner;
import androidx.appcompat.widget.SwitchCompat;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.deace.deacear.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ConfigPanelBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final AppCompatSpinner defaultGridFormatSpinner;

  @NonNull
  public final AppCompatSeekBar defaultGridOpacitySeekbar;

  @NonNull
  public final AppCompatSeekBar defaultImageOpacitySeekbar;

  @NonNull
  public final RadioGroup fileFormatRadioGroup;

  @NonNull
  public final AppCompatEditText filenamePatternEdittext;

  @NonNull
  public final AppCompatButton gridColorButton;

  @NonNull
  public final AppCompatSpinner gridLineThicknessSpinner;

  @NonNull
  public final Button helpAboutButton;

  @NonNull
  public final RadioGroup imageQualityRadioGroup;

  @NonNull
  public final AppCompatRadioButton jpgFormatRadio;

  @NonNull
  public final AppCompatRadioButton maxQualityRadio;

  @NonNull
  public final AppCompatRadioButton normalQualityRadio;

  @NonNull
  public final AppCompatRadioButton pngFormatRadio;

  @NonNull
  public final SwitchCompat preserveAspectSwitch;

  @NonNull
  public final AppCompatButton resetSessionButton;

  @NonNull
  public final Button resetSettingsButton;

  @NonNull
  public final Button saveDirectoryButton;

  @NonNull
  public final TextView saveDirectoryText;

  private ConfigPanelBinding(@NonNull ScrollView rootView,
      @NonNull AppCompatSpinner defaultGridFormatSpinner,
      @NonNull AppCompatSeekBar defaultGridOpacitySeekbar,
      @NonNull AppCompatSeekBar defaultImageOpacitySeekbar,
      @NonNull RadioGroup fileFormatRadioGroup, @NonNull AppCompatEditText filenamePatternEdittext,
      @NonNull AppCompatButton gridColorButton, @NonNull AppCompatSpinner gridLineThicknessSpinner,
      @NonNull Button helpAboutButton, @NonNull RadioGroup imageQualityRadioGroup,
      @NonNull AppCompatRadioButton jpgFormatRadio, @NonNull AppCompatRadioButton maxQualityRadio,
      @NonNull AppCompatRadioButton normalQualityRadio,
      @NonNull AppCompatRadioButton pngFormatRadio, @NonNull SwitchCompat preserveAspectSwitch,
      @NonNull AppCompatButton resetSessionButton, @NonNull Button resetSettingsButton,
      @NonNull Button saveDirectoryButton, @NonNull TextView saveDirectoryText) {
    this.rootView = rootView;
    this.defaultGridFormatSpinner = defaultGridFormatSpinner;
    this.defaultGridOpacitySeekbar = defaultGridOpacitySeekbar;
    this.defaultImageOpacitySeekbar = defaultImageOpacitySeekbar;
    this.fileFormatRadioGroup = fileFormatRadioGroup;
    this.filenamePatternEdittext = filenamePatternEdittext;
    this.gridColorButton = gridColorButton;
    this.gridLineThicknessSpinner = gridLineThicknessSpinner;
    this.helpAboutButton = helpAboutButton;
    this.imageQualityRadioGroup = imageQualityRadioGroup;
    this.jpgFormatRadio = jpgFormatRadio;
    this.maxQualityRadio = maxQualityRadio;
    this.normalQualityRadio = normalQualityRadio;
    this.pngFormatRadio = pngFormatRadio;
    this.preserveAspectSwitch = preserveAspectSwitch;
    this.resetSessionButton = resetSessionButton;
    this.resetSettingsButton = resetSettingsButton;
    this.saveDirectoryButton = saveDirectoryButton;
    this.saveDirectoryText = saveDirectoryText;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ConfigPanelBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ConfigPanelBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.config_panel, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ConfigPanelBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.default_grid_format_spinner;
      AppCompatSpinner defaultGridFormatSpinner = ViewBindings.findChildViewById(rootView, id);
      if (defaultGridFormatSpinner == null) {
        break missingId;
      }

      id = R.id.default_grid_opacity_seekbar;
      AppCompatSeekBar defaultGridOpacitySeekbar = ViewBindings.findChildViewById(rootView, id);
      if (defaultGridOpacitySeekbar == null) {
        break missingId;
      }

      id = R.id.default_image_opacity_seekbar;
      AppCompatSeekBar defaultImageOpacitySeekbar = ViewBindings.findChildViewById(rootView, id);
      if (defaultImageOpacitySeekbar == null) {
        break missingId;
      }

      id = R.id.file_format_radio_group;
      RadioGroup fileFormatRadioGroup = ViewBindings.findChildViewById(rootView, id);
      if (fileFormatRadioGroup == null) {
        break missingId;
      }

      id = R.id.filename_pattern_edittext;
      AppCompatEditText filenamePatternEdittext = ViewBindings.findChildViewById(rootView, id);
      if (filenamePatternEdittext == null) {
        break missingId;
      }

      id = R.id.grid_color_button;
      AppCompatButton gridColorButton = ViewBindings.findChildViewById(rootView, id);
      if (gridColorButton == null) {
        break missingId;
      }

      id = R.id.grid_line_thickness_spinner;
      AppCompatSpinner gridLineThicknessSpinner = ViewBindings.findChildViewById(rootView, id);
      if (gridLineThicknessSpinner == null) {
        break missingId;
      }

      id = R.id.help_about_button;
      Button helpAboutButton = ViewBindings.findChildViewById(rootView, id);
      if (helpAboutButton == null) {
        break missingId;
      }

      id = R.id.image_quality_radio_group;
      RadioGroup imageQualityRadioGroup = ViewBindings.findChildViewById(rootView, id);
      if (imageQualityRadioGroup == null) {
        break missingId;
      }

      id = R.id.jpg_format_radio;
      AppCompatRadioButton jpgFormatRadio = ViewBindings.findChildViewById(rootView, id);
      if (jpgFormatRadio == null) {
        break missingId;
      }

      id = R.id.max_quality_radio;
      AppCompatRadioButton maxQualityRadio = ViewBindings.findChildViewById(rootView, id);
      if (maxQualityRadio == null) {
        break missingId;
      }

      id = R.id.normal_quality_radio;
      AppCompatRadioButton normalQualityRadio = ViewBindings.findChildViewById(rootView, id);
      if (normalQualityRadio == null) {
        break missingId;
      }

      id = R.id.png_format_radio;
      AppCompatRadioButton pngFormatRadio = ViewBindings.findChildViewById(rootView, id);
      if (pngFormatRadio == null) {
        break missingId;
      }

      id = R.id.preserve_aspect_switch;
      SwitchCompat preserveAspectSwitch = ViewBindings.findChildViewById(rootView, id);
      if (preserveAspectSwitch == null) {
        break missingId;
      }

      id = R.id.reset_session_button;
      AppCompatButton resetSessionButton = ViewBindings.findChildViewById(rootView, id);
      if (resetSessionButton == null) {
        break missingId;
      }

      id = R.id.reset_settings_button;
      Button resetSettingsButton = ViewBindings.findChildViewById(rootView, id);
      if (resetSettingsButton == null) {
        break missingId;
      }

      id = R.id.save_directory_button;
      Button saveDirectoryButton = ViewBindings.findChildViewById(rootView, id);
      if (saveDirectoryButton == null) {
        break missingId;
      }

      id = R.id.save_directory_text;
      TextView saveDirectoryText = ViewBindings.findChildViewById(rootView, id);
      if (saveDirectoryText == null) {
        break missingId;
      }

      return new ConfigPanelBinding((ScrollView) rootView, defaultGridFormatSpinner,
          defaultGridOpacitySeekbar, defaultImageOpacitySeekbar, fileFormatRadioGroup,
          filenamePatternEdittext, gridColorButton, gridLineThicknessSpinner, helpAboutButton,
          imageQualityRadioGroup, jpgFormatRadio, maxQualityRadio, normalQualityRadio,
          pngFormatRadio, preserveAspectSwitch, resetSessionButton, resetSettingsButton,
          saveDirectoryButton, saveDirectoryText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
