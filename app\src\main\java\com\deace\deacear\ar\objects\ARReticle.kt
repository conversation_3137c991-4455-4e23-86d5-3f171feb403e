package com.deace.deacear.ar.objects

import android.content.Context
import android.opengl.GLES30
import android.opengl.Matrix
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.FloatBuffer

/**
 * A reticle displayed in the AR scene to help users understand where they can place AR content.
 * The reticle changes color when it's over a detected plane.
 */
class ARReticle(context: Context) {
    private var vertexBuffer: FloatBuffer
    private var colorBuffer: FloatBuffer
    private val modelMatrix = FloatArray(16)
    
    private var program = 0
    private var positionHandle = 0
    private var colorHandle = 0
    private var mvpMatrixHandle = 0
    
    // The color when not over a plane (RGBA)
    private val inactiveColor = floatArrayOf(0.8f, 0.2f, 0.2f, 0.8f) // Red
    
    // The color when over a valid plane (RGBA)
    private val activeColor = floatArrayOf(0.2f, 0.8f, 0.2f, 0.8f) // Green
    
    // Current color used in the reticle drawing
    private var currentColor = inactiveColor.copyOf()
    
    // Flag to indicate if the reticle is over a valid placement surface
    private var isOverValidSurface = false
    
    private val reticleVertices = floatArrayOf(
        // Center circle (12 segments)
        0.0f, 0.0f, 0.0f, // Center point
        // Generate circle vertices
        0.05f, 0.0f, 0.0f,
        0.043f, 0.025f, 0.0f,
        0.025f, 0.043f, 0.0f,
        0.0f, 0.05f, 0.0f,
        -0.025f, 0.043f, 0.0f,
        -0.043f, 0.025f, 0.0f,
        -0.05f, 0.0f, 0.0f,
        -0.043f, -0.025f, 0.0f,
        -0.025f, -0.043f, 0.0f,
        0.0f, -0.05f, 0.0f,
        0.025f, -0.043f, 0.0f,
        0.043f, -0.025f, 0.0f,
        0.05f, 0.0f, 0.0f, // Close the circle
        
        // Crosshair (4 lines)
        // Horizontal line
        -0.1f, 0.0f, 0.0f,
        -0.06f, 0.0f, 0.0f,
        
        0.06f, 0.0f, 0.0f,
        0.1f, 0.0f, 0.0f,
        
        // Vertical line
        0.0f, -0.1f, 0.0f,
        0.0f, -0.06f, 0.0f,
        
        0.0f, 0.06f, 0.0f,
        0.0f, 0.1f, 0.0f
    )
    
    private val vertexShaderCode = """
        uniform mat4 uMVPMatrix;
        attribute vec4 vPosition;
        attribute vec4 vColor;
        varying vec4 fColor;
        void main() {
            fColor = vColor;
            gl_Position = uMVPMatrix * vPosition;
            gl_PointSize = 5.0;
        }
    """
    
    private val fragmentShaderCode = """
        precision mediump float;
        varying vec4 fColor;
        void main() {
            gl_FragColor = fColor;
        }
    """
    
    init {
        Matrix.setIdentityM(modelMatrix, 0)
        
        // Initialize vertex buffer
        val bb = ByteBuffer.allocateDirect(reticleVertices.size * 4)
        bb.order(ByteOrder.nativeOrder())
        vertexBuffer = bb.asFloatBuffer()
        vertexBuffer.put(reticleVertices)
        vertexBuffer.position(0)
        
        // Initialize color buffer with initial inactive color
        val colors = FloatArray(reticleVertices.size / 3 * 4) // 4 color components per vertex
        for (i in 0 until reticleVertices.size / 3) {
            colors[i * 4] = inactiveColor[0]
            colors[i * 4 + 1] = inactiveColor[1]
            colors[i * 4 + 2] = inactiveColor[2]
            colors[i * 4 + 3] = inactiveColor[3]
        }
        
        val cb = ByteBuffer.allocateDirect(colors.size * 4)
        cb.order(ByteOrder.nativeOrder())
        colorBuffer = cb.asFloatBuffer()
        colorBuffer.put(colors)
        colorBuffer.position(0)
        
        // Don't initialize OpenGL resources in constructor
        // They will be initialized in createGLResources()
    }
    
    /**
     * Creates OpenGL resources when an OpenGL context is available.
     * This should be called from the GL thread, specifically from onSurfaceCreated.
     * 
     * @return True if initialization succeeded, false otherwise
     */
    fun createGLResources(): Boolean {
        try {
            if (program != 0) {
                // Already initialized
                return true
            }
            
            // Prepare shaders and OpenGL program
            val vertexShader = loadShader(GLES30.GL_VERTEX_SHADER, vertexShaderCode)
            if (vertexShader == 0) {
                android.util.Log.e("ARReticle", "Failed to load vertex shader")
                return false
            }
            
            val fragmentShader = loadShader(GLES30.GL_FRAGMENT_SHADER, fragmentShaderCode)
            if (fragmentShader == 0) {
                android.util.Log.e("ARReticle", "Failed to load fragment shader")
                GLES30.glDeleteShader(vertexShader)
                return false
            }
            
            program = GLES30.glCreateProgram()
            if (program == 0) {
                android.util.Log.e("ARReticle", "Failed to create GL program")
                GLES30.glDeleteShader(vertexShader)
                GLES30.glDeleteShader(fragmentShader)
                return false
            }
            
            GLES30.glAttachShader(program, vertexShader)
            GLES30.glAttachShader(program, fragmentShader)
            GLES30.glLinkProgram(program)
            
            // Check link status
            val linkStatus = IntArray(1)
            GLES30.glGetProgramiv(program, GLES30.GL_LINK_STATUS, linkStatus, 0)
            if (linkStatus[0] != GLES30.GL_TRUE) {
                android.util.Log.e("ARReticle", "Could not link program: ${GLES30.glGetProgramInfoLog(program)}")
                GLES30.glDeleteProgram(program)
                program = 0
                return false
            }
            
            // Get handles to shader variables
            positionHandle = GLES30.glGetAttribLocation(program, "vPosition")
            colorHandle = GLES30.glGetAttribLocation(program, "vColor")
            mvpMatrixHandle = GLES30.glGetUniformLocation(program, "uMVPMatrix")
            
            // Free shader resources
            GLES30.glDetachShader(program, vertexShader)
            GLES30.glDetachShader(program, fragmentShader)
            GLES30.glDeleteShader(vertexShader)
            GLES30.glDeleteShader(fragmentShader)
            
            android.util.Log.d("ARReticle", "GL resources created successfully")
            return true
        } catch (e: Exception) {
            android.util.Log.e("ARReticle", "Error creating GL resources", e)
            return false
        }
    }
    
    fun draw(projectionMatrix: FloatArray, viewMatrix: FloatArray) {
        // Check if we need to create GL resources first
        if (program == 0) {
            if (!createGLResources()) {
                android.util.Log.e("ARReticle", "Cannot draw reticle - OpenGL resources not initialized")
                return
            }
        }
        
        // Combine the model, view, and projection matrices
        val mvpMatrix = FloatArray(16)
        val mvMatrix = FloatArray(16)
        Matrix.multiplyMM(mvMatrix, 0, viewMatrix, 0, modelMatrix, 0)
        Matrix.multiplyMM(mvpMatrix, 0, projectionMatrix, 0, mvMatrix, 0)
        
        try {
            // Add program to OpenGL environment
            GLES30.glUseProgram(program)
            
            // Enable vertex arrays and set up attributes
            GLES30.glEnableVertexAttribArray(positionHandle)
            GLES30.glVertexAttribPointer(
                positionHandle, 3, GLES30.GL_FLOAT, false, 0, vertexBuffer
            )
            
            GLES30.glEnableVertexAttribArray(colorHandle)
            GLES30.glVertexAttribPointer(
                colorHandle, 4, GLES30.GL_FLOAT, false, 0, colorBuffer
            )
            
            // Apply the combined projection and view transformation
            GLES30.glUniformMatrix4fv(mvpMatrixHandle, 1, false, mvpMatrix, 0)
            
            // Draw the reticle
            // Circle (center point and segments)
            GLES30.glDrawArrays(GLES30.GL_TRIANGLE_FAN, 0, 14)
            
            // Crosshair lines
            GLES30.glLineWidth(3.0f) // Set line width
            GLES30.glDrawArrays(GLES30.GL_LINES, 14, 8)
            
            // Disable vertex arrays
            GLES30.glDisableVertexAttribArray(positionHandle)
            GLES30.glDisableVertexAttribArray(colorHandle)
        } catch (e: Exception) {
            android.util.Log.e("ARReticle", "Error drawing reticle", e)
        }
    }
    
    /**
     * Sets the position of the reticle in world space.
     */
    fun setPosition(x: Float, y: Float, z: Float) {
        modelMatrix[12] = x
        modelMatrix[13] = y
        modelMatrix[14] = z
    }
    
    /**
     * Set the reticle color based on whether it's over a valid placement surface.
     */
    fun setOverValidSurface(isValid: Boolean) {
        if (isOverValidSurface != isValid) {
            isOverValidSurface = isValid
            
            // Update the color buffer with the new color
            val newColor = if (isValid) activeColor else inactiveColor
            currentColor = newColor.copyOf()
            
            for (i in 0 until reticleVertices.size / 3) {
                colorBuffer.position(i * 4)
                colorBuffer.put(newColor)
            }
            colorBuffer.position(0)
        }
    }
    
    /**
     * Release OpenGL resources.
     */
    fun release() {
        if (program != 0) {
            GLES30.glDeleteProgram(program)
            program = 0
        }
    }
    
    private fun loadShader(type: Int, shaderCode: String): Int {
        val shader = GLES30.glCreateShader(type)
        GLES30.glShaderSource(shader, shaderCode)
        GLES30.glCompileShader(shader)
        
        // Get compilation status
        val compiled = IntArray(1)
        GLES30.glGetShaderiv(shader, GLES30.GL_COMPILE_STATUS, compiled, 0)
        if (compiled[0] == 0) {
            android.util.Log.e("ARReticle", "Could not compile shader $type: ${GLES30.glGetShaderInfoLog(shader)}")
            GLES30.glDeleteShader(shader)
            return 0
        }
        return shader
    }
}