package com.deace.deacear.ar

import android.content.Context
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.opengl.GLES30
import android.opengl.GLSurfaceView
import android.opengl.Matrix
import android.util.Log
import com.deace.deacear.MainActivity
import com.deace.deacear.ar.objects.ARGrid
import com.deace.deacear.ar.objects.ARImage
import com.deace.deacear.ar.objects.ARPlaneVisualizer
import com.deace.deacear.ar.objects.ARReticle
import com.google.ar.core.Frame
import com.google.ar.core.Plane
import com.google.ar.core.Pose
import com.google.ar.core.Session
import com.google.ar.core.TrackingState
import com.google.ar.core.exceptions.CameraNotAvailableException
import java.io.ByteArrayOutputStream
import java.nio.ByteBuffer
import java.nio.IntBuffer
import javax.microedition.khronos.egl.EGLConfig
import javax.microedition.khronos.opengles.GL10

class ARRenderer(private val context: Context, private val arSession: Session) : GLSurfaceView.Renderer {
    private val projectionMatrix = FloatArray(16)
    private val viewMatrix = FloatArray(16)
    private val modelMatrix = FloatArray(16)

    private var planeVisualizer: ARPlaneVisualizer? = null
    private var arGrid: ARGrid? = null
    private var arImage: ARImage? = null
    private var arReticle: ARReticle? = null

    private var showPlanes = true
    private var showGrid = false
    private var showImage = false
    private var showReticle = true

    private var screenWidth = 0
    private var screenHeight = 0

    // These matrices will be used throughout the renderer
    private val projmtx = FloatArray(16)
    private val viewmtx = FloatArray(16)

    init {
        planeVisualizer = ARPlaneVisualizer(context)
        arReticle = ARReticle(context)
        Matrix.setIdentityM(modelMatrix, 0)
    }

    fun setPlaneVisualizerEnabled(isEnabled: Boolean) {
        this.showPlanes = isEnabled
        Log.d("ARRenderer", "Plane Visualizer Enabled set to: $isEnabled")
    }

    private lateinit var backgroundRenderer: BackgroundRenderer

    override fun onSurfaceCreated(gl: GL10?, config: EGLConfig?) {
        GLES30.glClearColor(0.0f, 0.0f, 0.0f, 1.0f)
        GLES30.glEnable(GLES30.GL_DEPTH_TEST)
        GLES30.glEnable(GLES30.GL_BLEND)
        GLES30.glBlendFunc(GLES30.GL_SRC_ALPHA, GLES30.GL_ONE_MINUS_SRC_ALPHA)

        // Initialize the background renderer
        try {
            // Defer ARCore texture name setting until AFTER we're sure the session is resumed
            // This is a critical change to avoid the race condition where OpenGL resources
            // are initialized but the session is still in a paused state internally
            backgroundRenderer = BackgroundRenderer(context)
            backgroundRenderer.createOnGlThread()

            // IMPORTANT: Do NOT set camera texture name here
            // We'll do this later once we're sure the session is resumed
            // The camera texture name setting will be done in onDrawFrame when we confirm
            // the session is active

            // Initialize OpenGL resources for all AR objects now that we have a valid OpenGL context
            Log.d("ARRenderer", "Initializing GL resources for AR objects")

            // Initialize plane visualizer OpenGL resources
            planeVisualizer?.let { visualizer ->
                if (!visualizer.createGLResources()) {
                    Log.e("ARRenderer", "Failed to create GL resources for plane visualizer")
                }
            }

            // Initialize reticle OpenGL resources
            arReticle?.let { reticle ->
                if (!reticle.createGLResources()) {
                    Log.e("ARRenderer", "Failed to create GL resources for reticle")
                }
            }

            Log.d("ARRenderer", "OpenGL resources initialized successfully")
        } catch (e: Exception) {
            Log.e("ARRenderer", "Error initializing OpenGL resources", e)
        }
    }

    override fun onSurfaceChanged(gl: GL10?, width: Int, height: Int) {
        screenWidth = width
        screenHeight = height
        GLES30.glViewport(0, 0, width, height)
    }

    // Track if we've set the camera texture name
    private var cameraTxtureNameSet = false

    // Track number of camera texture set attempts
    private var cameraTextureSetAttempts = 0
    private val MAX_TEXTURE_SET_ATTEMPTS = 10 // Increased from 5 to 10 for more retries

    override fun onDrawFrame(gl: GL10?) {
        // Always start with clearing the buffer
        GLES30.glClear(GLES30.GL_COLOR_BUFFER_BIT or GLES30.GL_DEPTH_BUFFER_BIT)

        try {
            // Check if session is initialized
            // Note: This check is kept for safety, even though arSession should never be null
            // at this point due to initialization in the constructor
            if (arSession == null) {
                // Session is not initialized - just render a black screen
                Log.e("ARRenderer", "Session is null, skipping render")
                return
            }

            var isSessionPaused = false
            try {
                // This is a lightweight check that avoids actually updating the frame
                // Just check if session is paused by trying to get its configuration
                arSession.config

                // Add extra verification to ensure session is truly ready
                // Sometimes the session reports as active but still isn't ready for camera texture
                // This is an extra safety check to catch those cases
                try {
                    // Try a quick camera-related operation to verify camera is ready
                    val frame = arSession.update()
                    val testStatus = frame.camera.trackingState
                    Log.d("ARRenderer", "Camera tracking state: $testStatus")
                } catch (e: com.google.ar.core.exceptions.SessionPausedException) {
                    // Camera not ready despite session appearing resumed
                    Log.d("ARRenderer", "Camera not ready despite session appearing active")
                    isSessionPaused = true
                    cameraTxtureNameSet = false
                }
            } catch (e: com.google.ar.core.exceptions.SessionPausedException) {
                // Session is definitely paused
                isSessionPaused = true
                cameraTxtureNameSet = false // Reset texture name set flag when paused
            } catch (e: Exception) {
                Log.e("ARRenderer", "Error checking session state", e)
                // Assume session has an issue and skip rendering
                return
            }

            // If session is paused, don't even try to update the frame
            if (isSessionPaused) {
                // Just draw a clear black screen when paused
                GLES30.glClearColor(0.0f, 0.0f, 0.0f, 1.0f)
                GLES30.glClear(GLES30.GL_COLOR_BUFFER_BIT)
                return
            }

            // IMPORTANT: The session appears active at this point.
            // Check if we need to set the camera texture name
            if (!cameraTxtureNameSet && ::backgroundRenderer.isInitialized) {
                try {
                    // Before setting texture name, perform one more check to verify
                    // that the camera is truly ready
                    val frame = arSession.update()
                    if (frame.camera.trackingState != TrackingState.STOPPED) {
                        // Additional verification passed - camera is tracking or starting to track
                        Log.d("ARRenderer", "Camera tracking state verified before setting texture, state: ${frame.camera.trackingState}")

                        // Now it should be safe to set the camera texture name
                        arSession.setCameraTextureName(backgroundRenderer.getTextureId())
                        cameraTxtureNameSet = true
                        cameraTextureSetAttempts = 0  // Reset attempts on success
                        Log.d("ARRenderer", "Camera texture name set successfully")
                    } else {
                        // Camera isn't tracking yet, may need more time
                        cameraTextureSetAttempts++
                        Log.d("ARRenderer", "Delaying camera texture set - camera not tracking yet. Attempt: $cameraTextureSetAttempts")

                        if (cameraTextureSetAttempts >= MAX_TEXTURE_SET_ATTEMPTS) {
                            // Try anyway after max attempts - sometimes the tracking state
                            // isn't accurate about camera readiness
                            Log.d("ARRenderer", "Maximum attempts reached, trying texture set anyway")
                            arSession.setCameraTextureName(backgroundRenderer.getTextureId())
                            cameraTxtureNameSet = true
                            cameraTextureSetAttempts = 0  // Reset attempts
                        }
                    }
                } catch (e: com.google.ar.core.exceptions.SessionPausedException) {
                    // Session just became paused again
                    isSessionPaused = true
                    Log.d("ARRenderer", "Session paused while setting camera texture name")
                    return
                } catch (e: Exception) {
                    Log.e("ARRenderer", "Error setting camera texture name", e)
                    cameraTextureSetAttempts++
                    // We'll try again next frame if we haven't exceeded attempts
                    if (cameraTextureSetAttempts >= MAX_TEXTURE_SET_ATTEMPTS) {
                        Log.e("ARRenderer", "Max texture set attempts exceeded, giving up for now")
                        // We don't return here to allow rendering to continue without camera background
                    }
                }
            }

            // Try to get a valid frame now that we've confirmed session isn't paused
            var frame: Frame? = null
            try {
                frame = arSession.update()

                // CRITICAL FIX: Process any pending tap events with the current frame
                // This ensures tap events are processed on the GL thread with a valid frame
                if (frame != null && context is MainActivity) {
                    val mainActivity = context as MainActivity
                    if (mainActivity.isARSessionManagerInitialized()) {
                        Log.d("ARRenderer", "Processing pending tap events on GL thread")
                        mainActivity.getARSessionManager()?.processPendingTapEvents(frame)
                    }
                }
            } catch (e: com.google.ar.core.exceptions.SessionPausedException) {
                // Session was just paused between our check and update - this is okay
                Log.d("ARRenderer", "Session became paused during update")
                isSessionPaused = true
                cameraTxtureNameSet = false // Reset texture name set flag
                // Just render a blank screen
                return
            } catch (e: Exception) {
                Log.e("ARRenderer", "Error updating frame", e)
                return
            }

            // At this point, frame should never be null due to our previous checks
            // but we'll keep the structure for code clarity and safety
            try {
                // Draw the camera background
                backgroundRenderer.draw(frame)

                // Add a longer delay before accessing camera data
                // This can help prevent race conditions with camera pipeline
                try {
                    Thread.sleep(50) // Increased from 5ms to 50ms to ensure camera data is ready
                } catch (e: InterruptedException) {
                    // Ignore interruptions
                }

                val camera = frame.camera

                // Get projection matrix
                camera.getProjectionMatrix(projectionMatrix, 0, 0.1f, 100.0f)
                // Copy to the shared projection matrix
                System.arraycopy(projectionMatrix, 0, projmtx, 0, 16)

                // Get view matrix
                camera.getViewMatrix(viewMatrix, 0)
                // Copy to the shared view matrix
                System.arraycopy(viewMatrix, 0, viewmtx, 0, 16)

                // IMPORTANT: Changed rendering order to ensure proper Z-order
                // Draw AR image first if enabled (so it appears in front of planes)
                arImage?.let { image ->
                    if (showImage) {
                        Log.d("ARImageDebug", "==== RENDERER DRAWING IMAGE ====")
                        Log.d("ARImageDebug", "Image object exists: ${arImage != null}")
                        Log.d("ARImageDebug", "Image visibility flag: $showImage")

                        try {
                            // Check for OpenGL errors before drawing image
                            val preImageDrawError = GLES30.glGetError()
                            if (preImageDrawError != GLES30.GL_NO_ERROR) {
                                Log.e("ARImageDebug", "OpenGL error before drawing image: $preImageDrawError")
                            }

                            // Configure OpenGL for image rendering
                            // Temporarily disable depth writing but keep depth testing
                            // This ensures the image is tested against existing depth but doesn't affect
                            // subsequent rendering
                            GLES30.glEnable(GLES30.GL_DEPTH_TEST)
                            GLES30.glDepthMask(false) // Disable depth writing

                            // Enable blending for transparency
                            GLES30.glEnable(GLES30.GL_BLEND)
                            GLES30.glBlendFunc(GLES30.GL_SRC_ALPHA, GLES30.GL_ONE_MINUS_SRC_ALPHA)

                            // Draw the image
                            image.draw(projmtx, viewmtx)
                            Log.d("ARImageDebug", "Image.draw() method called successfully")

                            // Restore depth writing for other objects
                            GLES30.glDepthMask(true)

                            // Check for OpenGL errors after drawing image
                            val postImageDrawError = GLES30.glGetError()
                            if (postImageDrawError != GLES30.GL_NO_ERROR) {
                                Log.e("ARImageDebug", "OpenGL error after drawing image: $postImageDrawError")
                            } else {
                                Log.d("ARImageDebug", "Image drawn successfully by renderer")
                            }
                        } catch (e: Exception) {
                            Log.e("ARImageDebug", "Exception during image drawing: ${e.message}")
                            Log.e("ARImageDebug", "Stack trace: ${e.stackTraceToString()}")
                        }
                    } else {
                        Log.d("ARImageDebug", "Image exists but showImage flag is false")
                    }
                } ?: run {
                    Log.d("ARImageDebug", "No image object available to draw")
                }

                // Draw planes only if enabled (after image to ensure image is in front)
                if (showPlanes && planeVisualizer != null) {
                    try {
                        val planes: Collection<Plane> = arSession.getAllTrackables(Plane::class.java)
                        planeVisualizer?.drawPlanes(planes, projmtx, viewmtx)
                    } catch (e: Exception) {
                        Log.e("ARRenderer", "Exception drawing planes", e)
                    }
                }

                // Draw AR grid if enabled
                arGrid?.let { grid ->
                    if (showGrid) {
                        grid.draw(projmtx, viewmtx)
                    }
                }

                // Draw the reticle in the center of the view
                arReticle?.let { reticle ->
                    if (showReticle) {
                        reticle.draw(projmtx, viewmtx)
                    }
                }
            } catch (e: com.google.ar.core.exceptions.SessionPausedException) {
                // Session became paused during rendering
                isSessionPaused = true
                cameraTxtureNameSet = false // Reset texture name set flag
                Log.d("ARRenderer", "Session became paused during rendering")
            } catch (e: Exception) {
                Log.e("ARRenderer", "Error rendering frame components", e)
            }
        } catch (e: CameraNotAvailableException) {
            Log.e("ARRenderer", "Camera not available during onDrawFrame", e)
            cameraTxtureNameSet = false // Reset when camera is unavailable
        } catch (e: Exception) {
            Log.e("ARRenderer", "Error in onDrawFrame", e)
        }
    }

    fun setPlaneVisibility(visible: Boolean) {
        showPlanes = visible
    }

    fun setGrid(grid: ARGrid) {
        this.arGrid = grid
        showGrid = true
    }

    fun setImage(image: ARImage) {
        try {
            Log.d("ARImageDebug", "==== SETTING IMAGE IN RENDERER ====")

            // Release any existing image first
            if (this.arImage != null) {
                Log.d("ARImageDebug", "Releasing previous image before setting new one")
                this.arImage?.release()
            }

            // Set the new image
            this.arImage = image

            // Ensure visibility flag is set
            showImage = true

            // Verify the image was set correctly
            val imageSet = this.arImage != null

            Log.d("ARImageDebug", "Image set in renderer. Object exists: $imageSet, Visibility flag: $showImage")

            // Force a redraw to ensure the image appears immediately
            // This is done by the GLSurfaceView automatically on the next frame

        } catch (e: Exception) {
            Log.e("ARImageDebug", "Error setting image in renderer: ${e.message}")
            Log.e("ARImageDebug", "Stack trace: ${e.stackTraceToString()}")
        }
    }

    /**
     * Verifies if the renderer has an active image
     * @return true if there is an image loaded and visible
     */
    fun hasActiveImage(): Boolean {
        val hasImage = arImage != null && showImage
        Log.d("ARRenderer", "Checking image status: hasImage=$hasImage, arImage=${arImage != null}, showImage=$showImage")
        return hasImage
    }

    /**
     * Explicitly sets the image visibility flag
     * @param visible Whether the image should be visible
     */
    fun ensureImageVisibility(visible: Boolean) {
        showImage = visible
        Log.d("ARRenderer", "Image visibility explicitly set to: $visible")
    }

    /**
     * Sets the image visibility and requests an immediate render
     * @param visible Whether the image should be visible
     */
    fun setImageVisibility(visible: Boolean) {
        // Check if we're actually changing the visibility state
        val wasVisible = showImage
        showImage = visible

        // Log detailed information about the visibility change
        Log.d("ARImageDebug", "==== IMAGE VISIBILITY CHANGE ====")
        Log.d("ARImageDebug", "Image visibility changed from $wasVisible to $visible")
        Log.d("ARImageDebug", "Image object exists: ${arImage != null}")

        // Verify the image is properly initialized
        if (arImage == null && visible) {
            Log.e("ARImageDebug", "ERROR: Trying to show image but arImage is null!")
        }

        // Request a render to ensure the change takes effect immediately
        requestRender()
        Log.d("ARImageDebug", "Render requested after visibility change")

        // Double-check the visibility flag after setting
        Log.d("ARImageDebug", "Final image visibility state: $showImage")
    }

    // Reference to the GLSurfaceView for requesting renders
    private var glSurfaceView: GLSurfaceView? = null

    /**
     * Sets the GLSurfaceView reference for requesting renders
     * This should be called from MainActivity after creating the renderer
     */
    fun setGLSurfaceView(surfaceView: GLSurfaceView) {
        this.glSurfaceView = surfaceView
        Log.d("ARRenderer", "GLSurfaceView reference set for render requests")
    }

    /**
     * Requests an immediate render of the current frame
     * This is useful when making changes that need to be reflected immediately
     */
    fun requestRender() {
        if (glSurfaceView != null) {
            // Actually request a render from the GLSurfaceView
            glSurfaceView?.requestRender()
            Log.d("ARImageDebug", "Render explicitly requested from GLSurfaceView")
        } else {
            // No GLSurfaceView reference, can't request render
            Log.d("ARImageDebug", "Cannot request render - no GLSurfaceView reference")
        }
    }

    fun removeGrid() {
        showGrid = false
        arGrid = null
    }

    fun removeImage() {
        showImage = false
        arImage = null
    }

    fun updateGridOpacity(opacity: Float) {
        arGrid?.let { grid ->
            grid.setOpacity(opacity)
        }
    }

    fun updateImageOpacity(opacity: Float) {
        arImage?.let { image ->
            image.opacity = opacity
        }
    }

    fun updateGridColor(color: FloatArray) {
        arGrid?.setColor(color)
    }

    fun updateGridLineThickness(thickness: Float) {
        arGrid?.setLineThickness(thickness)
    }

    fun captureARFrame(frame: Frame): Bitmap? {
        try {
            // Create a bitmap with the screen dimensions
            val bitmap = Bitmap.createBitmap(screenWidth, screenHeight, Bitmap.Config.ARGB_8888)

            // Allocate a buffer for reading pixels
            val buffer = IntBuffer.allocate(screenWidth * screenHeight)

            // Read pixels from the framebuffer
            GLES30.glReadPixels(
                0, 0, screenWidth, screenHeight,
                GLES30.GL_RGBA, GLES30.GL_UNSIGNED_BYTE, buffer
            )

            // Check for OpenGL errors
            val error = GLES30.glGetError()
            if (error != GLES30.GL_NO_ERROR) {
                Log.e("ARRenderer", "glReadPixels error: $error")
                return null
            }

            // Convert the buffer to bitmap
            buffer.rewind()
            bitmap.copyPixelsFromBuffer(buffer)

            // OpenGL reads from bottom to top, so we need to flip the bitmap vertically
            val flippedBitmap = Bitmap.createBitmap(
                bitmap.width, bitmap.height, Bitmap.Config.ARGB_8888
            )

            for (y in 0 until bitmap.height) {
                for (x in 0 until bitmap.width) {
                    flippedBitmap.setPixel(x, bitmap.height - y - 1, bitmap.getPixel(x, y))
                }
            }

            // Clean up the original bitmap
            bitmap.recycle()

            return flippedBitmap
        } catch (e: Exception) {
            Log.e("ARRenderer", "Error capturing AR frame", e)
            return null
        }
    }

    fun updateReticlePosition(hitResult: com.google.ar.core.HitResult?) {
        arReticle?.let { reticle ->
            if (hitResult != null) {
                // Hit test found a valid surface, update reticle position to hit point
                val pose = hitResult.hitPose
                reticle.setPosition(pose.tx(), pose.ty(), pose.tz())

                // MODIFIED: Always show green reticle when any hit is found
                // This helps users understand they can place content
                reticle.setOverValidSurface(true)

                // Log reticle position for debugging
                Log.d("ARRenderer", "Reticle positioned at valid surface: (${pose.tx()}, ${pose.ty()}, ${pose.tz()})")
            } else {
                // No valid hit found, indicate that to the user via the reticle color
                reticle.setOverValidSurface(false)
                Log.d("ARRenderer", "No valid surface for reticle")
            }
        }
    }

    /**
     * Updates the reticle position with a pose and validity flag
     * @param pose The pose to position the reticle at, or null to hide it
     * @param isValid Whether the reticle is over a valid surface
     */
    fun updateReticlePosition(pose: com.google.ar.core.Pose?, isValid: Boolean) {
        arReticle?.let { reticle ->
            if (pose != null) {
                // Update reticle position to the provided pose
                reticle.setPosition(pose.tx(), pose.ty(), pose.tz())

                // Set validity based on the provided flag
                reticle.setOverValidSurface(isValid)

                // Log reticle position for debugging
                Log.d("ARRenderer", "Reticle positioned at: (${pose.tx()}, ${pose.ty()}, ${pose.tz()}), valid: $isValid")
            } else {
                // No valid pose, indicate that to the user via the reticle color
                reticle.setOverValidSurface(false)
                Log.d("ARRenderer", "No valid pose for reticle")
            }
        }
    }

    fun setReticleVisibility(visible: Boolean) {
        showReticle = visible
    }

    /**
     * Ensures the image is properly set up and visible
     * @param image The image to display
     * @param pose The pose to position the image at
     * @param planeType Optional plane type to help with orientation
     * @return True if successful, false otherwise
     */
    fun ensureImageSetup(image: ARImage, pose: Pose, planeType: Plane.Type? = null): Boolean {
        try {
            Log.d("TapDebug", "Ensuring image setup in renderer with plane type: $planeType")

            // Set the image in the renderer
            setImage(image)

            // Update the image position with plane type for proper orientation
            image.updateModelMatrix(pose, planeType)

            // Ensure visibility
            setImageVisibility(true)

            // Request immediate render
            requestRender()

            // Verify setup was successful
            val success = hasActiveImage()
            Log.d("TapDebug", "Image setup successful: $success")
            return success
        } catch (e: Exception) {
            Log.e("TapDebug", "Error setting up image: ${e.message}")
            return false
        }
    }

    fun release() {
        if (::backgroundRenderer.isInitialized) {
            backgroundRenderer.release()
        }

        // Release ARGrid resources
        arGrid?.release()
        arGrid = null

        // Release ARImage resources
        arImage?.release()
        arImage = null

        // Release ARPlaneVisualizer resources
        planeVisualizer?.release()
        planeVisualizer = null

        // Release ARReticle resources
        arReticle?.release()
        arReticle = null

        Log.d("ARRenderer", "All renderer resources released")
    }

    /**
     * Get the current viewport width
     * @return The width of the viewport in pixels
     */
    fun getViewportWidth(): Int {
        return screenWidth
    }

    /**
     * Get the current viewport height
     * @return The height of the viewport in pixels
     */
    fun getViewportHeight(): Int {
        return screenHeight
    }

    companion object {
        private const val TAG = "ARRenderer"
    }
}