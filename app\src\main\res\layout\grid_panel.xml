<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/grid_format"
        android:textAppearance="@style/TextAppearance.AppCompat.Medium" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/vertical_format_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/vertical" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/square_format_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="@string/square" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/horizontal_format_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="@string/horizontal" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/fix_grid_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/fix_grid" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/release_grid_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:visibility="gone"
            android:text="@string/release_grid" />
    </LinearLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/grid_opacity"
        android:textAppearance="@style/TextAppearance.AppCompat.Medium" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatSeekBar
            android:id="@+id/grid_opacity_seekbar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:max="100"
            android:progress="50" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/grid_opacity_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="@string/opacity_format_50" />
    </LinearLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/grid_color"
        android:textAppearance="@style/TextAppearance.AppCompat.Medium" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/grid_color_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/choose_color" />

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/line_thickness"
        android:textAppearance="@style/TextAppearance.AppCompat.Medium" />

    <androidx.appcompat.widget.AppCompatSpinner
        android:id="@+id/grid_line_thickness_spinner"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:entries="@array/line_thickness_options" />
</LinearLayout>