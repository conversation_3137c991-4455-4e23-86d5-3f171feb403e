package com.deace.deacear.ar

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import android.util.Log
import android.view.MotionEvent
import android.widget.Toast
import com.deace.deacear.utils.MessageUtils
import com.google.ar.core.Anchor
import com.google.ar.core.Frame
import com.google.ar.core.HitResult
import com.google.ar.core.Plane
import com.google.ar.core.Pose
import com.google.ar.core.Session
import com.google.ar.core.TrackingState
import com.deace.deacear.ar.DistanceUtils
import kotlin.math.sqrt
import com.deace.deacear.utils.SettingsManager
import com.deace.deacear.ar.objects.ARGrid
import com.deace.deacear.ar.objects.ARImage
import java.io.IOException
import java.io.InputStream

import kotlin.math.max
import kotlin.math.min
import kotlin.math.roundToInt

class ARSessionManager(
    private val context: Context,
    private val arSession: Session,
    private val arRenderer: ARRenderer,
    private val settingsManager: SettingsManager
) {
    // Listener for UI updates
    private var sessionListener: ARSessionListener? = null

    // Set the listener for UI updates
    fun setARSessionListener(listener: ARSessionListener) {
        this.sessionListener = listener
    }
    private var gridAnchor: Anchor? = null
    private var imageAnchor: Anchor? = null

    private var currentGrid: com.deace.deacear.ar.objects.ARGrid? = null
    private var currentImage: ARImage? = null

    var isGridFixed: Boolean = false
        private set // Makes the setter private, getter remains public
    var isImageFixed: Boolean = false
        private set // Public getter, private setter

    // Make isDragging, isScaling, isRotating public for external control
    var isDragging = false
        private set
    var isScaling = false
        private set
    var isRotating = false
        private set

    // Queue to store tap events to be processed on the GL thread
    private val pendingTapEvents = mutableListOf<MotionEvent>()

    // Synthetic hit variables for fallback placement
    private var usingSyntheticHit = false
    private var syntheticHitPose: Pose? = null
    private var syntheticHitPlane: Plane? = null

    // Flag to track if we're in the process of placing an image
    // This helps prevent race conditions during tap handling
    // Using @Volatile to ensure visibility across threads
    @Volatile
    private var isPlacingImage = false

    private val desiredMaxImageDimension by lazy { 2400 }  // Example: Max width or height of 1024px

    fun handleTap(event: MotionEvent) {
        if (isSessionPaused) {
            Log.d("ARSessionManager", "HandleTap: Session is paused, ignoring tap")
            return
        }

        // Log detailed tap information
        Log.d("TapDetectionDebug", "==== TAP EVENT RECEIVED ====")
        Log.d("TapDetectionDebug", "Tap at (${event.x}, ${event.y})")
        Log.d("TapDetectionDebug", "Current state: isPlacingImage=$isPlacingImage, imageFixed=$isImageFixed, imageLoaded=${currentImage != null}")

        // Prevent multiple tap events from being processed simultaneously
        // This is especially important when placing images
        // Use synchronized block to ensure thread safety when checking and setting the flag
        synchronized(this) {
            if (isPlacingImage) {
                Log.d("ARSessionManager", "HandleTap: Already placing an image, ignoring tap")
                Log.d("TapDetectionDebug", "Tap ignored - already placing an image")
                return
            }

            // If we're about to process a tap and have an image loaded but not fixed,
            // set the flag immediately to prevent race conditions
            if (currentImage != null && !isImageFixed) {
                isPlacingImage = true
                Log.d("TapDetectionDebug", "Setting isPlacingImage flag to true at start of tap handling")
            }
        }

        // Create a copy of the event since the original might be recycled
        val eventCopy = MotionEvent.obtain(event)

        // Add the event to the queue to be processed on the GL thread
        synchronized(pendingTapEvents) {
            pendingTapEvents.add(eventCopy)
            Log.d("ARSessionManager", "Tap event queued for processing on GL thread")
        }

        // Try to process the tap immediately if possible
        try {
            val frame = arSession.update()
            // Process the tap event directly
            processTapEvent(eventCopy, frame)
            Log.d("ARSessionManager", "Tap event processed immediately")

            // Remove the event from the queue since we processed it
            synchronized(pendingTapEvents) {
                pendingTapEvents.remove(eventCopy)
            }
        } catch (e: Exception) {
            Log.e("ARSessionManager", "Error during immediate tap processing: ${e.message}")
            Log.d("ARSessionManager", "Could not get frame for immediate tap processing, will process in next frame")

            // Reset the isPlacingImage flag in case of error during immediate processing
            // This prevents the flag from getting stuck in the "true" state
            if (currentImage != null && !isImageFixed) {
                synchronized(this) {
                    isPlacingImage = false
                    Log.d("TapDetectionDebug", "Reset isPlacingImage flag after immediate processing error")
                }
            }

            // The event will still be in the queue to be processed later
        }
    }

    /**
     * Process any pending tap events on the GL thread
     * This should be called from the ARRenderer's onDrawFrame method
     */
    fun processPendingTapEvents(frame: Frame) {
        // Process all pending tap events
        val eventsToProcess = mutableListOf<MotionEvent>()

        // Get all pending events while holding the lock
        synchronized(pendingTapEvents) {
            if (pendingTapEvents.isEmpty()) return

            eventsToProcess.addAll(pendingTapEvents)
            pendingTapEvents.clear()
        }

        // Process each event
        for (event in eventsToProcess) {
            try {
                processTapEvent(event, frame)
            } finally {
                // Always recycle the event copy when done
                event.recycle()
            }
        }
    }

    /**
     * Process a single tap event with the given frame
     * This is called on the GL thread
     */
    private fun processTapEvent(event: MotionEvent, frame: Frame) {
        try {
            // Log detailed information about the tap event
            Log.d("ARSessionManager", "==== PROCESSING TAP EVENT ====")
            Log.d("ARSessionManager", "Tap position on screen: (${event.x}, ${event.y})")
            Log.d("ARSessionManager", "Current image loaded: ${currentImage != null}")
            Log.d("ARSessionManager", "Image fixed: $isImageFixed")

            // Check if there are any planes detected at all
            val allPlanes = frame.getUpdatedTrackables(Plane::class.java)
            val trackingPlanes = allPlanes.count { it.trackingState == TrackingState.TRACKING }
            val verticalPlanes = allPlanes.count {
                it.trackingState == TrackingState.TRACKING && it.type == Plane.Type.VERTICAL
            }

            // Log detailed plane information
            Log.d("ARSessionManager", "Plane detection status:")
            Log.d("ARSessionManager", "- Total planes: ${allPlanes.size}")
            Log.d("ARSessionManager", "- Tracking planes: $trackingPlanes")
            Log.d("ARSessionManager", "- Vertical planes: $verticalPlanes")

            // Log details about each vertical plane
            if (verticalPlanes > 0) {
                Log.d("ARSessionManager", "Vertical plane details:")
                allPlanes.filter { it.trackingState == TrackingState.TRACKING && it.type == Plane.Type.VERTICAL }
                    .forEachIndexed { index, plane ->
                        Log.d("ARSessionManager", "  Plane $index:")
                        Log.d("ARSessionManager", "  - Size: ${plane.extentX}m x ${plane.extentZ}m")
                        Log.d("ARSessionManager", "  - Position: (${plane.centerPose.tx()}, ${plane.centerPose.ty()}, ${plane.centerPose.tz()})")
                    }
            }

            // Check if we have an image loaded but not yet placed
            val imageReadyToPlace = !isImageFixed && currentImage != null

            // Enhanced logging for tap detection debugging
            Log.d("TapDetectionDebug", "==== TAP DETECTION STATE ====")
            Log.d("TapDetectionDebug", "Image ready to place: $imageReadyToPlace")
            Log.d("TapDetectionDebug", "isImageFixed: $isImageFixed")
            Log.d("TapDetectionDebug", "currentImage: ${currentImage != null}")
            Log.d("TapDetectionDebug", "Vertical planes: $verticalPlanes")
            Log.d("TapDetectionDebug", "Tracking planes: $trackingPlanes")
            Log.d("SyntheticHitDebug", "Image ready to place: $imageReadyToPlace (isImageFixed=$isImageFixed, currentImage=${currentImage != null})")

            // If we have an image ready to place, be more permissive with hit testing
            var hitResult = if (imageReadyToPlace) {
                Log.d("ARSessionManager", "Image ready to place - using permissive hit testing")
                Log.d("TapDetectionDebug", "Image ready to place - using permissive hit testing")

                // Try multiple locations for hit testing to increase chances of success
                var hit: HitResult? = null

                // First try the tap location
                hit = performHitTest(frame, event.x, event.y)
                if (hit != null) {
                    Log.d("TapDetectionDebug", "Found hit at tap location (${event.x}, ${event.y})")
                }

                // If no hit found, try center of screen
                if (hit == null) {
                    Log.d("ARSessionManager", "No hit at tap location, trying center of screen")
                    Log.d("TapDetectionDebug", "No hit at tap location, trying center of screen")
                    val screenWidth = arRenderer.getViewportWidth().toFloat()
                    val screenHeight = arRenderer.getViewportHeight().toFloat()
                    hit = performHitTest(frame, screenWidth / 2f, screenHeight / 2f)
                    if (hit != null) {
                        Log.d("TapDetectionDebug", "Found hit at center of screen")
                    }
                }

                // If still no hit, try a few more positions
                if (hit == null) {
                    Log.d("TapDetectionDebug", "No hit at center, trying additional positions")
                    val screenWidth = arRenderer.getViewportWidth().toFloat()
                    val screenHeight = arRenderer.getViewportHeight().toFloat()

                    // Try left, right, top, bottom of center
                    val testPositions = listOf(
                        Pair(screenWidth * 0.25f, screenHeight * 0.5f),  // Left center
                        Pair(screenWidth * 0.75f, screenHeight * 0.5f),  // Right center
                        Pair(screenWidth * 0.5f, screenHeight * 0.25f),  // Top center
                        Pair(screenWidth * 0.5f, screenHeight * 0.75f)   // Bottom center
                    )

                    for (pos in testPositions) {
                        if (hit == null) {
                            hit = performHitTest(frame, pos.first, pos.second)
                            if (hit != null) {
                                Log.d("TapDetectionDebug", "Found hit at position (${pos.first}, ${pos.second})")
                                break
                            }
                        }
                    }
                }

                // If still no hit but we have planes, create a fallback hit
                if (hit == null && trackingPlanes > 0) {
                    Log.d("ARSessionManager", "No hit found but planes exist - creating fallback hit")
                    Log.d("SyntheticHitDebug", "==== SYNTHETIC HIT GENERATION ATTEMPT ====")
                    Log.d("SyntheticHitDebug", "Total tracking planes: $trackingPlanes")
                    Log.d("SyntheticHitDebug", "Vertical planes: $verticalPlanes")
                    Log.d("TapDetectionDebug", "Creating synthetic hit - vertical planes: $verticalPlanes")

                    // Use the camera position as a fallback
                    val cameraPose = frame.camera.pose
                    Log.d("SyntheticHitDebug", "Camera position: (${cameraPose.tx()}, ${cameraPose.ty()}, ${cameraPose.tz()})")

                    // Create multiple points at different distances in front of the camera
                    // to increase chances of finding a good hit point
                    val distances = listOf(-1.5f, -2.0f, -2.5f, -3.0f)
                    var bestSyntheticHitPose: Pose? = null
                    var bestSyntheticHitPlane: Plane? = null
                    var bestDistance = Float.MAX_VALUE

                    for (distance in distances) {
                        // Create a point at the specified distance in front of the camera
                        val translation = floatArrayOf(0f, 0f, distance)
                        val rotatedTranslation = FloatArray(3)
                        cameraPose.transformPoint(translation, 0, rotatedTranslation, 0)
                        Log.d("TapDetectionDebug", "Trying synthetic hit at distance $distance: (${rotatedTranslation[0]}, ${rotatedTranslation[1]}, ${rotatedTranslation[2]})")

                        // Create a pose at this position
                        val hitPose = Pose(rotatedTranslation, cameraPose.rotationQuaternion)

                        // Find the closest plane to use for the hit
                        var closestPlane: Plane? = null
                        var minDistance = Float.MAX_VALUE
                        var planeIndex = 0

                        // First try to find vertical planes only
                        for (plane in allPlanes) {
                            if (plane.trackingState == TrackingState.TRACKING && plane.type == Plane.Type.VERTICAL) {
                                val distance = DistanceUtils.calculateDistance(hitPose, plane.centerPose)
                                Log.d("TapDetectionDebug", "Vertical plane $planeIndex: distance=$distance")

                                if (distance < minDistance) {
                                    minDistance = distance
                                    closestPlane = plane
                                }
                                planeIndex++
                            }
                        }

                        // If we found a vertical plane and it's closer than our previous best, use it
                        if (closestPlane != null && minDistance < bestDistance) {
                            bestDistance = minDistance
                            bestSyntheticHitPose = hitPose
                            bestSyntheticHitPlane = closestPlane
                            Log.d("TapDetectionDebug", "Found better synthetic hit at distance $distance with vertical plane, distance: $minDistance")
                        }
                    }

                    // If we didn't find any vertical planes, try with any planes
                    if (bestSyntheticHitPlane == null) {
                        Log.d("TapDetectionDebug", "No vertical planes found for synthetic hit, trying any planes")

                        // Create a point 2 meters in front of the camera (default fallback)
                        val translation = floatArrayOf(0f, 0f, -2f)
                        val rotatedTranslation = FloatArray(3)
                        cameraPose.transformPoint(translation, 0, rotatedTranslation, 0)
                        Log.d("SyntheticHitDebug", "Synthetic hit position: (${rotatedTranslation[0]}, ${rotatedTranslation[1]}, ${rotatedTranslation[2]})")

                        // Create a pose at this position
                        val hitPose = Pose(rotatedTranslation, cameraPose.rotationQuaternion)
                        Log.d("SyntheticHitDebug", "Synthetic hit pose created")

                        // Find the closest plane to use for the hit
                        var closestPlane: Plane? = null
                        var minDistance = Float.MAX_VALUE
                        var planeIndex = 0

                        Log.d("SyntheticHitDebug", "Searching for closest plane among $trackingPlanes tracking planes")
                        for (plane in allPlanes) {
                            if (plane.trackingState == TrackingState.TRACKING) {
                                val distance = DistanceUtils.calculateDistance(hitPose, plane.centerPose)
                                Log.d("SyntheticHitDebug", "Plane $planeIndex: type=${plane.type}, distance=$distance, size=${plane.extentX}x${plane.extentZ}")

                                if (distance < minDistance) {
                                    minDistance = distance
                                    closestPlane = plane
                                    Log.d("SyntheticHitDebug", "New closest plane: $planeIndex, type=${plane.type}, distance=$distance")
                                }
                                planeIndex++
                            }
                        }

                        bestSyntheticHitPose = hitPose
                        bestSyntheticHitPlane = closestPlane
                    }

                    // Use the best synthetic hit we found
                    if (bestSyntheticHitPlane != null && bestSyntheticHitPose != null) {
                        Log.d("SyntheticHitDebug", "Using best synthetic hit")
                        Log.d("SyntheticHitDebug", "Selected plane: type=${bestSyntheticHitPlane.type}")
                        Log.d("SyntheticHitDebug", "Plane center: (${bestSyntheticHitPlane.centerPose.tx()}, ${bestSyntheticHitPlane.centerPose.ty()}, ${bestSyntheticHitPlane.centerPose.tz()})")
                        Log.d("SyntheticHitDebug", "Hit pose: (${bestSyntheticHitPose.tx()}, ${bestSyntheticHitPose.ty()}, ${bestSyntheticHitPose.tz()})")
                        Log.d("ARSessionManager", "Created synthetic hit on best plane")
                        Log.d("TapDetectionDebug", "Created synthetic hit on best plane: type=${bestSyntheticHitPlane.type}")

                        // We can't create a HitResult directly, so we'll handle this special case below
                        // by setting a flag and using the hitPose
                        usingSyntheticHit = true
                        syntheticHitPose = bestSyntheticHitPose
                        syntheticHitPlane = bestSyntheticHitPlane

                        Log.d("SyntheticHitDebug", "Synthetic hit flags set: usingSyntheticHit=$usingSyntheticHit")
                        Log.d("TapDetectionDebug", "Synthetic hit flags set for image placement")
                    } else {
                        Log.d("SyntheticHitDebug", "No suitable plane found for synthetic hit")
                        Log.d("TapDetectionDebug", "Failed to create synthetic hit - no suitable planes found")
                    }
                }

                hit
            } else {
                // Standard hit testing for grid placement or other interactions
                performHitTest(frame, event.x, event.y)
            }

            if (hitResult != null) {
                val trackable = hitResult.trackable
                Log.d("ARSessionManager", "Hit test successful on: ${trackable.javaClass.simpleName}")

                // Handle the hit result - accept ANY trackable for now to make it more permissive
                // We'll place content even if it's not exactly on a plane

                // Log the hit position
                Log.d("ARSessionManager", "Hit position: (${hitResult.hitPose.tx()}, ${hitResult.hitPose.ty()}, ${hitResult.hitPose.tz()})")

                // Place grid if none exists yet and not fixed
                if (!isGridFixed && currentGrid == null) {
                    Log.d("ARSessionManager", "Creating new grid at hit position")

                    // Create a new grid
                    val aspectRatio = when (settingsManager.defaultGridFormat) {
                        SettingsManager.GRID_FORMAT_VERTICAL -> 9f / 16f
                        SettingsManager.GRID_FORMAT_SQUARE -> 1f
                        SettingsManager.GRID_FORMAT_HORIZONTAL -> 16f / 9f
                        else -> 1f
                    }

                    try {
                        // Create the grid with default settings
                        currentGrid = ARGrid(context, aspectRatio, settingsManager.defaultGridColor)
                        currentGrid?.setOpacity(settingsManager.defaultGridOpacity)
                        currentGrid?.setLineThickness(settingsManager.defaultGridLineThickness)

                        // Create anchor at hit location
                        gridAnchor = hitResult.createAnchor()

                        // Log anchor creation
                        Log.d("ARSessionManager", "Created grid anchor at: " +
                                "(${gridAnchor!!.pose.tx()}, ${gridAnchor!!.pose.ty()}, ${gridAnchor!!.pose.tz()})")

                        // Update grid position to match anchor
                        currentGrid?.updateModelMatrix(gridAnchor!!.pose)

                        // Set the grid in the renderer
                        arRenderer.setGrid(currentGrid!!)

                        // Show a toast to confirm grid placement
                        val handler = Handler(Looper.getMainLooper())
                        handler.post {
                            Toast.makeText(
                                context,
                                "Grid placed successfully!",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    } catch (e: Exception) {
                        Log.e("ARSessionManager", "Error creating grid", e)
                        val handler = Handler(Looper.getMainLooper())
                        handler.post {
                            Toast.makeText(
                                context,
                                "Error creating grid: ${e.message}",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                }
                // If image loaded but not fixed, place or update its position
                else if (!isImageFixed && currentImage != null) {
                    Log.d("ARSessionManager", "Placing or updating loaded image at hit position")

                    // Set the flag to indicate we're placing an image
                    isPlacingImage = true
                    Log.d("TapDetectionDebug", "Setting isPlacingImage flag to true")

                    try {
                        // Always start a dragging operation when placing image with tap
                        isDragging = true

                        // Detach any existing anchor first
                        imageAnchor?.detach()

                        // Check if we're using a synthetic hit
                        if (usingSyntheticHit && syntheticHitPose != null && syntheticHitPlane != null) {
                            Log.d("ARSessionManager", "Using synthetic hit for image placement")
                            Log.d("SyntheticHitDebug", "==== USING SYNTHETIC HIT FOR IMAGE PLACEMENT ====")
                            Log.d("SyntheticHitDebug", "Synthetic hit pose: (${syntheticHitPose!!.tx()}, ${syntheticHitPose!!.ty()}, ${syntheticHitPose!!.tz()})")
                            Log.d("SyntheticHitDebug", "Synthetic hit plane type: ${syntheticHitPlane!!.type}")
                            Log.d("SyntheticHitDebug", "Synthetic hit plane size: ${syntheticHitPlane!!.extentX}x${syntheticHitPlane!!.extentZ}")
                            Log.d("TapDetectionDebug", "Using synthetic hit for image placement on ${syntheticHitPlane!!.type} plane")

                            try {
                                // Create anchor from the synthetic hit pose on the closest plane
                                imageAnchor = syntheticHitPlane!!.createAnchor(syntheticHitPose!!)
                                Log.d("SyntheticHitDebug", "Successfully created anchor from synthetic hit")
                                Log.d("SyntheticHitDebug", "Anchor pose: (${imageAnchor!!.pose.tx()}, ${imageAnchor!!.pose.ty()}, ${imageAnchor!!.pose.tz()})")
                                Log.d("TapDetectionDebug", "Successfully created anchor from synthetic hit")

                                // Reset synthetic hit flags
                                usingSyntheticHit = false
                                syntheticHitPose = null
                                syntheticHitPlane = null
                                Log.d("SyntheticHitDebug", "Reset synthetic hit flags")
                            } catch (e: Exception) {
                                Log.e("SyntheticHitDebug", "Failed to create anchor from synthetic hit", e)
                                Log.e("TapDetectionDebug", "Failed to create anchor from synthetic hit: ${e.message}")

                                // Fallback to regular hit test if synthetic hit fails
                                // Check if we have a valid hit result to fall back to
                                if (hitResult != null) {
                                    try {
                                        Log.d("SyntheticHitDebug", "Falling back to regular hit test")
                                        Log.d("TapDetectionDebug", "Falling back to regular hit test")
                                        imageAnchor = hitResult.createAnchor()
                                        Log.d("TapDetectionDebug", "Created anchor from regular hit test fallback")
                                    } catch (anchorEx: Exception) {
                                        Log.e("SyntheticHitDebug", "Failed to create anchor from fallback hit result", anchorEx)
                                        Log.e("TapDetectionDebug", "Failed to create anchor from fallback hit result: ${anchorEx.message}")
                                        throw anchorEx // Re-throw to be caught by outer try-catch
                                    }
                                } else {
                                    Log.e("SyntheticHitDebug", "No fallback hit result available")
                                    Log.e("TapDetectionDebug", "No fallback hit result available, image placement failed")
                                    throw e // Re-throw to be caught by outer try-catch
                                }
                            }
                        } else {
                            // Create new anchor at hit location from normal hit test
                            // At this point, hitResult must be non-null due to the outer if-else structure
                            Log.d("SyntheticHitDebug", "Using normal hit test for image placement")
                            Log.d("TapDetectionDebug", "Using normal hit test for image placement")

                            // Get the trackable type for logging
                            // hitResult is guaranteed to be non-null here
                            val trackableType = if (hitResult!!.trackable is Plane) {
                                val plane = hitResult.trackable as Plane
                                "Plane (${plane.type})"
                            } else {
                                hitResult.trackable.javaClass.simpleName
                            }

                            Log.d("TapDetectionDebug", "Hit test trackable type: $trackableType")
                            imageAnchor = hitResult.createAnchor()
                            Log.d("SyntheticHitDebug", "Created anchor from normal hit test")
                            Log.d("SyntheticHitDebug", "Anchor pose: (${imageAnchor!!.pose.tx()}, ${imageAnchor!!.pose.ty()}, ${imageAnchor!!.pose.tz()})")
                            Log.d("TapDetectionDebug", "Created anchor from normal hit test at (${imageAnchor!!.pose.tx()}, ${imageAnchor!!.pose.ty()}, ${imageAnchor!!.pose.tz()})")
                        }

                        // Log anchor creation for debug purposes
                        Log.d("ARSessionManager", "Created image anchor at: " +
                                "(${imageAnchor!!.pose.tx()}, ${imageAnchor!!.pose.ty()}, ${imageAnchor!!.pose.tz()})")

                        // Update image position to match anchor
                        currentImage?.updateModelMatrix(imageAnchor!!.pose)

                        // Show a toast to confirm image placement
                        val handler = Handler(Looper.getMainLooper())
                        handler.post {
                            // Notify listener that image was placed
                            sessionListener?.onImagePlaced()

                            MessageUtils.showToast(
                                context,
                                "Image placed! You can now adjust it with touch gestures.",
                                Toast.LENGTH_LONG
                            )
                        }

                        // Reset the image placement flag
                        isPlacingImage = false
                        Log.d("TapDetectionDebug", "Reset isPlacingImage flag to false after successful placement")
                    } catch (e: Exception) {
                        Log.e("ARSessionManager", "Error placing image", e)
                        val handler = Handler(Looper.getMainLooper())
                        handler.post {
                            MessageUtils.showToast(
                                context,
                                "Error placing image: ${e.message}",
                                Toast.LENGTH_SHORT
                            )
                        }

                        // Reset the image placement flag even if there was an error
                        isPlacingImage = false
                        Log.d("TapDetectionDebug", "Reset isPlacingImage flag to false after placement error")
                    }
                }
                // If both grid and image exist, update the one that's not fixed
                else if ((!isGridFixed && currentGrid != null) || (!isImageFixed && currentImage != null)) {
                    Log.d("ARSessionManager", "Updating existing content position")

                    try {
                        if (!isGridFixed && currentGrid != null) {
                            // Update grid position
                            gridAnchor?.detach()
                            gridAnchor = hitResult.createAnchor()
                            currentGrid?.updateModelMatrix(gridAnchor!!.pose)

                            // Show a toast to confirm grid update
                            val handler = Handler(Looper.getMainLooper())
                            handler.post {
                                Toast.makeText(
                                    context,
                                    "Grid position updated!",
                                    Toast.LENGTH_SHORT
                                ).show()
                            }
                        } else if (!isImageFixed && currentImage != null) {
                            // Update image position
                            imageAnchor?.detach()
                            imageAnchor = hitResult.createAnchor()
                            currentImage?.updateModelMatrix(imageAnchor!!.pose)

                            // Show a toast to confirm image update
                            val handler = Handler(Looper.getMainLooper())
                            handler.post {
                                Toast.makeText(
                                    context,
                                    "Image position updated!",
                                    Toast.LENGTH_SHORT
                                ).show()
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("ARSessionManager", "Error updating content position", e)
                        val handler = Handler(Looper.getMainLooper())
                        handler.post {
                            MessageUtils.showToast(
                                context,
                                "Error updating position: ${e.message}",
                                Toast.LENGTH_SHORT
                            )
                        }
                    }
                }
                else {
                    // Both grid and image are fixed, or something else is wrong
                    Log.d("ARSessionManager", "No action taken - Grid fixed: $isGridFixed, Image fixed: $isImageFixed")

                    val handler = Handler(Looper.getMainLooper())
                    handler.post {
                        MessageUtils.showToast(
                            context,
                            "Tap on a surface to place or move content",
                            Toast.LENGTH_SHORT
                        )
                    }
                }
            } else {
                // No hit result found
                Log.d("ARSessionManager", "No hit result found")

                // Check if there are any planes detected in the frame
                val hasPlanes = trackingPlanes > 0

                // Show a toast with appropriate guidance
                val handler = Handler(Looper.getMainLooper())
                handler.post {
                    // Determine which message to show based on plane detection
                    if (hasPlanes && verticalPlanes > 0) {
                        // We need to perform this outside of the post{} lambda
                        // Show a message first
                        MessageUtils.showToast(
                            context,
                            "Finding best wall for placement...",
                            Toast.LENGTH_SHORT
                        )
                    } else if (hasPlanes) {
                        // Planes detected but none are vertical
                        MessageUtils.showToast(
                            context,
                            "Horizontal surfaces detected. Please scan vertical walls.",
                            Toast.LENGTH_LONG
                        )
                    } else {
                        // No planes detected yet
                        MessageUtils.showToast(
                            context,
                            "No surface detected. Try moving around to scan walls.",
                            Toast.LENGTH_LONG
                        )
                    }
                }

                // Handle synthetic hit generation for vertical planes - outside the post{} lambda
                if (hasPlanes && verticalPlanes > 0 && !isImageFixed && currentImage != null) {
                    // We have vertical planes but tap didn't hit any of them
                    // Instead of just showing a message, let's try a synthetic hit
                    Log.d("ARSessionManager", "Vertical planes detected but hit test failed. Creating synthetic hit...")
                    Log.d("SyntheticHitDebug", "==== FALLBACK SYNTHETIC HIT GENERATION ====")
                    Log.d("SyntheticHitDebug", "Vertical planes available: $verticalPlanes")
                    Log.d("SyntheticHitDebug", "Image ready to place: ${currentImage != null}")
                    Log.d("SyntheticHitDebug", "Image fixed: $isImageFixed")
                    Log.d("TapDetectionDebug", "==== FALLBACK SYNTHETIC HIT GENERATION ====")
                    Log.d("TapDetectionDebug", "Vertical planes: $verticalPlanes, Image ready: ${currentImage != null}")

                    // Set the flag to indicate we're placing an image
                    isPlacingImage = true
                    Log.d("TapDetectionDebug", "Setting isPlacingImage flag to true for fallback synthetic hit")

                    // Get camera position
                    val cameraPose = frame.camera.pose
                    Log.d("SyntheticHitDebug", "Camera position: (${cameraPose.tx()}, ${cameraPose.ty()}, ${cameraPose.tz()})")

                    // Create multiple points at different distances in front of the camera
                    // to increase chances of finding a good hit point
                    // Use a wider range of distances to better handle different room sizes
                    // Include closer distances for small rooms and further distances for large rooms
                    val distances = listOf(-0.5f, -1.0f, -1.5f, -2.0f, -2.5f, -3.0f, -3.5f, -4.0f, -4.5f, -5.0f)
                    var bestSyntheticHitPose: Pose? = null
                    var bestSyntheticHitPlane: Plane? = null
                    var bestDistance = Float.MAX_VALUE

                    // Log all vertical planes for debugging
                    Log.d("SyntheticHitDebug", "Listing all vertical planes:")
                    var planeIndex = 0
                    for (plane in allPlanes) {
                        if (plane.trackingState == TrackingState.TRACKING && plane.type == Plane.Type.VERTICAL) {
                            Log.d("SyntheticHitDebug", "Vertical plane $planeIndex:")
                            Log.d("SyntheticHitDebug", "  - Size: ${plane.extentX}m x ${plane.extentZ}m")
                            Log.d("SyntheticHitDebug", "  - Position: (${plane.centerPose.tx()}, ${plane.centerPose.ty()}, ${plane.centerPose.tz()})")
                            planeIndex++
                        }
                    }

                    Log.d("TapDetectionDebug", "Trying synthetic hit generation with ${distances.size} different distances")
                    Log.d("TapDetectionDebug", "Camera position: (${cameraPose.tx()}, ${cameraPose.ty()}, ${cameraPose.tz()})")

                    for (distance in distances) {
                        // Create a point at the specified distance in front of the camera
                        val translation = floatArrayOf(0f, 0f, distance)
                        val rotatedTranslation = FloatArray(3)
                        cameraPose.transformPoint(translation, 0, rotatedTranslation, 0)
                        Log.d("TapDetectionDebug", "Trying fallback synthetic hit at distance $distance: (${rotatedTranslation[0]}, ${rotatedTranslation[1]}, ${rotatedTranslation[2]})")

                        // Create a pose at this position
                        val hitPose = Pose(rotatedTranslation, cameraPose.rotationQuaternion)

                        // Find the closest vertical plane to use for the hit
                        var closestPlane: Plane? = null
                        var minDistance = Float.MAX_VALUE
                        var planeIndex = 0

                        // Only consider vertical planes for this fallback
                        for (plane in allPlanes) {
                            if (plane.trackingState == TrackingState.TRACKING && plane.type == Plane.Type.VERTICAL) {
                                val distance = DistanceUtils.calculateDistance(hitPose, plane.centerPose)
                                Log.d("TapDetectionDebug", "Fallback - Vertical plane $planeIndex: distance=$distance")

                                if (distance < minDistance) {
                                    minDistance = distance
                                    closestPlane = plane
                                }
                                planeIndex++
                            }
                        }

                        // If we found a vertical plane and it's closer than our previous best, use it
                        if (closestPlane != null && minDistance < bestDistance) {
                            bestDistance = minDistance
                            bestSyntheticHitPose = hitPose
                            bestSyntheticHitPlane = closestPlane
                            Log.d("TapDetectionDebug", "Found better fallback synthetic hit at distance $distance, distance to plane: $minDistance")
                        }
                    }

                    // If we found a good vertical plane, use it
                    if (bestSyntheticHitPlane != null && bestSyntheticHitPose != null) {
                        Log.d("SyntheticHitDebug", "Selected best vertical plane for fallback: distance=$bestDistance")
                        Log.d("SyntheticHitDebug", "Plane center: (${bestSyntheticHitPlane.centerPose.tx()}, ${bestSyntheticHitPlane.centerPose.ty()}, ${bestSyntheticHitPlane.centerPose.tz()})")
                        Log.d("TapDetectionDebug", "Using best fallback synthetic hit with vertical plane")

                        // Use the best synthetic hit we found
                        usingSyntheticHit = true
                        syntheticHitPose = bestSyntheticHitPose
                        syntheticHitPlane = bestSyntheticHitPlane

                        Log.d("SyntheticHitDebug", "Fallback synthetic hit flags set: usingSyntheticHit=$usingSyntheticHit")
                        Log.d("TapDetectionDebug", "Fallback synthetic hit flags set for image placement")

                        // Place image immediately
                        try {
                            Log.d("SyntheticHitDebug", "Attempting immediate image placement with synthetic hit")
                            Log.d("TapDetectionDebug", "Attempting immediate image placement with fallback synthetic hit")

                            // Create anchor from the synthetic hit pose on the closest plane
                            imageAnchor = syntheticHitPlane!!.createAnchor(syntheticHitPose!!)
                            Log.d("SyntheticHitDebug", "Successfully created anchor from synthetic hit for immediate placement")
                            Log.d("SyntheticHitDebug", "Anchor pose: (${imageAnchor!!.pose.tx()}, ${imageAnchor!!.pose.ty()}, ${imageAnchor!!.pose.tz()})")
                            Log.d("TapDetectionDebug", "Successfully created anchor from fallback synthetic hit")

                            // Update image position to match anchor
                            currentImage?.updateModelMatrix(imageAnchor!!.pose)
                            Log.d("SyntheticHitDebug", "Updated image model matrix with anchor pose")
                            Log.d("TapDetectionDebug", "Updated image position with anchor pose")

                            // Notify listener that image was placed
                            sessionListener?.onImagePlaced()
                            Log.d("SyntheticHitDebug", "Notified listener that image was placed")
                            Log.d("TapDetectionDebug", "Notified UI that image was placed")

                            // Show toast
                            Handler(Looper.getMainLooper()).post {
                                MessageUtils.showToast(
                                    context,
                                    "Image placed on wall! You can adjust it with gestures.",
                                    Toast.LENGTH_LONG
                                )
                            }

                            // Reset synthetic hit flags
                            usingSyntheticHit = false
                            syntheticHitPose = null
                            syntheticHitPlane = null

                            // Also reset the image placement flag
                            isPlacingImage = false

                            Log.d("SyntheticHitDebug", "Reset synthetic hit flags after immediate placement")
                            Log.d("TapDetectionDebug", "Reset synthetic hit flags and isPlacingImage flag after successful placement")

                            // Successfully placed using synthetic hit
                            Log.d("SyntheticHitDebug", "Successfully completed immediate image placement with synthetic hit")
                            Log.d("TapDetectionDebug", "Successfully completed image placement with fallback synthetic hit")
                            return  // This is now safe because we're outside of any lambda
                        } catch (e: Exception) {
                            Log.e("ARSessionManager", "Error placing image with synthetic hit", e)
                            Log.e("SyntheticHitDebug", "Failed to place image with synthetic hit", e)
                            Log.e("SyntheticHitDebug", "Exception details: ${e.javaClass.name}: ${e.message}")
                            Log.e("TapDetectionDebug", "Failed to place image with fallback synthetic hit: ${e.message}")

                            // Show error message
                            Handler(Looper.getMainLooper()).post {
                                MessageUtils.showToast(
                                    context,
                                    "Error placing image: ${e.message}. Try tapping directly on a wall.",
                                    Toast.LENGTH_LONG
                                )
                            }

                            // Reset the image placement flag even if there was an error
                            isPlacingImage = false
                            Log.d("TapDetectionDebug", "Reset isPlacingImage flag after fallback placement error")
                        }
                    } else {
                        // No vertical planes close enough
                        Handler(Looper.getMainLooper()).post {
                            MessageUtils.showToast(
                                context,
                                "Move closer to a wall and tap directly on it",
                                Toast.LENGTH_LONG
                            )
                        }

                        // Reset the image placement flag since we couldn't place the image
                        isPlacingImage = false
                        Log.d("TapDetectionDebug", "Reset isPlacingImage flag - no suitable vertical planes found")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("ARSessionManager", "Error processing tap event", e)
            Log.e("TapDetectionDebug", "Error processing tap event: ${e.message}")

            // Reset the image placement flag in case of error
            isPlacingImage = false
            Log.d("TapDetectionDebug", "Reset isPlacingImage flag after tap processing error")

            // Show error toast
            val handler = Handler(Looper.getMainLooper())
            handler.post {
                Toast.makeText(
                    context,
                    "Error: ${e.message}",
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

    fun handleScroll(e1: MotionEvent, e2: MotionEvent, distanceX: Float, distanceY: Float) {
        if (isDragging) {
            try {
                // Get the current frame for raycasting
                val frame = arSession.update() ?: return

                // Calculate the new screen position based on the drag
                val screenWidth = arRenderer.getViewportWidth().toFloat()
                val screenHeight = arRenderer.getViewportHeight().toFloat()

                // Get the center of the screen for raycasting
                val centerX = screenWidth / 2f
                val centerY = screenHeight / 2f

                // Perform a hit test from the center of the screen
                val hitResult = performHitTest(frame, centerX, centerY)

                if (hitResult != null) {
                    // We have a hit on a vertical plane
                    val hitPose = hitResult.hitPose

                    if (!isGridFixed && currentGrid != null) {
                        // Update grid anchor to the new hit position
                        // First detach the old anchor
                        gridAnchor?.detach()

                        // Create a new anchor at the hit location
                        gridAnchor = hitResult.createAnchor()

                        // Log the new anchor position
                        Log.d("ARSessionManager", "Updated grid anchor to: " +
                                "(${gridAnchor!!.pose.tx()}, ${gridAnchor!!.pose.ty()}, ${gridAnchor!!.pose.tz()})")

                        // Update grid position to match the new anchor
                        currentGrid?.updateModelMatrix(gridAnchor!!.pose)
                    } else if (!isImageFixed && currentImage != null) {
                        // Update image anchor to the new hit position
                        // First detach the old anchor if it exists
                        imageAnchor?.detach()

                        // Create a new anchor at the hit location
                        imageAnchor = hitResult.createAnchor()

                        // Log the new anchor position
                        Log.d("ARSessionManager", "Updated image anchor to: " +
                                "(${imageAnchor!!.pose.tx()}, ${imageAnchor!!.pose.ty()}, ${imageAnchor!!.pose.tz()})")

                        // Update image position to match the new anchor
                        currentImage?.updateModelMatrix(imageAnchor!!.pose)
                    }
                } else {
                    // No hit on a vertical plane, use the old simplified approach as fallback
                    Log.d("ARSessionManager", "No vertical plane hit during drag, using simplified movement")

                    if (!isGridFixed && currentGrid != null) {
                        // Use simplified movement as fallback
                        currentGrid?.translate(distanceX * 0.01f, distanceY * 0.01f)
                    } else if (!isImageFixed && currentImage != null) {
                        // Use simplified movement as fallback
                        currentImage?.translate(distanceX * 0.01f, distanceY * 0.01f)
                    }
                }
            } catch (e: Exception) {
                Log.e("ARSessionManager", "Error during scroll handling", e)

                // Fallback to simplified movement in case of error
                if (!isGridFixed && currentGrid != null) {
                    currentGrid?.translate(distanceX * 0.01f, distanceY * 0.01f)
                } else if (!isImageFixed && currentImage != null) {
                    currentImage?.translate(distanceX * 0.01f, distanceY * 0.01f)
                }
            }
        }
    }

    fun handleTouchEnd() {
        isDragging = false
        isScaling = false
        isRotating = false
        Log.d("ARSessionManager", "Touch interaction ended")
    }

    fun startDragging() {
        isDragging = true
        Log.d("ARSessionManager", "Started dragging")
    }

    fun startScaling() {
        isScaling = true
    }

    fun endScaling() {
        isScaling = false
    }

    fun handleScale(scaleFactor: Float) {
        if (!isScaling) return

        if (!isGridFixed && currentGrid != null) {
            // Scale the grid
            currentGrid?.scale(scaleFactor)
        } else if (!isImageFixed && currentImage != null) {
            // Scale the image, respecting the aspect ratio setting
            currentImage?.scale(scaleFactor, settingsManager.preserveAspectRatio)
        }
    }

    fun handleRotation(rotationDegrees: Float) {
        isRotating = true

        if (!isGridFixed && currentGrid != null) {
            // Rotate the grid (if needed - may not be necessary for grids)
            // currentGrid?.rotate(rotationDegrees)
        } else if (!isImageFixed && currentImage != null) {
            // Rotate the image
            currentImage?.rotate(rotationDegrees)
        }
    }

    fun fixGrid() {
        if (currentGrid != null) {
            isGridFixed = true
            // Log anchor information when fixing grid
            logAnchorInfo("Grid fixed", gridAnchor)
        }
    }

    fun releaseGrid() {
        isGridFixed = false
        Log.d("ARSessionManager", "Grid released")
    }

    fun fixImage() {
        if (currentImage != null) {
            isImageFixed = true
            // Log anchor information when fixing image
            logAnchorInfo("Image fixed", imageAnchor)

            // Show a confirmation toast and update UI
            Handler(Looper.getMainLooper()).post {
                // Notify listener that image was fixed
                sessionListener?.onImageFixed()

                Toast.makeText(
                    context,
                    "Image position locked. Use 'Release Image' to adjust again.",
                    Toast.LENGTH_LONG
                ).show()
            }
        }
    }

    fun releaseImage() {
        isImageFixed = false
        Log.d("ARSessionManager", "Image released")

        // Update UI when image is released
        Handler(Looper.getMainLooper()).post {
            // Notify listener that image was released
            sessionListener?.onImageReleased()

            MessageUtils.showToast(context, "Image released. You can now adjust its position.", Toast.LENGTH_SHORT)
        }
    }

    /**
     * Logs detailed information about an anchor for debugging purposes
     * @param context A string describing the context of the anchor (e.g., "Grid fixed")
     * @param anchor The anchor to log information about
     */
    private fun logAnchorInfo(context: String, anchor: Anchor?) {
        anchor?.let { a ->
            val pose = a.pose
            val tx = pose.tx()
            val ty = pose.ty()
            val tz = pose.tz()

            // Get rotation as quaternion
            val qx = pose.qx()
            val qy = pose.qy()
            val qz = pose.qz()
            val qw = pose.qw()

            // Calculate normal vector (assuming Y is up in world space)
            // This helps determine if the anchor is on a vertical surface
            val normalX = 2.0f * (qx * qz + qw * qy)
            val normalY = 2.0f * (qy * qz - qw * qx)
            val normalZ = 1.0f - 2.0f * (qx * qx + qy * qy)

            // Calculate angle with vertical (Y) axis
            val angleWithVertical = Math.toDegrees(Math.acos(normalY.toDouble())).toFloat()

            // Log detailed anchor information
            Log.d("ARSessionManager", "$context - Anchor details:")
            Log.d("ARSessionManager", "  Position: ($tx, $ty, $tz)")
            Log.d("ARSessionManager", "  Rotation (quaternion): ($qx, $qy, $qz, $qw)")
            Log.d("ARSessionManager", "  Surface normal: ($normalX, $normalY, $normalZ)")
            Log.d("ARSessionManager", "  Angle with vertical: $angleWithVertical degrees")

            // Check if the anchor is on a vertical surface (90° ± 15° from vertical)
            val isVertical = angleWithVertical in 75.0f..105.0f
            Log.d("ARSessionManager", "  Is on vertical surface: $isVertical")

            // If not on a vertical surface, log a warning
            if (!isVertical) {
                Log.w("ARSessionManager", "  WARNING: Anchor may not be on a vertical wall!")
            }
        } ?: run {
            Log.w("ARSessionManager", "$context - No anchor available")
        }
    }

    fun loadImageFromUri(uri: Uri): Bitmap? {
        return try {
            context.contentResolver.openInputStream(uri)?.use { inputStream ->
                // Read entire stream into byte array first
                val bytes = inputStream.readBytes()
                decodeSampledBitmapFromBytes(bytes)
            }
        } catch (e: IOException) {
            Log.e("ARSessionManager", "Error loading image", e)
            null
        }
    }



    private fun decodeSampledBitmapFromBytes(bytes: ByteArray): Bitmap? { // Return nullable Bitmap

        // --- First Pass: Get original dimensions ---
        val options = BitmapFactory.Options().apply {
            inJustDecodeBounds = true // Don't load the bitmap, just get dimensions
        }
        BitmapFactory.decodeByteArray(bytes, 0, bytes.size, options)

        val originalWidth = options.outWidth
        val originalHeight = options.outHeight

        if (originalWidth <= 0 || originalHeight <= 0) {
            // Could not decode bounds, invalid image data?
            return null
        }

        // --- Calculate inSampleSize ---
        options.inSampleSize = calculateInSampleSize(options, desiredMaxImageDimension, desiredMaxImageDimension)

        // --- Second Pass: Decode the actual bitmap with sampling ---
        options.inJustDecodeBounds = false // Now load the bitmap data
        // You might want to set other options here too, like inPreferredConfig
        // options.inPreferredConfig = Bitmap.Config.ARGB_8888

        return BitmapFactory.decodeByteArray(bytes, 0, bytes.size, options) // This returns the sampled bitmap
    }



// --- Optional: Further Precise Scaling (if needed after sampling) ---
// If you need the bitmap to be EXACTLY within the bounds AFTER decoding with inSampleSize
// (which might still leave it slightly larger), you can add a final scaling step.
// However, the inSampleSize decoding is the MOST important step for memory saving.

    private fun scaleBitmapIfNeeded(bitmap: Bitmap, maxDimension: Int): Bitmap {
        val currentWidth = bitmap.width
        val currentHeight = bitmap.height

        if (currentWidth <= maxDimension && currentHeight <= maxDimension) {
            return bitmap // No scaling needed
        }

        val scaleFactor = max(currentWidth.toFloat() / maxDimension, currentHeight.toFloat() / maxDimension)

        val targetWidth = (currentWidth / scaleFactor).roundToInt()
        val targetHeight = (currentHeight / scaleFactor).roundToInt()

        // Create a new scaled bitmap. This uses more memory temporarily.
        val scaledBitmap = Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, true)

        // Important: If the original bitmap is no longer needed, recycle it
        // ONLY if you are sure it's not used elsewhere and you created it yourself.
        // Be cautious with recycling if the bitmap came from an external source.
        // if (scaledBitmap != bitmap) { // Check if scaling actually happened
        //    bitmap.recycle()
        // }

        return scaledBitmap
    }

    private fun calculateTargetDimensions(
        originalWidth: Int,
        originalHeight: Int,
        maxDimension: Int
    ): Pair<Int, Int> {
        return if (originalWidth > originalHeight) {
            val ratio = originalHeight.toFloat() / originalWidth
            maxDimension to (maxDimension * ratio).toInt()
        } else {
            val ratio = originalWidth.toFloat() / originalHeight
            (maxDimension * ratio).toInt() to maxDimension
        }
    }

    private fun decodeSampledBitmapFromStream(
        inputStream: InputStream,
        reqWidth: Int,
        reqHeight: Int
    ): Bitmap? { // <--- CHANGE return type to Bitmap?

        // Mark the stream ONLY if it supports it. Otherwise, this two-pass method won't work
        // reliably without reading the whole stream into memory first (like decodeSampledBitmapFromBytes).
        if (!inputStream.markSupported()) {
            Log.e("ARSessionManager", "InputStream does not support mark/reset. Cannot perform efficient two-pass decoding on this stream.")
            // Consider an alternative: read to ByteArray and use decodeSampledBitmapFromBytes
            try {
                val bytes = inputStream.readBytes() // Reads entire stream - potentially memory intensive!
                return decodeSampledBitmapFromBytes(bytes)
            } catch (e: IOException) {
                Log.e("ARSessionManager", "Error reading non-markable stream to bytes", e)
                return null
            } catch (oom: OutOfMemoryError) {
                Log.e("ARSessionManager", "OutOfMemoryError reading non-markable stream to bytes", oom)
                return null // Explicitly handle OOM
            }
        }

        // Mark the stream's current position. Parameter is readlimit.
        inputStream.mark(10 * 1024 * 1024) // Mark a large enough buffer (e.g., 10MB)

        try {
            // --- First Pass: Get original dimensions ---
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true // Don't load the bitmap, just get dimensions
            }
            BitmapFactory.decodeStream(inputStream, null, options) // Decode bounds

            val originalWidth = options.outWidth
            val originalHeight = options.outHeight

            if (originalWidth <= 0 || originalHeight <= 0) {
                Log.e("ARSessionManager", "Could not decode bounds from stream.")
                // Don't forget to reset the stream even on error if marked
                try { inputStream.reset() } catch (resetEx: IOException) { Log.e("ARSessionManager", "Failed to reset stream after bounds error", resetEx) }
                return null // Invalid bounds
            }

            // --- Calculate inSampleSize ---
            options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight)

            // --- Second Pass: Decode the actual bitmap with sampling ---
            options.inJustDecodeBounds = false // Now load the bitmap data

            // Reset the stream to the marked position before decoding again
            try {
                inputStream.reset()
            } catch (resetEx: IOException) {
                Log.e("ARSessionManager", "Failed to reset input stream for second pass", resetEx)
                return null // Can't decode if reset failed
            }

            // Decode the actual bitmap
            return BitmapFactory.decodeStream(inputStream, null, options) // Returns Bitmap? which matches the function's return type now

        } catch (e: Exception) { // Catch generic exceptions during decoding
            Log.e("ARSessionManager", "Error during bitmap stream decoding", e)
            // Attempt to reset if an exception occurred mid-process (best effort)
            try { if (inputStream.markSupported()) inputStream.reset() } catch (ignored: IOException) {}
            return null
        } catch (oom: OutOfMemoryError) { // Catch OutOfMemoryError specifically
            Log.e("ARSessionManager", "OutOfMemoryError during bitmap stream decoding", oom)
            try { if (inputStream.markSupported()) inputStream.reset() } catch (ignored: IOException) {}
            return null
        }
        // Note: inputStream is likely closed by the caller (e.g., using .use {} block)
    }

    private fun calculateInSampleSize(
        options: BitmapFactory.Options,
        reqWidth: Int,
        reqHeight: Int
    ): Int {
        // Raw height and width of image
        val height = options.outHeight
        val width = options.outWidth
        var inSampleSize = 1

        if (height > reqHeight || width > reqWidth) {
            val halfHeight = height / 2
            val halfWidth = width / 2

            // Calculate the largest inSampleSize value that is a power of 2 and keeps both
            // height and width larger than the requested height and width.
            while (halfHeight / inSampleSize >= reqHeight && halfWidth / inSampleSize >= reqWidth) {
                inSampleSize *= 2
            }
        }

        return inSampleSize
    }

    private fun performHitTest(frame: Frame, x: Float, y: Float): HitResult? {
        // Log all hit test attempts for debugging
        Log.d("ARSessionManager", "==== PERFORMING HIT TEST ====")
        Log.d("ARSessionManager", "Hit test at screen coordinates: ($x, $y)")
        Log.d("ARSessionManager", "Screen dimensions: ${arRenderer.getViewportWidth()}x${arRenderer.getViewportHeight()}")
        Log.d("ARSessionManager", "Device orientation: ${context.resources.configuration.orientation}")
        Log.d("ARSessionManager", "Camera pose: ${frame.camera.pose.tx()}, ${frame.camera.pose.ty()}, ${frame.camera.pose.tz()}")

        // Enhanced logging for tap detection debugging
        Log.d("TapDetectionDebug", "==== HIT TEST STARTED ====")
        Log.d("TapDetectionDebug", "Hit test at screen coordinates: ($x, $y)")
        Log.d("TapDetectionDebug", "Image ready to place: ${!isImageFixed && currentImage != null}")
        Log.d("TapDetectionDebug", "Is placing image: $isPlacingImage")

        // First, check if there are any planes detected at all
        val allPlanes = frame.getUpdatedTrackables(Plane::class.java)
        val trackingPlanes = allPlanes.count { it.trackingState == TrackingState.TRACKING }
        val verticalPlanes = allPlanes.count {
            it.trackingState == TrackingState.TRACKING && it.type == Plane.Type.VERTICAL
        }
        val horizontalPlanes = allPlanes.count {
            it.trackingState == TrackingState.TRACKING &&
            (it.type == Plane.Type.HORIZONTAL_UPWARD_FACING || it.type == Plane.Type.HORIZONTAL_DOWNWARD_FACING)
        }

        Log.d("ARSessionManager", "Plane statistics for hit test:")
        Log.d("ARSessionManager", "- Total planes: ${allPlanes.size}")
        Log.d("ARSessionManager", "- Tracking planes: $trackingPlanes")
        Log.d("ARSessionManager", "- Vertical planes: $verticalPlanes")
        Log.d("ARSessionManager", "- Horizontal planes: $horizontalPlanes")

        // Enhanced logging for tap detection debugging
        Log.d("TapDetectionDebug", "Plane statistics:")
        Log.d("TapDetectionDebug", "- Total planes: ${allPlanes.size}")
        Log.d("TapDetectionDebug", "- Tracking planes: $trackingPlanes")
        Log.d("TapDetectionDebug", "- Vertical planes: $verticalPlanes")
        Log.d("TapDetectionDebug", "- Horizontal planes: $horizontalPlanes")

        // Try multiple hit test points around the tap location for better results
        // This creates a larger grid of test points centered on the tap location
        val hitPoints = mutableListOf<Pair<Float, Float>>()

        // Add the original tap point with higher priority
        hitPoints.add(Pair(x, y))

        // Get screen dimensions for better offset calculation
        val screenWidth = arRenderer.getViewportWidth().toFloat()
        val screenHeight = arRenderer.getViewportHeight().toFloat()

        // Calculate adaptive offsets based on screen size (smaller screens need smaller offsets)
        val screenSizeFactor = min(screenWidth, screenHeight) / 1000f // Normalize to a reference size

        // Add points in a larger grid around the tap with increased offsets for better coverage
        // Use both percentage-based and fixed offsets for better adaptability
        val percentageOffsets = listOf(0.02f, 0.05f, 0.1f) // 2%, 5%, 10% of screen size
        val fixedOffsets = listOf(10f, 20f, 30f, 50f, 70f, 100f)  // Added larger offsets

        // Add percentage-based offsets
        for (percentage in percentageOffsets) {
            val offsetX = screenWidth * percentage
            val offsetY = screenHeight * percentage

            hitPoints.add(Pair(x + offsetX, y))
            hitPoints.add(Pair(x - offsetX, y))
            hitPoints.add(Pair(x, y + offsetY))
            hitPoints.add(Pair(x, y - offsetY))
            // Add diagonal points
            hitPoints.add(Pair(x + offsetX, y + offsetY))
            hitPoints.add(Pair(x - offsetX, y - offsetY))
            hitPoints.add(Pair(x + offsetX, y - offsetY))
            hitPoints.add(Pair(x - offsetX, y + offsetY))
        }

        // Add fixed offsets scaled by screen size
        for (offset in fixedOffsets) {
            val scaledOffset = offset * screenSizeFactor

            hitPoints.add(Pair(x + scaledOffset, y))
            hitPoints.add(Pair(x - scaledOffset, y))
            hitPoints.add(Pair(x, y + scaledOffset))
            hitPoints.add(Pair(x, y - scaledOffset))
            // Add diagonal points
            hitPoints.add(Pair(x + scaledOffset, y + scaledOffset))
            hitPoints.add(Pair(x - scaledOffset, y - scaledOffset))
            hitPoints.add(Pair(x + scaledOffset, y - scaledOffset))
            hitPoints.add(Pair(x - scaledOffset, y + scaledOffset))
        }

        // Add more points in the center area for better precision
        val centerOffsets = listOf(5f, 15f, 25f)
        for (offset in centerOffsets) {
            val scaledOffset = offset * screenSizeFactor

            hitPoints.add(Pair(x + scaledOffset, y))
            hitPoints.add(Pair(x - scaledOffset, y))
            hitPoints.add(Pair(x, y + scaledOffset))
            hitPoints.add(Pair(x, y - scaledOffset))
        }

        // Add the center of the screen as a fallback point
        hitPoints.add(Pair(screenWidth / 2f, screenHeight / 2f))

        Log.d("TapDetectionDebug", "Created ${hitPoints.size} hit test points")

        // Try each hit point
        val allHits = mutableListOf<HitResult>()

        for (point in hitPoints) {
            try {
                val pointHits = frame.hitTest(point.first, point.second)
                if (pointHits.isNotEmpty()) {
                    Log.d("ARSessionManager", "Hit found at point (${point.first}, ${point.second})")
                    allHits.addAll(pointHits)
                }
            } catch (e: Exception) {
                Log.e("ARSessionManager", "Error during hit test at point (${point.first}, ${point.second}): ${e.message}")
            }
        }

        Log.d("ARSessionManager", "Multi-point hit test returned ${allHits.size} total results")

        // Store potential hits for fallback
        var bestVerticalPlaneHit: HitResult? = null
        var bestHorizontalPlaneHit: HitResult? = null
        var bestAnyHit: HitResult? = null

        // If we have hits, process them
        if (allHits.isNotEmpty()) {
            Log.d("TapDetectionDebug", "Processing ${allHits.size} hit results")

            // First try to find vertical planes (our primary target)
            // Create a list to store all vertical plane hits for better selection
            val verticalPlaneHits = mutableListOf<HitResult>()

            for (hit in allHits) {
                try {
                    val trackable = hit.trackable
                    if (trackable is Plane && trackable.trackingState == TrackingState.TRACKING) {
                        // Log all plane hits for debugging
                        Log.d("ARSessionManager", "Hit on plane - Type: ${trackable.type}, " +
                                "Size: ${trackable.extentX}m x ${trackable.extentZ}m, " +
                                "TrackingState: ${trackable.trackingState}, " +
                                "Hit position: (${hit.hitPose.tx()}, ${hit.hitPose.ty()}, ${hit.hitPose.tz()})")

                        Log.d("TapDetectionDebug", "Hit on plane - Type: ${trackable.type}, " +
                                "Size: ${trackable.extentX}m x ${trackable.extentZ}m, " +
                                "Hit position: (${hit.hitPose.tx()}, ${hit.hitPose.ty()}, ${hit.hitPose.tz()})")

                        // Accept any plane type, but prioritize vertical planes
                        if (trackable.type == Plane.Type.VERTICAL) {
                            // Collect all vertical plane hits for better selection
                            verticalPlaneHits.add(hit)
                            Log.d("TapDetectionDebug", "Added vertical plane hit to collection")

                            // Also keep track of the first vertical plane hit as a fallback
                            if (bestVerticalPlaneHit == null) {
                                bestVerticalPlaneHit = hit
                                Log.d("ARSessionManager", "ACCEPTED vertical plane hit")
                            }
                        } else {
                            // Store the first horizontal plane hit as a fallback
                            if (bestHorizontalPlaneHit == null) {
                                bestHorizontalPlaneHit = hit
                                Log.d("ARSessionManager", "Found horizontal plane hit (potential fallback)")
                            }
                        }
                    } else if (bestAnyHit == null) {
                        // Store the first hit of any kind as a last resort
                        bestAnyHit = hit
                        Log.d("ARSessionManager", "Found non-plane hit (last resort fallback)")
                    }
                } catch (e: Exception) {
                    Log.e("ARSessionManager", "Error processing hit result: ${e.message}")
                }
            }

            // If we have vertical plane hits, select the best one
            if (verticalPlaneHits.isNotEmpty()) {
                Log.d("TapDetectionDebug", "Found ${verticalPlaneHits.size} vertical plane hits")

                // Select the best vertical plane hit based on distance from camera
                // This helps ensure we pick the most visible/prominent vertical plane
                var bestHit = verticalPlaneHits.first()
                var bestDistance = Float.MAX_VALUE

                for (hit in verticalPlaneHits) {
                    val hitPose = hit.hitPose
                    val cameraPose = frame.camera.pose
                    val distance = DistanceUtils.calculateDistance(hitPose, cameraPose)

                    // Log each hit's distance for debugging
                    Log.d("TapDetectionDebug", "Vertical plane hit at (${hitPose.tx()}, ${hitPose.ty()}, ${hitPose.tz()}) - distance: $distance")

                    // Find the closest hit that's not too close (at least 0.5m away)
                    if (distance < bestDistance && distance > 0.5f) {
                        bestDistance = distance
                        bestHit = hit
                    }
                }

                Log.d("TapDetectionDebug", "Selected best vertical plane hit at (${bestHit.hitPose.tx()}, ${bestHit.hitPose.ty()}, ${bestHit.hitPose.tz()}) - distance: $bestDistance")
                return bestHit
            }

            // Return the best hit we found, in order of preference
            if (bestVerticalPlaneHit != null) {
                Log.d("ARSessionManager", "Using vertical plane hit")
                return bestVerticalPlaneHit
            }

            if (bestHorizontalPlaneHit != null) {
                Log.d("ARSessionManager", "Using horizontal plane hit as fallback")
                return bestHorizontalPlaneHit
            }

            if (bestAnyHit != null) {
                Log.d("ARSessionManager", "Using ANY hit as last resort - Type: ${bestAnyHit.trackable.javaClass.simpleName}")
                return bestAnyHit
            }

            // If we somehow got here with hits but no best hit, just return the first one
            Log.d("ARSessionManager", "Using first hit as absolute last resort")
            return allHits[0]
        }

        // If we didn't find any suitable planes, log this fact
        Log.d("ARSessionManager", "No suitable planes found in hit test")
        return null
    }

    // calculateDistance method moved to a single implementation at the end of the file

    /**
     * Gets the current frame from the AR session.
     * This method handles session paused state and other error conditions.
     *
     * @return The current Frame, or null if the session is paused or an error occurred
     */
    fun getCurrentFrame(): Frame? {
        // Check if session is paused first to avoid unnecessary work
        if (isSessionPaused) {
            // Skip log spam in normal pause conditions
            return null
        }

        // Note: arSession is a constructor parameter and can't be null here,
        // but we log this fact for debugging purposes
        Log.d("ARSessionManager", "Using ARCore session for frame update")

        // Reset retry count on each new getCurrentFrame call
        frameUpdateRetryCount = 0

        return tryGetCurrentFrame()
    }

    /**
     * Updates the reticle position based on a hit test from the center of the screen.
     * Call this method every frame to keep the reticle updated.
     *
     * @param frame The current ARCore frame
     * @param screenWidth Width of the display in pixels
     * @param screenHeight Height of the display in pixels
     */
    fun updateReticleHitTest(frame: Frame, screenWidth: Int, screenHeight: Int) {
        // Check session state first
        if (isSessionPaused) {
            // Skip hit testing if session is already known to be paused
            return
        }

        try {
            // Perform hit test from center of screen
            val centerX = screenWidth / 2f
            val centerY = screenHeight / 2f

            // Get hit result
            val hitResult = performHitTest(frame, centerX, centerY)

            // Update reticle position in renderer
            if (hitResult != null) {
                // We have a hit, update reticle position
                arRenderer.updateReticlePosition(hitResult.hitPose, true)
            } else {
                // No hit, hide reticle or place it at default distance
                arRenderer.updateReticlePosition(null, false)
            }
        } catch (e: Exception) {
            Log.e("ARSessionManager", "Error updating reticle hit test", e)
            // Hide reticle on error
            arRenderer.updateReticlePosition(null, false)
        }
    }

    fun captureARView(): Bitmap? {
        // Get the current frame from the session
        val frame = arSession.update()

        // Use the renderer's capture method to get the current view
        return arRenderer.captureARFrame(frame)
    }



    // Track the session state
    private var isSessionPaused = false

    /**
     * Returns whether the AR session is currently paused.
     * @return true if session is paused, false otherwise
     */
    fun isSessionPaused(): Boolean {
        return isSessionPaused
    }

    /**
     * External method for MainActivity to check session state.
     * @return true if session is paused, false otherwise
     */
    fun isSessionPausedExt(): Boolean {
        return isSessionPaused
    }

    /**
     * External method for MainActivity to notify session paused.
     */
    fun onSessionPausedExt() {
        isSessionPaused = true
    }

    // Implement a retry mechanism with backoff for frame acquisition
    private var frameUpdateRetryCount = 0
    private val MAX_FRAME_UPDATE_RETRIES = 5 // Increased from 3 to 5 to give more retry attempts

    @Synchronized
    private fun tryGetCurrentFrame(): Frame? {
        try {
            // Do a lightweight check first to avoid the more expensive update() call
            // if the session is already known to be in a paused state
            try {
                // Just access the config to see if it throws SessionPausedException
                arSession.config
            } catch (e: com.google.ar.core.exceptions.SessionPausedException) {
                // Update our state tracking
                isSessionPaused = true
                Log.d("ARSessionManager", "Session is paused (detected during pre-check)")
                return null
            }

            // If we reached here, session appears to be active
            // Now try to get the actual frame update
            val frame = arSession.update()

            // Process the frame we received
            // Note: ARCore's update() method should never return null when successful,
            // but we keep the null check for robustness

            // Reset error flags if we had previous errors
            if (hadFrameUpdateError) {
                Log.d("ARSessionManager", "Successfully got frame after previous errors")
                hadFrameUpdateError = false
            }

            // Reset retry count on success
            frameUpdateRetryCount = 0

            // If we successfully got a frame, make sure isSessionPaused is false
            if (isSessionPaused) {
                Log.d("ARSessionManager", "Successfully got frame but isSessionPaused was true - correcting state")
                isSessionPaused = false
            }

            return frame
        } catch (e: com.google.ar.core.exceptions.SessionPausedException) {
            // Update our state tracking
            isSessionPaused = true

            // Log once but don't spam
            if (!hadFrameUpdateError) {
                Log.d("ARSessionManager", "Session is paused, can't update frame")
                hadFrameUpdateError = true
            }

            // Try to resume the session automatically if we keep getting paused exceptions
            if (frameUpdateRetryCount < MAX_FRAME_UPDATE_RETRIES) {
                frameUpdateRetryCount++
                Log.d("ARSessionManager", "Attempting auto-recovery from paused state, retry $frameUpdateRetryCount")

                // Wait a moment before trying again
                try {
                    Thread.sleep(300)
                } catch (e: InterruptedException) {
                    Log.w("ARSessionManager", "Sleep interrupted during pause recovery", e)
                }

                // Try to force a state reset
                isSessionPaused = false

                // Try again
                return tryGetCurrentFrame()
            }

            return null
        } catch (e: com.google.ar.core.exceptions.CameraNotAvailableException) {
            // Camera became unavailable - likely system took it away
            if (!hadFrameUpdateError) {
                Log.e("ARSessionManager", "Camera not available during frame update", e)
                hadFrameUpdateError = true
            }
            return null
        } catch (e: com.google.ar.core.exceptions.DeadlineExceededException) {
            // Timeout - perhaps system is under load
            if (!hadFrameUpdateError) {
                Log.w("ARSessionManager", "Deadline exceeded getting camera frame - system may be under load", e)
                hadFrameUpdateError = true
            }

            // Try again if we haven't exceeded retry limit
            if (frameUpdateRetryCount < MAX_FRAME_UPDATE_RETRIES) {
                frameUpdateRetryCount++
                Log.d("ARSessionManager", "Retry attempt $frameUpdateRetryCount after deadline exceeded")

                // Longer delay for deadline exceeded
                try {
                    Thread.sleep(500) // Increased from 200ms to 500ms for much longer delay
                } catch (e: InterruptedException) {
                    Log.w("ARSessionManager", "Sleep interrupted during frame retry", e)
                }

                // Recursive call to try again
                return tryGetCurrentFrame()
            }

            return null
        } catch (e: Exception) {
            // Generic catch-all for any other issues
            if (!hadFrameUpdateError) {
                Log.e("ARSessionManager", "Error getting current frame", e)
                hadFrameUpdateError = true
            }

            // Try again if we haven't exceeded retry limit
            if (frameUpdateRetryCount < MAX_FRAME_UPDATE_RETRIES) {
                frameUpdateRetryCount++
                Log.d("ARSessionManager", "Retry attempt $frameUpdateRetryCount after generic error")

                try {
                    Thread.sleep(300)
                } catch (e: InterruptedException) {
                    Log.w("ARSessionManager", "Sleep interrupted during error recovery", e)
                }

                return tryGetCurrentFrame()
            }

            return null
        }
    }

    /**
     * Notify the manager that the session has been resumed.
     * This is called from MainActivity after successfully resuming the session.
     * The method is synchronized to ensure thread safety with getCurrentFrame.
     */
    // Add a counter to track verification attempts
    private var resumeVerificationAttempts = 0
    private val MAX_RESUME_VERIFICATION_ATTEMPTS = 10 // Increased from 7 to 10 for more retries

    @Synchronized
    fun onSessionResumed() {
        Log.d("ARSessionManager", "Session resumed state recorded")

        // Explicitly set isSessionPaused to false immediately to allow frame requests
        isSessionPaused = false

        // Reset verification attempts
        resumeVerificationAttempts = 0

        // Try to verify the session state with multiple attempts if needed
        verifySessionResumedState()

        // Log the current state for debugging
        Log.d("ARSessionManager", "After onSessionResumed call: isSessionPaused = $isSessionPaused")
    }

    /**
     * Helper method to verify the session is truly resumed and retry if necessary
     *
     * This is a critical method that ensures the session is ACTUALLY ready for frame updates
     * after being resumed. There's a race condition in ARCore where the session reports
     * as resumed but the camera pipeline is not fully initialized.
     */
    private fun verifySessionResumedState() {
        // Give ARCore a moment to complete its internal resume operation
        // This is critical as the session resume may take a few frames to complete internally
        try {
            // Use a much longer initial delay to give ARCore camera initialization more time
            // This is critical for letting internal camera pipeline initialize fully
            val delayMs = 1000L + (resumeVerificationAttempts * 500L) // Increased to 1-5 seconds per retry
            Log.d("ARSessionManager", "Delaying for ${delayMs}ms before verification attempt ${resumeVerificationAttempts + 1}")
            Thread.sleep(delayMs)
        } catch (e: InterruptedException) {
            Log.w("ARSessionManager", "Sleep interrupted during resume delay", e)
        }

        // Log the current session state before verification
        Log.d("ARSessionManager", "Before verification: isSessionPaused = $isSessionPaused")

        // Now check if the session is actually in a resumed state before updating our flag
        try {
            // Try to access the config to confirm session isn't paused
            arSession.config

            // First check the camera tracking state - this is fast and can tell us
            // if the camera system is initialized properly
            val frame = arSession.update()
            val trackingState = frame.camera.trackingState
            Log.d("ARSessionManager", "Camera tracking state during verification: $trackingState")

            // Add a much longer delay before frame acquisition attempt
            // This helps ensure the camera pipeline is fully initialized
            try {
                Thread.sleep(300) // Increased from 50ms to 300ms for better camera stabilization
            } catch (e: InterruptedException) {
                Log.w("ARSessionManager", "Sleep interrupted during camera stabilization", e)
            }

            // If we got here without exception, session appears to be resumed
            // But we need to actually try to get a frame to be sure
            try {
                // Get a test frame to verify the session is truly resumed
                val testFrame = arSession.update()

                // Success! Session is truly resumed and can get frames
                // Note: ARCore's update() should never return null on success,
                // it would throw an exception instead
                isSessionPaused = false
                Log.d("ARSessionManager", "Successfully verified session is resumed with a real frame")

                // Try to access camera pose info as final verification
                // This ensures the camera is fully initialized
                try {
                    val cameraPose = testFrame.camera.pose
                    Log.d("ARSessionManager", "Camera pose verified: ${cameraPose.tx()}, ${cameraPose.ty()}, ${cameraPose.tz()}")
                } catch (e: Exception) {
                    Log.w("ARSessionManager", "Got frame but camera pose not available yet", e)
                    // We'll still consider resumed if we got a frame
                }

                // Reset error flags
                hadFrameUpdateError = false

                // Log success state
                Log.d("ARSessionManager", "Verification successful: isSessionPaused = $isSessionPaused")
                return
            } catch (e: com.google.ar.core.exceptions.SessionPausedException) {
                // If we can't get a frame, session is still effectively paused
                Log.w("ARSessionManager", "Cannot get frame after resume - still paused internally")

                // Only set to paused if we've exhausted all retry attempts
                // This prevents prematurely marking the session as paused
                if (resumeVerificationAttempts >= MAX_RESUME_VERIFICATION_ATTEMPTS) {
                    isSessionPaused = true
                    Log.w("ARSessionManager", "Max verification attempts reached, marking session as paused")
                }

                // Try again if we haven't exceeded max attempts
                if (resumeVerificationAttempts < MAX_RESUME_VERIFICATION_ATTEMPTS) {
                    resumeVerificationAttempts++
                    Log.d("ARSessionManager", "Retrying resume verification (attempt ${resumeVerificationAttempts})")
                    verifySessionResumedState()
                    return
                }
            } catch (e: Exception) {
                Log.e("ARSessionManager", "Error getting test frame after resume", e)
                // We'll still mark as resumed for now if we could access config
                // Don't change isSessionPaused here - keep it as is
                Log.d("ARSessionManager", "Keeping session state as is: isSessionPaused = $isSessionPaused")
            }
        } catch (e: com.google.ar.core.exceptions.SessionPausedException) {
            // Session is still paused despite resume attempt
            Log.w("ARSessionManager", "ARCore session still reports paused state after resume call")

            // Only set to paused if we've exhausted all retry attempts
            if (resumeVerificationAttempts >= MAX_RESUME_VERIFICATION_ATTEMPTS) {
                isSessionPaused = true
                Log.w("ARSessionManager", "Max verification attempts reached, marking session as paused")
            }

            // Try again if we haven't exceeded max attempts
            if (resumeVerificationAttempts < MAX_RESUME_VERIFICATION_ATTEMPTS) {
                resumeVerificationAttempts++
                Log.d("ARSessionManager", "Retrying resume verification (attempt ${resumeVerificationAttempts})")
                verifySessionResumedState()
                return
            }
        } catch (e: Exception) {
            // Some other issue - log but don't throw
            Log.e("ARSessionManager", "Error verifying session state after resume", e)
            // Don't automatically set to paused - only if we've exhausted retries
            if (resumeVerificationAttempts >= MAX_RESUME_VERIFICATION_ATTEMPTS) {
                isSessionPaused = true
                Log.w("ARSessionManager", "Max verification attempts reached with errors, marking session as paused")
            }
        }

        // Reset error flags even if we couldn't fully verify
        hadFrameUpdateError = false

        // Log final state
        Log.d("ARSessionManager", "After verification completion: isSessionPaused = $isSessionPaused")
    }

    /**
     * Notify the manager that the session has been paused.
     * This is called from MainActivity before pausing the session.
     * The method is synchronized to ensure thread safety with getCurrentFrame.
     */
    @Synchronized
    fun onSessionPaused() {
        Log.d("ARSessionManager", "Session paused state recorded")

        // Update our state flag immediately to prevent further frame requests
        isSessionPaused = true

        // Clear any pending tap events since they can't be processed when paused
        synchronized(pendingTapEvents) {
            pendingTapEvents.forEach { it.recycle() }
            pendingTapEvents.clear()
            Log.d("ARSessionManager", "Cleared pending tap events due to session pause")
        }

        // No need to verify - it's safer to assume paused state
        // even if there's an issue with the actual pause operation
    }

    // Track if we've had frame update errors to avoid log spam
    private var hadFrameUpdateError = false

    /**
     * Returns the currently active ARGrid object, if one exists.
     */
    fun getCurrentGrid(): ARGrid? {
        return currentGrid
    }

// --- Inside ARSessionManager class ---

    /**
     * Sets the visibility of the detected plane visualizations.
     * @param isVisible True to show planes, false to hide them.
     */
    fun setPlaneVisibility(isVisible: Boolean) {
        // This needs to delegate to the ARRenderer which holds the ARPlaneVisualizer
        arRenderer.setPlaneVisualizerEnabled(isVisible)
        Log.d("ARSessionManager", "Set Plane Visibility: $isVisible")
    }

    fun getCurrentImage(): ARImage? = currentImage

    fun setPreserveAspectRatio(preserve: Boolean) {
        currentImage?.let { image ->
            image.preserveAspectRatio = preserve
        }
    }

    /**
     * Loads an image from a bitmap and prepares it for placement in AR.
     * This method handles all the necessary cleanup of previous images and
     * initializes the new image with proper settings.
     *
     * @param bitmap The bitmap to load as an AR image
     */
    @Synchronized
    fun loadImage(bitmap: Bitmap) {
        // Log the image loading for debugging purposes
        Log.d("ARSessionManager", "==== LOADING IMAGE ====")
        Log.d("ARSessionManager", "Image dimensions: ${bitmap.width}x${bitmap.height}")
        Log.d("ARSessionManager", "Previous image: ${currentImage != null}")
        Log.d("ARSessionManager", "Previous anchor: ${imageAnchor != null}")
        Log.d("SyntheticHitDebug", "==== IMAGE LOADING STARTED ====")
        Log.d("SyntheticHitDebug", "Image dimensions: ${bitmap.width}x${bitmap.height}")
        Log.d("SyntheticHitDebug", "Current state - isImageFixed: $isImageFixed, currentImage: ${currentImage != null}")
        Log.d("TapDetectionDebug", "==== IMAGE LOADING STARTED ====")
        Log.d("TapDetectionDebug", "Loading image with dimensions: ${bitmap.width}x${bitmap.height}")

        // Make sure we're not in the middle of placing an image
        synchronized(this) {
            if (isPlacingImage) {
                Log.d("TapDetectionDebug", "Cancelling any ongoing image placement before loading new image")
                isPlacingImage = false
            }
        }

        try {
            // Release any previous image resources
            if (currentImage != null) {
                Log.d("ARSessionManager", "Releasing previous image resources")
                currentImage?.release()
                currentImage = null
            }

            // Detach any existing image anchor
            if (imageAnchor != null) {
                Log.d("ARSessionManager", "Detaching previous image anchor")
                imageAnchor?.detach()
                imageAnchor = null
            }

            // Create a new ARImage from the bitmap
            currentImage = ARImage(context, bitmap)
            Log.d("ARSessionManager", "Created new ARImage successfully")
            Log.d("TapDetectionDebug", "Created new ARImage successfully")

            // Set the image in the renderer to make it visible
            arRenderer.setImage(currentImage!!)

            // Explicitly verify renderer state
            val imageActive = arRenderer.hasActiveImage()
            Log.d("ARSessionManager", "Verified image active in renderer: $imageActive")
            Log.d("TapDetectionDebug", "Image active in renderer: $imageActive")

            // Double-ensure the image visibility flag is set in renderer
            arRenderer.ensureImageVisibility(true)
            Log.d("ARSessionManager", "Explicitly ensured image visibility")

            // Reset the fixed state
            isImageFixed = false

            // Set default opacity from settings
            currentImage?.opacity = settingsManager.defaultImageOpacity
            Log.d("ARSessionManager", "Set default opacity: ${settingsManager.defaultImageOpacity}")

            // Set preserve aspect ratio from settings
            currentImage?.preserveAspectRatio = settingsManager.preserveAspectRatio
            Log.d("ARSessionManager", "Set preserve aspect ratio: ${settingsManager.preserveAspectRatio}")

            // Notify listener about image state change
            sessionListener?.onImagePlaced()
            Log.d("ARSessionManager", "Notified listener about image placement")

            // Log success
            Log.d("ARSessionManager", "Image loaded successfully and ready for placement")

            // Additional logging for synthetic hit debugging
            Log.d("SyntheticHitDebug", "==== IMAGE LOADING COMPLETED ====")
            Log.d("SyntheticHitDebug", "Image loaded successfully and ready for placement")
            Log.d("SyntheticHitDebug", "Updated state - isImageFixed: $isImageFixed, currentImage: ${currentImage != null}")
            Log.d("SyntheticHitDebug", "Image ready to place: ${!isImageFixed && currentImage != null}")

            Log.d("TapDetectionDebug", "==== IMAGE LOADING COMPLETED ====")
            Log.d("TapDetectionDebug", "Image loaded successfully and ready for placement")
            Log.d("TapDetectionDebug", "isImageFixed: $isImageFixed, currentImage: ${currentImage != null}")

            // Wait a short moment to ensure ARCore has had time to process the latest frame
            // This helps with more reliable placement
            try {
                Thread.sleep(100)
            } catch (e: InterruptedException) {
                Log.w("ARSessionManager", "Sleep interrupted during image loading stabilization", e)
            }

            // Try to place the image immediately if there are vertical planes
            // This is done on a separate thread to avoid blocking the UI
            Thread {
                try {
                    Log.d("TapDetectionDebug", "Starting immediate placement attempt on background thread")
                    tryPlaceImageImmediately()
                    Log.d("TapDetectionDebug", "Completed immediate placement attempt on background thread")
                } catch (e: Exception) {
                    Log.e("TapDetectionDebug", "Error in background immediate placement thread: ${e.message}")
                }
            }.start()

            // Show a toast guiding the user to tap on a wall
            Handler(Looper.getMainLooper()).post {
                MessageUtils.showToast(
                    context,
                    "Image loaded. Tap on a wall to place it. If placement isn't working, move closer to a wall, ensuring it's highlighted green.",
                    Toast.LENGTH_LONG
                )
            }
        } catch (e: Exception) {
            Log.e("ARSessionManager", "Error loading image", e)
            Log.e("TapDetectionDebug", "Error loading image: ${e.message}")
            Log.e("TapDetectionDebug", "Stack trace: ${e.stackTraceToString()}")

            // Clean up in case of error
            currentImage?.release()
            currentImage = null

            imageAnchor?.detach()
            imageAnchor = null

            // Reset renderer state
            arRenderer.removeImage()

            // Reset the image placement flag
            synchronized(this) {
                isPlacingImage = false
            }

            // No need to notify listener here as there's no image to place

            // Notify the user
            Handler(Looper.getMainLooper()).post {
                MessageUtils.showToast(
                    context,
                    "Error loading image: ${e.message}",
                    Toast.LENGTH_SHORT
                )
            }
        }
    }

    /**
     * Attempts to place the image immediately after loading if there are vertical planes available.
     * This helps with the tap-to-place functionality by trying to place the image automatically.
     */
    private fun tryPlaceImageImmediately() {
        // Check preconditions with detailed logging
        if (isSessionPaused) {
            Log.d("TapDetectionDebug", "Cannot place image immediately: session is paused")
            return
        }

        if (currentImage == null) {
            Log.d("TapDetectionDebug", "Cannot place image immediately: no image loaded")
            return
        }

        if (isImageFixed) {
            Log.d("TapDetectionDebug", "Cannot place image immediately: image is already fixed")
            return
        }

        // Use synchronized block to prevent race conditions with the isPlacingImage flag
        synchronized(this) {
            if (isPlacingImage) {
                Log.d("TapDetectionDebug", "Cannot place image immediately: already placing an image")
                return
            }

            // Set the flag to indicate we're attempting to place the image
            isPlacingImage = true
            Log.d("TapDetectionDebug", "Setting isPlacingImage flag to true for immediate placement attempt")
        }

        try {
            Log.d("TapDetectionDebug", "==== ATTEMPTING IMMEDIATE IMAGE PLACEMENT ====")

            // Get the current frame
            val frame = arSession.update()

            // Check if there are any vertical planes
            val allPlanes = frame.getUpdatedTrackables(Plane::class.java)
            val trackingPlanes = allPlanes.count { it.trackingState == TrackingState.TRACKING }
            val verticalPlanes = allPlanes.count {
                it.trackingState == TrackingState.TRACKING && it.type == Plane.Type.VERTICAL
            }

            Log.d("TapDetectionDebug", "Immediate placement - Total tracking planes: $trackingPlanes")
            Log.d("TapDetectionDebug", "Immediate placement - Vertical planes: $verticalPlanes")

            // Log details about each vertical plane for debugging
            if (verticalPlanes > 0) {
                Log.d("TapDetectionDebug", "Vertical plane details for immediate placement:")
                allPlanes.filter { it.trackingState == TrackingState.TRACKING && it.type == Plane.Type.VERTICAL }
                    .forEachIndexed { index, plane ->
                        Log.d("TapDetectionDebug", "  Plane $index:")
                        Log.d("TapDetectionDebug", "  - Size: ${plane.extentX}m x ${plane.extentZ}m")
                        Log.d("TapDetectionDebug", "  - Position: (${plane.centerPose.tx()}, ${plane.centerPose.ty()}, ${plane.centerPose.tz()})")
                    }
            }

            if (verticalPlanes > 0) {
                // Get the center of the screen
                val screenWidth = arRenderer.getViewportWidth().toFloat()
                val screenHeight = arRenderer.getViewportHeight().toFloat()
                val centerX = screenWidth / 2f
                val centerY = screenHeight / 2f

                Log.d("TapDetectionDebug", "Trying hit test at center of screen: ($centerX, $centerY)")

                // Perform a hit test at the center of the screen
                val hitResult = performHitTest(frame, centerX, centerY)

                if (hitResult != null) {
                    // Get the trackable type for logging
                    val trackableType = if (hitResult.trackable is Plane) {
                        val plane = hitResult.trackable as Plane
                        "Plane (${plane.type})"
                    } else {
                        hitResult.trackable.javaClass.simpleName
                    }

                    Log.d("TapDetectionDebug", "Found hit result for immediate placement on $trackableType")
                    Log.d("TapDetectionDebug", "Hit position: (${hitResult.hitPose.tx()}, ${hitResult.hitPose.ty()}, ${hitResult.hitPose.tz()})")

                    // Create a synthetic tap event at the center of the screen
                    val syntheticTap = MotionEvent.obtain(
                        SystemClock.uptimeMillis(),
                        SystemClock.uptimeMillis(),
                        MotionEvent.ACTION_DOWN,
                        centerX,
                        centerY,
                        0
                    )

                    // Process the tap event
                    processTapEvent(syntheticTap, frame)

                    // Recycle the event
                    syntheticTap.recycle()

                    Log.d("TapDetectionDebug", "Processed synthetic tap for immediate placement")

                    // Note: isPlacingImage flag will be reset by processTapEvent
                } else {
                    Log.d("TapDetectionDebug", "No hit result found at center of screen, trying alternative positions")

                    // Try additional positions if center hit test failed
                    val testPositions = listOf(
                        Pair(screenWidth * 0.25f, screenHeight * 0.5f),  // Left center
                        Pair(screenWidth * 0.75f, screenHeight * 0.5f),  // Right center
                        Pair(screenWidth * 0.5f, screenHeight * 0.25f),  // Top center
                        Pair(screenWidth * 0.5f, screenHeight * 0.75f)   // Bottom center
                    )

                    var foundHit = false

                    for (pos in testPositions) {
                        if (!foundHit) {
                            Log.d("TapDetectionDebug", "Trying hit test at position (${pos.first}, ${pos.second})")
                            val posHitResult = performHitTest(frame, pos.first, pos.second)

                            if (posHitResult != null) {
                                Log.d("TapDetectionDebug", "Found hit at alternative position (${pos.first}, ${pos.second})")

                                // Create a synthetic tap event at this position
                                val syntheticTap = MotionEvent.obtain(
                                    SystemClock.uptimeMillis(),
                                    SystemClock.uptimeMillis(),
                                    MotionEvent.ACTION_DOWN,
                                    pos.first,
                                    pos.second,
                                    0
                                )

                                // Process the tap event
                                processTapEvent(syntheticTap, frame)

                                // Recycle the event
                                syntheticTap.recycle()

                                foundHit = true
                                Log.d("TapDetectionDebug", "Processed synthetic tap at alternative position")
                                break
                            }
                        }
                    }

                    if (!foundHit) {
                        Log.d("TapDetectionDebug", "No hit found at any test position, falling back to synthetic hit generation")

                        // If we still don't have a hit, try the synthetic hit generation approach
                        // This is similar to the fallback approach in processTapEvent

                        // Get camera position
                        val cameraPose = frame.camera.pose

                        // Create multiple points at different distances in front of the camera
                        val distances = listOf(-1.5f, -2.0f, -2.5f, -3.0f)
                        var bestSyntheticHitPose: Pose? = null
                        var bestSyntheticHitPlane: Plane? = null
                        var bestDistance = Float.MAX_VALUE

                        for (distance in distances) {
                            // Create a point at the specified distance in front of the camera
                            val translation = floatArrayOf(0f, 0f, distance)
                            val rotatedTranslation = FloatArray(3)
                            cameraPose.transformPoint(translation, 0, rotatedTranslation, 0)

                            // Create a pose at this position
                            val hitPose = Pose(rotatedTranslation, cameraPose.rotationQuaternion)

                            // Find the closest vertical plane
                            var closestPlane: Plane? = null
                            var minDistance = Float.MAX_VALUE

                            for (plane in allPlanes) {
                                if (plane.trackingState == TrackingState.TRACKING && plane.type == Plane.Type.VERTICAL) {
                                    val planeDistance = DistanceUtils.calculateDistance(hitPose, plane.centerPose)

                                    if (planeDistance < minDistance) {
                                        minDistance = planeDistance
                                        closestPlane = plane
                                    }
                                }
                            }

                            // If we found a vertical plane and it's closer than our previous best, use it
                            if (closestPlane != null && minDistance < bestDistance) {
                                bestDistance = minDistance
                                bestSyntheticHitPose = hitPose
                                bestSyntheticHitPlane = closestPlane
                            }
                        }

                        // If we found a good vertical plane, use it
                        if (bestSyntheticHitPlane != null && bestSyntheticHitPose != null) {
                            Log.d("TapDetectionDebug", "Using synthetic hit for immediate placement, distance: $bestDistance")

                            try {
                                // Create anchor from the synthetic hit pose on the closest plane
                                imageAnchor = bestSyntheticHitPlane.createAnchor(bestSyntheticHitPose)

                                // Update image position to match anchor
                                currentImage?.updateModelMatrix(imageAnchor!!.pose)

                                // Notify listener that image was placed
                                sessionListener?.onImagePlaced()

                                // Show toast
                                Handler(Looper.getMainLooper()).post {
                                    MessageUtils.showToast(
                                        context,
                                        "Image placed on wall! You can adjust it with gestures.",
                                        Toast.LENGTH_LONG
                                    )
                                }

                                Log.d("TapDetectionDebug", "Successfully placed image using synthetic hit")
                            } catch (e: Exception) {
                                Log.e("TapDetectionDebug", "Error placing image with synthetic hit: ${e.message}")

                                // Reset the image anchor if it was created
                                imageAnchor?.detach()
                                imageAnchor = null
                            }
                        } else {
                            Log.d("TapDetectionDebug", "No suitable vertical plane found for synthetic hit")
                        }
                    }
                }
            } else {
                Log.d("TapDetectionDebug", "No vertical planes available for immediate placement")

                // Show a toast to guide the user
                Handler(Looper.getMainLooper()).post {
                    MessageUtils.showToast(
                        context,
                        "Image loaded! Scan walls by moving your device, then tap on a wall to place the image.",
                        Toast.LENGTH_LONG
                    )
                }
            }
        } catch (e: Exception) {
            Log.e("TapDetectionDebug", "Error trying to place image immediately: ${e.message}")
            Log.e("TapDetectionDebug", "Stack trace: ${e.stackTraceToString()}")
        } finally {
            // Always reset the flag when we're done, regardless of success or failure
            synchronized(this) {
                isPlacingImage = false
                Log.d("TapDetectionDebug", "Reset isPlacingImage flag after immediate placement attempt")
            }
        }
    }


    // Using DistanceUtils.calculateDistance instead of local implementation

    /**
     * Checks if there are any vertical surfaces detected in the current frame.
     * This is used to determine if we can place an image on a vertical surface.
     *
     * @param frame The current AR frame
     * @return A pair containing (number of vertical planes, total tracking planes)
     */
    private fun checkForVerticalSurfaces(frame: Frame): Pair<Int, Int> {
        val allPlanes = frame.getUpdatedTrackables(Plane::class.java)
        val trackingPlanes = allPlanes.count { it.trackingState == TrackingState.TRACKING }
        val verticalPlanes = allPlanes.count {
            it.trackingState == TrackingState.TRACKING && it.type == Plane.Type.VERTICAL
        }

        Log.d("TapDetectionDebug", "Vertical surface check - Total tracking planes: $trackingPlanes")
        Log.d("TapDetectionDebug", "Vertical surface check - Vertical planes: $verticalPlanes")

        return Pair(verticalPlanes, trackingPlanes)
    }

    /**
     * Updates the line thickness for the currently displayed grid.
     * @param thickness The desired line thickness (e.g., 1.0f, 2.0f).
     */
    fun updateGridLineThickness(thickness: Float) {
        // This should delegate to the current ARGrid object
        currentGrid?.setLineThickness(thickness) // Assumes ARGrid has this method
        Log.d("ARSessionManager", "Update Grid Line Thickness: $thickness")
    }

    /**
     * Resets the AR session by clearing all content and anchors
     */
    fun resetARSession() {
        Log.d("ARSessionManager", "Resetting AR session")

        // Clear grid
        currentGrid?.release()
        currentGrid = null
        gridAnchor?.detach()
        gridAnchor = null
        isGridFixed = false

        // Clear image
        currentImage?.release()
        currentImage = null
        imageAnchor?.detach()
        imageAnchor = null
        isImageFixed = false

        // Reset state variables
        isDragging = false
        isScaling = false
        isRotating = false

        // Clear any pending tap events
        synchronized(pendingTapEvents) {
            pendingTapEvents.forEach { it.recycle() }
            pendingTapEvents.clear()
        }

        // Update renderer
        arRenderer.removeGrid()
        arRenderer.removeImage()

        Log.d("ARSessionManager", "AR session reset complete")
    }

//    fun testARSession() {
//        // 1. Verify plane detection
//        assertTrue(arSessionManager.detectPlanes())
//
//        // 2. Test grid placement
//        arSessionManager.placeGrid(SettingsManager.GRID_FORMAT_VERTICAL)
//        assertTrue(arRenderer.hasActiveGrid())
//
//        // 3. Test image projection
//        val testBitmap = Bitmap.createBitmap(100, 100, Bitmap.Config.ARGB_8888)
//        arSessionManager.loadImage(testBitmap)
//        assertTrue(arRenderer.hasActiveImage())
//    }
}



