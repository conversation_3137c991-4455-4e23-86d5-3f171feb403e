<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.deace.deacear" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/activity_main_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="825" endOffset="16"/></Target><Target id="@+id/expanded_image_panel_scroll" tag="binding_1" view="ScrollView"><Expressions/><location startLine="471" startOffset="4" endLine="483" endOffset="16"/></Target><Target tag="binding_1" include="image_panel"><Expressions/><location startLine="481" startOffset="8" endLine="481" endOffset="47"/></Target><Target id="@+id/ar_surface_view" view="android.opengl.GLSurfaceView"><Expressions/><location startLine="7" startOffset="4" endLine="10" endOffset="46"/></Target><Target id="@+id/loading_layout" view="LinearLayout"><Expressions/><location startLine="13" startOffset="4" endLine="34" endOffset="18"/></Target><Target id="@+id/loading_text" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="26" startOffset="8" endLine="33" endOffset="37"/></Target><Target id="@+id/main_menu_layout" view="LinearLayout"><Expressions/><location startLine="37" startOffset="4" endLine="113" endOffset="18"/></Target><Target id="@+id/deace_logo" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="50" startOffset="8" endLine="58" endOffset="56"/></Target><Target id="@+id/menu_icons_container" view="LinearLayout"><Expressions/><location startLine="61" startOffset="8" endLine="111" endOffset="22"/></Target><Target id="@+id/grid_tab_button" view="androidx.appcompat.widget.AppCompatImageButton"><Expressions/><location startLine="68" startOffset="12" endLine="77" endOffset="49"/></Target><Target id="@+id/image_tab_button" view="androidx.appcompat.widget.AppCompatImageButton"><Expressions/><location startLine="79" startOffset="12" endLine="88" endOffset="50"/></Target><Target id="@+id/export_tab_button" view="androidx.appcompat.widget.AppCompatImageButton"><Expressions/><location startLine="90" startOffset="12" endLine="99" endOffset="51"/></Target><Target id="@+id/config_tab_button" view="androidx.appcompat.widget.AppCompatImageButton"><Expressions/><location startLine="101" startOffset="12" endLine="110" endOffset="53"/></Target><Target id="@+id/grid_panel_layout" view="LinearLayout"><Expressions/><location startLine="116" startOffset="4" endLine="204" endOffset="18"/></Target><Target id="@+id/vertical_format_button" view="ImageButton"><Expressions/><location startLine="138" startOffset="8" endLine="145" endOffset="54"/></Target><Target id="@+id/square_format_button" view="ImageButton"><Expressions/><location startLine="147" startOffset="8" endLine="154" endOffset="52"/></Target><Target id="@+id/horizontal_format_button" view="ImageButton"><Expressions/><location startLine="156" startOffset="8" endLine="163" endOffset="56"/></Target><Target id="@+id/fix_grid_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="166" startOffset="8" endLine="176" endOffset="45"/></Target><Target id="@+id/grid_opacity_seekbar" view="androidx.appcompat.widget.AppCompatSeekBar"><Expressions/><location startLine="187" startOffset="8" endLine="194" endOffset="41"/></Target><Target id="@+id/grid_opacity_value" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="196" startOffset="8" endLine="203" endOffset="46"/></Target><Target id="@+id/image_panel_layout" view="LinearLayout"><Expressions/><location startLine="207" startOffset="4" endLine="468" endOffset="18"/></Target><Target id="@+id/load_image_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="221" startOffset="8" endLine="230" endOffset="47"/></Target><Target id="@+id/image_thumbnail" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="233" startOffset="8" endLine="241" endOffset="39"/></Target><Target id="@+id/image_selected_status" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="244" startOffset="8" endLine="251" endOffset="46"/></Target><Target id="@+id/fix_image_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="254" startOffset="8" endLine="264" endOffset="46"/></Target><Target id="@+id/test_image_placement_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="267" startOffset="8" endLine="278" endOffset="40"/></Target><Target id="@+id/advanced_rotation_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="281" startOffset="8" endLine="292" endOffset="38"/></Target><Target id="@+id/release_image_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="295" startOffset="8" endLine="306" endOffset="50"/></Target><Target id="@+id/image_opacity_seekbar" view="androidx.appcompat.widget.AppCompatSeekBar"><Expressions/><location startLine="308" startOffset="8" endLine="314" endOffset="35"/></Target><Target id="@+id/image_opacity_value" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="316" startOffset="8" endLine="321" endOffset="54"/></Target><Target id="@+id/preserve_aspect_switch" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="323" startOffset="8" endLine="328" endOffset="36"/></Target><Target id="@+id/image_size_text" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="330" startOffset="8" endLine="335" endOffset="52"/></Target><Target id="@+id/rotate_x_minus_90_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="339" startOffset="8" endLine="344" endOffset="35"/></Target><Target id="@+id/rotate_x_minus_15_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="346" startOffset="8" endLine="351" endOffset="35"/></Target><Target id="@+id/rotate_x_plus_15_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="353" startOffset="8" endLine="358" endOffset="35"/></Target><Target id="@+id/rotate_x_plus_90_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="360" startOffset="8" endLine="365" endOffset="35"/></Target><Target id="@+id/rotate_y_minus_90_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="368" startOffset="8" endLine="373" endOffset="35"/></Target><Target id="@+id/rotate_y_minus_15_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="375" startOffset="8" endLine="380" endOffset="35"/></Target><Target id="@+id/rotate_y_plus_15_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="382" startOffset="8" endLine="387" endOffset="35"/></Target><Target id="@+id/rotate_y_plus_90_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="389" startOffset="8" endLine="394" endOffset="35"/></Target><Target id="@+id/rotate_z_minus_90_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="397" startOffset="8" endLine="402" endOffset="35"/></Target><Target id="@+id/rotate_z_minus_15_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="404" startOffset="8" endLine="409" endOffset="35"/></Target><Target id="@+id/rotate_z_plus_15_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="411" startOffset="8" endLine="416" endOffset="35"/></Target><Target id="@+id/rotate_z_plus_90_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="418" startOffset="8" endLine="423" endOffset="35"/></Target><Target id="@+id/rotate_left_90_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="426" startOffset="8" endLine="431" endOffset="34"/></Target><Target id="@+id/rotate_right_90_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="433" startOffset="8" endLine="438" endOffset="34"/></Target><Target id="@+id/rotate_180_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="440" startOffset="8" endLine="445" endOffset="33"/></Target><Target id="@+id/rotate_left_15_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="447" startOffset="8" endLine="452" endOffset="34"/></Target><Target id="@+id/rotate_right_15_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="454" startOffset="8" endLine="459" endOffset="34"/></Target><Target id="@+id/flip_horizontal_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="461" startOffset="8" endLine="466" endOffset="35"/></Target><Target id="@+id/export_panel_layout" view="LinearLayout"><Expressions/><location startLine="486" startOffset="4" endLine="510" endOffset="18"/></Target><Target id="@+id/export_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="500" startOffset="8" endLine="509" endOffset="51"/></Target><Target id="@+id/deace_panel_layout" view="LinearLayout"><Expressions/><location startLine="513" startOffset="4" endLine="537" endOffset="18"/></Target><Target id="@+id/confirm_reinit_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="527" startOffset="8" endLine="536" endOffset="55"/></Target><Target id="@+id/config_panel_scroll" view="ScrollView"><Expressions/><location startLine="540" startOffset="4" endLine="721" endOffset="16"/></Target><Target id="@+id/config_panel_layout" view="LinearLayout"><Expressions/><location startLine="550" startOffset="8" endLine="720" endOffset="22"/></Target><Target id="@+id/show_planes_switch" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="567" startOffset="12" endLine="574" endOffset="41"/></Target><Target id="@+id/reset_session_button" view="Button"><Expressions/><location startLine="576" startOffset="12" endLine="585" endOffset="41"/></Target><Target id="@+id/default_grid_format_spinner" view="Spinner"><Expressions/><location startLine="605" startOffset="12" endLine="609" endOffset="48"/></Target><Target id="@+id/default_grid_opacity_seekbar" view="androidx.appcompat.widget.AppCompatSeekBar"><Expressions/><location startLine="619" startOffset="12" endLine="626" endOffset="45"/></Target><Target id="@+id/image_quality_radio_group" view="RadioGroup"><Expressions/><location startLine="638" startOffset="12" endLine="661" endOffset="24"/></Target><Target id="@+id/normal_quality_radio" view="RadioButton"><Expressions/><location startLine="645" startOffset="16" endLine="651" endOffset="45"/></Target><Target id="@+id/max_quality_radio" view="RadioButton"><Expressions/><location startLine="653" startOffset="16" endLine="660" endOffset="45"/></Target><Target id="@+id/file_format_radio_group" view="RadioGroup"><Expressions/><location startLine="663" startOffset="12" endLine="686" endOffset="24"/></Target><Target id="@+id/jpg_format_radio" view="RadioButton"><Expressions/><location startLine="670" startOffset="16" endLine="676" endOffset="45"/></Target><Target id="@+id/png_format_radio" view="RadioButton"><Expressions/><location startLine="678" startOffset="16" endLine="685" endOffset="45"/></Target><Target id="@+id/reset_settings_button" view="Button"><Expressions/><location startLine="698" startOffset="12" endLine="707" endOffset="41"/></Target><Target id="@+id/help_about_button" view="Button"><Expressions/><location startLine="709" startOffset="12" endLine="719" endOffset="41"/></Target><Target id="@+id/reticle" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="724" startOffset="4" endLine="730" endOffset="40"/></Target><Target id="@+id/tracking_indicator_container" view="LinearLayout"><Expressions/><location startLine="733" startOffset="4" endLine="797" endOffset="18"/></Target><Target id="@+id/tracking_quality_indicator" view="View"><Expressions/><location startLine="753" startOffset="12" endLine="758" endOffset="50"/></Target><Target id="@+id/tracking_quality_text" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="760" startOffset="12" endLine="768" endOffset="42"/></Target><Target id="@+id/tracking_init_text" view="androidx.appcompat.widget.AppCompatTextView"><Expressions/><location startLine="774" startOffset="8" endLine="784" endOffset="42"/></Target><Target id="@+id/tracking_progress_bar" view="ProgressBar"><Expressions/><location startLine="787" startOffset="8" endLine="796" endOffset="42"/></Target><Target id="@+id/help_button" view="androidx.appcompat.widget.AppCompatImageButton"><Expressions/><location startLine="800" startOffset="4" endLine="809" endOffset="41"/></Target><Target id="@+id/quick_lock_image_button" view="androidx.appcompat.widget.AppCompatButton"><Expressions/><location startLine="812" startOffset="4" endLine="824" endOffset="35"/></Target></Targets></Layout>