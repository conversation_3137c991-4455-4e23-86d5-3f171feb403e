# PowerShell script to fix ARSessionManager.kt

# Read the file content
$content = Get-Content -Path "app/src/main/java/com/deace/deacear/ar/ARSessionManager.kt" -Raw

# Replace all occurrences of calculateDistance with DistanceUtils.calculateDistance
$content = $content -replace "calculateDistance\(", "DistanceUtils.calculateDistance("

# Replace all occurrences of "if (distance < minDistance) {" with "if (distance.compareTo(minDistance) < 0) {"
$content = $content -replace "if \(distance < minDistance\) \{", "if (distance.compareTo(minDistance) < 0) {"

# Replace all occurrences of "if (minDistance < bestDistance) {" with "if (minDistance.compareTo(bestDistance) < 0) {"
$content = $content -replace "if \(closestPlane != null && minDistance < bestDistance\) \{", "if (closestPlane != null && minDistance.compareTo(bestDistance) < 0) {"

# Replace all occurrences of "if (distance < bestDistance" with "if (distance.compareTo(bestDistance) < 0"
$content = $content -replace "if \(distance < bestDistance", "if (distance.compareTo(bestDistance) < 0"

# Write the modified content back to the file
$content | Set-Content -Path "app/src/main/java/com/deace/deacear/ar/ARSessionManager.kt"

Write-Host "Fixed ARSessionManager.kt"
