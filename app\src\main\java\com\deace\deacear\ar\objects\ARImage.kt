package com.deace.deacear.ar.objects

import android.content.Context
import android.graphics.Bitmap
import android.opengl.GLES30
import android.opengl.GLUtils
import android.opengl.Matrix
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.FloatBuffer
import java.nio.ShortBuffer

class ARImage(context: Context, private val bitmap: Bitmap) {
    private var vertexBuffer: FloatBuffer
    private var textureBuffer: FloatBuffer
    private var indexBuffer: ShortBuffer

    var opacity: Float = 1.0f
        set(value) {
            field = value.coerceIn(0f, 1f)
        }

    var preserveAspectRatio: Boolean = true
        set(value) {
            field = value
            // Update scaling if needed
            val aspectRatio = bitmap.width.toFloat() / bitmap.height.toFloat()
            if (value) {
                scaleToAspectRatio(aspectRatio)
            }
        }

    private val vertexShaderCode =
        "uniform mat4 uMVPMatrix;" +
                "attribute vec4 vPosition;" +
                "attribute vec2 vTexCoord;" +
                "varying vec2 fTexCoord;" +
                "void main() {" +
                "  fTexCoord = vTexCoord;" +
                "  gl_Position = uMVPMatrix * vPosition;" +
                "}"

    private val fragmentShaderCode =
        "precision mediump float;" +
                "varying vec2 fTexCoord;" +
                "uniform sampler2D uTexture;" +
                "uniform float uOpacity;" +
                "void main() {" +
                "  vec4 color = texture2D(uTexture, fTexCoord);" +
                "  color.a *= uOpacity;" +
                "  gl_FragColor = color;" +
                "}"

    private var mProgram: Int = 0
    private var mPositionHandle: Int = 0
    private var mTexCoordHandle: Int = 0
    private var mMVPMatrixHandle: Int = 0
    private var mTextureHandle: Int = 0
    private var mOpacityHandle: Int = 0

    private val modelMatrix = FloatArray(16)
    private var textureId = -1

    // Coordinates for a square plane
    private val vertices = floatArrayOf(
        // Positions
        -0.5f, -0.5f, 0.0f,  // Bottom left
        0.5f, -0.5f, 0.0f,   // Bottom right
        0.5f,  0.5f, 0.0f,   // Top right
        -0.5f,  0.5f, 0.0f   // Top left
    )

    // Separate texture coordinates for better clarity
    private val texCoords = floatArrayOf(
        // Texture coords
        0.0f, 0.0f,  // Bottom left
        1.0f, 0.0f,  // Bottom right
        1.0f, 1.0f,  // Top right
        0.0f, 1.0f   // Top left
    )

    // Order to draw vertices
    private val indices = shortArrayOf(
        0, 1, 2,  // First triangle
        0, 2, 3   // Second triangle
    )

    init {
        Matrix.setIdentityM(modelMatrix, 0)

        // Adjust aspect ratio based on bitmap dimensions
        val aspectRatio = bitmap.width.toFloat() / bitmap.height.toFloat()
        scaleToAspectRatio(aspectRatio)

        // Initialize vertex byte buffer for shape coordinates
        val bb = ByteBuffer.allocateDirect(vertices.size * 4)
        bb.order(ByteOrder.nativeOrder())
        vertexBuffer = bb.asFloatBuffer()
        vertexBuffer.put(vertices)
        vertexBuffer.position(0)

        // Initialize byte buffer for texture coordinates
        val tb = ByteBuffer.allocateDirect(texCoords.size * 4)
        tb.order(ByteOrder.nativeOrder())
        textureBuffer = tb.asFloatBuffer()
        textureBuffer.put(texCoords)
        textureBuffer.position(0)

        // Initialize byte buffer for indices
        val ib = ByteBuffer.allocateDirect(indices.size * 2)
        ib.order(ByteOrder.nativeOrder())
        indexBuffer = ib.asShortBuffer()
        indexBuffer.put(indices)
        indexBuffer.position(0)

        // Prepare shaders and OpenGL program
        val vertexShader = loadShader(GLES30.GL_VERTEX_SHADER, vertexShaderCode)
        val fragmentShader = loadShader(GLES30.GL_FRAGMENT_SHADER, fragmentShaderCode)

        // Check if shaders were compiled successfully
        if (vertexShader == 0 || fragmentShader == 0) {
            android.util.Log.e("ARImageDebug", "Failed to compile one or both shaders")
            // Can't return from init block, but we can set mProgram to 0 to indicate failure
            mProgram = 0
        } else {
            // Create the program and attach shaders
            mProgram = GLES30.glCreateProgram()
            if (mProgram == 0) {
                android.util.Log.e("ARImageDebug", "Failed to create GL program")
                // Already set to 0, no need to do anything
            } else {
                GLES30.glAttachShader(mProgram, vertexShader)
                GLES30.glAttachShader(mProgram, fragmentShader)
                GLES30.glLinkProgram(mProgram)

                // Check linking status
                val linkStatus = IntArray(1)
                GLES30.glGetProgramiv(mProgram, GLES30.GL_LINK_STATUS, linkStatus, 0)
                if (linkStatus[0] != GLES30.GL_TRUE) {
                    android.util.Log.e("ARImageDebug", "Could not link program: ${GLES30.glGetProgramInfoLog(mProgram)}")
                    GLES30.glDeleteProgram(mProgram)
                    mProgram = 0
                }
            }
        }

        // Get handles to shader variables
        mPositionHandle = GLES30.glGetAttribLocation(mProgram, "vPosition")
        mTexCoordHandle = GLES30.glGetAttribLocation(mProgram, "vTexCoord")
        mMVPMatrixHandle = GLES30.glGetUniformLocation(mProgram, "uMVPMatrix")
        mTextureHandle = GLES30.glGetUniformLocation(mProgram, "uTexture")
        mOpacityHandle = GLES30.glGetUniformLocation(mProgram, "uOpacity")

        // Log shader handles for debugging
        android.util.Log.d("ARImageDebug", "Shader handles initialized - Position: $mPositionHandle, TexCoord: $mTexCoordHandle, MVP: $mMVPMatrixHandle, Texture: $mTextureHandle, Opacity: $mOpacityHandle")

        // Load the texture
        textureId = loadTexture(bitmap)
    }

    private fun scaleToAspectRatio(aspectRatio: Float) {
        // Reset to identity matrix first
        Matrix.setIdentityM(modelMatrix, 0)
        // Scale the model matrix to match the image aspect ratio
        Matrix.scaleM(modelMatrix, 0, aspectRatio, 1f, 1f)
    }

    fun draw(projectionMatrix: FloatArray, viewMatrix: FloatArray) {
        try {
            // Log drawing attempt with detailed information
            android.util.Log.d("ARImageDebug", "==== DRAWING IMAGE ====")
            android.util.Log.d("ARImageDebug", "Image dimensions: ${bitmap.width}x${bitmap.height}")
            android.util.Log.d("ARImageDebug", "Texture ID: $textureId")
            android.util.Log.d("ARImageDebug", "Opacity: $opacity")
            android.util.Log.d("ARImageDebug", "Preserve aspect ratio: $preserveAspectRatio")

            // Log model matrix for position debugging
            android.util.Log.d("ARImageDebug", "Model matrix: [" +
                    "${modelMatrix[0]}, ${modelMatrix[1]}, ${modelMatrix[2]}, ${modelMatrix[3]}, " +
                    "${modelMatrix[4]}, ${modelMatrix[5]}, ${modelMatrix[6]}, ${modelMatrix[7]}, " +
                    "${modelMatrix[8]}, ${modelMatrix[9]}, ${modelMatrix[10]}, ${modelMatrix[11]}, " +
                    "${modelMatrix[12]}, ${modelMatrix[13]}, ${modelMatrix[14]}, ${modelMatrix[15]}]")

            // Clear any existing OpenGL errors before starting
            while (GLES30.glGetError() != GLES30.GL_NO_ERROR) {
                // Clear error queue
            }

            // Check for invalid texture ID
            if (textureId <= 0) {
                android.util.Log.e("ARImageDebug", "ERROR: Invalid texture ID ($textureId). Attempting to regenerate texture.")
                // Try to regenerate the texture
                textureId = loadTexture(bitmap)
                android.util.Log.d("ARImageDebug", "Regenerated texture ID: $textureId")

                if (textureId <= 0) {
                    android.util.Log.e("ARImageDebug", "ERROR: Failed to regenerate texture. Image cannot be drawn.")
                    return
                }
            }

            // Combine the model, view, and projection matrices
            val mvpMatrix = FloatArray(16)
            Matrix.multiplyMM(mvpMatrix, 0, viewMatrix, 0, modelMatrix, 0)
            Matrix.multiplyMM(mvpMatrix, 0, projectionMatrix, 0, mvpMatrix, 0)

            // Check if program is valid
            if (mProgram == 0) {
                android.util.Log.e("ARImageDebug", "Cannot draw - shader program is invalid")
                recreateShaderProgram()
                return
            }

            // Add program to OpenGL environment
            GLES30.glUseProgram(mProgram)
            val programError = GLES30.glGetError()
            if (programError != GLES30.GL_NO_ERROR) {
                android.util.Log.e("ARImageDebug", "Error using program: $programError")
                // Try to recover by recreating the program
                recreateShaderProgram()
                return  // Skip this frame, we'll try again next time
            }

            // Enable blending for transparency
            GLES30.glEnable(GLES30.GL_BLEND)
            GLES30.glBlendFunc(GLES30.GL_SRC_ALPHA, GLES30.GL_ONE_MINUS_SRC_ALPHA)
            checkGlError("glBlendFunc")

            // Log shader handles for debugging
            android.util.Log.d("ARImageDebug", "Shader handles - Position: $mPositionHandle, TexCoord: $mTexCoordHandle, MVP: $mMVPMatrixHandle, Texture: $mTextureHandle, Opacity: $mOpacityHandle")

            // Verify all shader handles are valid
            if (mPositionHandle < 0 || mTexCoordHandle < 0 || mMVPMatrixHandle < 0 || mTextureHandle < 0 || mOpacityHandle < 0) {
                android.util.Log.e("ARImageDebug", "One or more shader handles are invalid")
                recreateShaderProgram()
                return  // Skip this frame, we'll try again next time
            }

            // Enable vertex arrays
            GLES30.glEnableVertexAttribArray(mPositionHandle)
            checkGlError("glEnableVertexAttribArray - Position")

            GLES30.glEnableVertexAttribArray(mTexCoordHandle)
            checkGlError("glEnableVertexAttribArray - TexCoord")

            // Prepare the coordinate data
            GLES30.glVertexAttribPointer(
                mPositionHandle, 3,
                GLES30.GL_FLOAT, false,
                3 * 4, vertexBuffer
            )
            checkGlError("glVertexAttribPointer - Position")

            // Prepare the texture coordinate data
            GLES30.glVertexAttribPointer(
                mTexCoordHandle, 2,
                GLES30.GL_FLOAT, false,
                2 * 4, textureBuffer
            )
            checkGlError("glVertexAttribPointer - TexCoord")

            // Apply the combined projection and view transformation
            GLES30.glUniformMatrix4fv(mMVPMatrixHandle, 1, false, mvpMatrix, 0)
            checkGlError("glUniformMatrix4fv")

            // Set the texture
            GLES30.glActiveTexture(GLES30.GL_TEXTURE0)
            checkGlError("glActiveTexture")

            GLES30.glBindTexture(GLES30.GL_TEXTURE_2D, textureId)

            // Check for OpenGL errors after binding texture
            val textureError = GLES30.glGetError()
            if (textureError != GLES30.GL_NO_ERROR) {
                android.util.Log.e("ARImageDebug", "OpenGL error after binding texture: $textureError")

                // If there was an error binding the texture, try to regenerate it
                if (textureError == GLES30.GL_INVALID_VALUE || textureError == GLES30.GL_INVALID_OPERATION) {
                    android.util.Log.d("ARImageDebug", "Attempting to regenerate texture after binding error")

                    // Release the old texture first
                    if (textureId > 0) {
                        GLES30.glDeleteTextures(1, intArrayOf(textureId), 0)
                    }

                    // Generate a new texture
                    textureId = loadTexture(bitmap)
                    android.util.Log.d("ARImageDebug", "Regenerated texture ID: $textureId")

                    // Try binding again
                    GLES30.glActiveTexture(GLES30.GL_TEXTURE0)
                    GLES30.glBindTexture(GLES30.GL_TEXTURE_2D, textureId)
                }
            }

            GLES30.glUniform1i(mTextureHandle, 0)
            checkGlError("glUniform1i - Texture")

            // Set the opacity
            GLES30.glUniform1f(mOpacityHandle, opacity)
            checkGlError("glUniform1f - Opacity")

            try {
                // Draw the image
                GLES30.glDrawElements(
                    GLES30.GL_TRIANGLES, indices.size,
                    GLES30.GL_UNSIGNED_SHORT, indexBuffer
                )

                // Check for OpenGL errors after drawing
                val postDrawError = GLES30.glGetError()
                if (postDrawError != GLES30.GL_NO_ERROR) {
                    android.util.Log.e("ARImageDebug", "OpenGL error after drawing: $postDrawError")
                } else {
                    android.util.Log.d("ARImageDebug", "Image drawn successfully")
                }
            } catch (e: Exception) {
                android.util.Log.e("ARImageDebug", "Error during glDrawElements: ${e.message}")
            } finally {
                // Always clean up, even if drawing failed
                try {
                    // Disable vertex arrays
                    GLES30.glDisableVertexAttribArray(mPositionHandle)
                    GLES30.glDisableVertexAttribArray(mTexCoordHandle)

                    // Disable blending
                    GLES30.glDisable(GLES30.GL_BLEND)
                } catch (e: Exception) {
                    android.util.Log.e("ARImageDebug", "Error during cleanup: ${e.message}")
                }
            }

            android.util.Log.d("ARImageDebug", "Image.draw() method called successfully")

        } catch (e: Exception) {
            android.util.Log.e("ARImageDebug", "Error drawing image: ${e.message}")
            android.util.Log.e("ARImageDebug", "Stack trace: ${e.stackTraceToString()}")
        }
    }

    /**
     * Updates the model matrix based on the provided pose
     * Now with improved handling for different plane types
     *
     * @param pose The pose to use for positioning the image
     * @param planeType Optional plane type to help with orientation (null if unknown)
     */
    fun updateModelMatrix(pose: com.google.ar.core.Pose, planeType: com.google.ar.core.Plane.Type? = null) {
        try {
            android.util.Log.d("ARImageDebug", "==== UPDATING IMAGE POSITION ====")
            android.util.Log.d("ARImageDebug", "Pose position: (${pose.tx()}, ${pose.ty()}, ${pose.tz()})")
            android.util.Log.d("ARImageDebug", "Pose rotation (quaternion): (${pose.qx()}, ${pose.qy()}, ${pose.qz()}, ${pose.qw()})")
            android.util.Log.d("ARImageDebug", "Plane type: $planeType")

            // Store the plane type and pose for use in translation
            this.currentPlaneType = planeType
            this.lastPose = pose
            android.util.Log.d("ARImageDebug", "Stored plane type and pose for coordinate transformations: $currentPlaneType")

            // Log model matrix before update
            android.util.Log.d("ARImageDebug", "Model matrix BEFORE update: [" +
                    "${modelMatrix[0]}, ${modelMatrix[1]}, ${modelMatrix[2]}, ${modelMatrix[3]}, " +
                    "${modelMatrix[4]}, ${modelMatrix[5]}, ${modelMatrix[6]}, ${modelMatrix[7]}, " +
                    "${modelMatrix[8]}, ${modelMatrix[9]}, ${modelMatrix[10]}, ${modelMatrix[11]}, " +
                    "${modelMatrix[12]}, ${modelMatrix[13]}, ${modelMatrix[14]}, ${modelMatrix[15]}]")

            // Create a slightly offset pose to place the image in front of the plane
            // This helps prevent Z-fighting and ensures the image is visible
            val offsetPose = createOffsetPose(pose)
            android.util.Log.d("ARImageDebug", "Created offset pose: (${offsetPose.tx()}, ${offsetPose.ty()}, ${offsetPose.tz()})")

            // Update the model matrix from the offset pose
            offsetPose.toMatrix(modelMatrix, 0)

            // Log model matrix after pose update but before scaling
            android.util.Log.d("ARImageDebug", "Model matrix AFTER pose update (before scaling): [" +
                    "${modelMatrix[0]}, ${modelMatrix[1]}, ${modelMatrix[2]}, ${modelMatrix[3]}, " +
                    "${modelMatrix[4]}, ${modelMatrix[5]}, ${modelMatrix[6]}, ${modelMatrix[7]}, " +
                    "${modelMatrix[8]}, ${modelMatrix[9]}, ${modelMatrix[10]}, ${modelMatrix[11]}, " +
                    "${modelMatrix[12]}, ${modelMatrix[13]}, ${modelMatrix[14]}, ${modelMatrix[15]}]")

            // Reapply the aspect ratio scaling after updating the model matrix
            val aspectRatio = bitmap.width.toFloat() / bitmap.height.toFloat()
            android.util.Log.d("ARImageDebug", "Applying aspect ratio scaling: $aspectRatio")

            // Apply scaling to maintain aspect ratio
            Matrix.scaleM(modelMatrix, 0, aspectRatio, 1f, 1f)

            // CRITICAL FIX: Adjust orientation based on plane type
            if (planeType == com.google.ar.core.Plane.Type.HORIZONTAL_UPWARD_FACING ||
                planeType == com.google.ar.core.Plane.Type.HORIZONTAL_DOWNWARD_FACING) {

                android.util.Log.d("ARImageDebug", "Adjusting orientation for horizontal plane")

                // For horizontal planes, we need to rotate the image to be parallel to the plane
                // This is a 90-degree rotation around the X-axis to make the image face up
                val rotationMatrix = FloatArray(16)
                Matrix.setIdentityM(rotationMatrix, 0)
                Matrix.rotateM(rotationMatrix, 0, -90f, 1f, 0f, 0f)

                // Apply the rotation to the model matrix
                val tempMatrix = FloatArray(16)
                Matrix.multiplyMM(tempMatrix, 0, modelMatrix, 0, rotationMatrix, 0)
                System.arraycopy(tempMatrix, 0, modelMatrix, 0, 16)

                android.util.Log.d("ARImageDebug", "Applied horizontal plane orientation adjustment")
            } else if (planeType == com.google.ar.core.Plane.Type.VERTICAL) {
                android.util.Log.d("ARImageDebug", "Adjusting orientation for vertical plane")

                // For vertical planes, we need to ensure the image is properly oriented
                // First, extract the rotation from the model matrix
                val rotationMatrix = FloatArray(16)
                Matrix.setIdentityM(rotationMatrix, 0)

                // Extract rotation from model matrix (first 3x3 part)
                for (i in 0..2) {
                    for (j in 0..2) {
                        rotationMatrix[i * 4 + j] = modelMatrix[i * 4 + j]
                    }
                }

                // Create a coordinate system aligned with the wall
                val adjustmentMatrix = FloatArray(16)
                Matrix.setIdentityM(adjustmentMatrix, 0)

                // Extract the plane's normal vector (perpendicular to the wall)
                val normalX = pose.zAxis[0]
                val normalY = pose.zAxis[1]
                val normalZ = pose.zAxis[2]
                android.util.Log.d("ARImageDebug", "Wall normal vector: ($normalX, $normalY, $normalZ)")

                // Create a right vector (X-axis of our wall-aligned coordinate system)
                // We can use the up vector (Y-axis in world space) and cross product to find it
                val worldUp = floatArrayOf(0f, 1f, 0f)
                val rightVector = floatArrayOf(0f, 0f, 0f)

                // Cross product of normal and world up gives us the right vector
                rightVector[0] = normalY * worldUp[2] - normalZ * worldUp[1]
                rightVector[1] = normalZ * worldUp[0] - normalX * worldUp[2]
                rightVector[2] = normalX * worldUp[1] - normalY * worldUp[0]

                // Normalize the right vector
                val rightMagnitude = Math.sqrt((rightVector[0] * rightVector[0] +
                                               rightVector[1] * rightVector[1] +
                                               rightVector[2] * rightVector[2]).toDouble()).toFloat()
                if (rightMagnitude > 0.0001f) {
                    rightVector[0] /= rightMagnitude
                    rightVector[1] /= rightMagnitude
                    rightVector[2] /= rightMagnitude
                }

                // Now get the up vector (Y-axis of our wall-aligned coordinate system)
                // Cross product of right vector and normal gives us the up vector
                val upVector = floatArrayOf(0f, 0f, 0f)
                upVector[0] = rightVector[1] * normalZ - rightVector[2] * normalY
                upVector[1] = rightVector[2] * normalX - rightVector[0] * normalZ
                upVector[2] = rightVector[0] * normalY - rightVector[1] * normalX

                // Build a rotation matrix from these three vectors
                // This matrix will transform from world space to wall-aligned space
                adjustmentMatrix[0] = rightVector[0]
                adjustmentMatrix[1] = upVector[0]
                adjustmentMatrix[2] = -normalX  // Negative normal for Z-axis (facing away from wall)
                adjustmentMatrix[3] = 0f

                adjustmentMatrix[4] = rightVector[1]
                adjustmentMatrix[5] = upVector[1]
                adjustmentMatrix[6] = -normalY
                adjustmentMatrix[7] = 0f

                adjustmentMatrix[8] = rightVector[2]
                adjustmentMatrix[9] = upVector[2]
                adjustmentMatrix[10] = -normalZ
                adjustmentMatrix[11] = 0f

                // Keep the translation component
                adjustmentMatrix[12] = modelMatrix[12]
                adjustmentMatrix[13] = modelMatrix[13]
                adjustmentMatrix[14] = modelMatrix[14]
                adjustmentMatrix[15] = 1f

                android.util.Log.d("ARImageDebug", "Applied wall-aligned coordinate system transformation")

                // Apply the adjustments
                val tempMatrix = FloatArray(16)
                Matrix.multiplyMM(tempMatrix, 0, modelMatrix, 0, adjustmentMatrix, 0)
                System.arraycopy(tempMatrix, 0, modelMatrix, 0, 16)

                android.util.Log.d("ARImageDebug", "Applied vertical plane orientation adjustment")
            } else {
                android.util.Log.d("ARImageDebug", "No specific orientation adjustment needed (unknown plane type)")
            }

            // Log final model matrix after all updates
            android.util.Log.d("ARImageDebug", "Model matrix FINAL (after scaling and orientation): [" +
                    "${modelMatrix[0]}, ${modelMatrix[1]}, ${modelMatrix[2]}, ${modelMatrix[3]}, " +
                    "${modelMatrix[4]}, ${modelMatrix[5]}, ${modelMatrix[6]}, ${modelMatrix[7]}, " +
                    "${modelMatrix[8]}, ${modelMatrix[9]}, ${modelMatrix[10]}, ${modelMatrix[11]}, " +
                    "${modelMatrix[12]}, ${modelMatrix[13]}, ${modelMatrix[14]}, ${modelMatrix[15]}]")

            android.util.Log.d("ARImageDebug", "Image position updated successfully")
        } catch (e: Exception) {
            android.util.Log.e("ARImageDebug", "Error updating model matrix: ${e.message}")
            android.util.Log.e("ARImageDebug", "Stack trace: ${e.stackTraceToString()}")
        }
    }

    /**
     * Creates a slightly offset pose to place the image in front of the plane.
     * This helps prevent Z-fighting and ensures the image is visible.
     */
    private fun createOffsetPose(originalPose: com.google.ar.core.Pose): com.google.ar.core.Pose {
        try {
            // Extract the original position and rotation
            val tx = originalPose.tx()
            val ty = originalPose.ty()
            val tz = originalPose.tz()
            val qx = originalPose.qx()
            val qy = originalPose.qy()
            val qz = originalPose.qz()
            val qw = originalPose.qw()

            // Calculate the forward vector based on the pose's orientation
            // This is the direction the plane is facing (its normal)
            val forwardX = 2.0f * (qx * qz + qw * qy)
            val forwardY = 2.0f * (qy * qz - qw * qx)
            val forwardZ = 1.0f - 2.0f * (qx * qx + qy * qy)

            // Normalize the forward vector
            val length = kotlin.math.sqrt(forwardX * forwardX + forwardY * forwardY + forwardZ * forwardZ)
            val normalizedForwardX = forwardX / length
            val normalizedForwardY = forwardY / length
            val normalizedForwardZ = forwardZ / length

            // Apply a larger offset in the direction of the normal (2cm)
            // This moves the image further in front of the plane to ensure visibility
            val OFFSET_DISTANCE = 0.02f // 2cm offset
            val offsetX = tx + normalizedForwardX * OFFSET_DISTANCE
            val offsetY = ty + normalizedForwardY * OFFSET_DISTANCE
            val offsetZ = tz + normalizedForwardZ * OFFSET_DISTANCE

            // Create a new pose with the offset position but same orientation
            return com.google.ar.core.Pose(floatArrayOf(offsetX, offsetY, offsetZ), floatArrayOf(qx, qy, qz, qw))
        } catch (e: Exception) {
            android.util.Log.e("ARImageDebug", "Error creating offset pose: ${e.message}")
            // Return the original pose if there was an error
            return originalPose
        }
    }

    // Store the plane type for use in translation
    private var currentPlaneType: com.google.ar.core.Plane.Type? = null

    // Store the last pose for coordinate transformations
    private var lastPose: com.google.ar.core.Pose? = null

    fun translate(dx: Float, dy: Float) {
        try {
            android.util.Log.d("ARImageDebug", "Translating image: dx=$dx, dy=$dy, planeType=$currentPlaneType")

            if (currentPlaneType == com.google.ar.core.Plane.Type.VERTICAL && lastPose != null) {
                // For vertical planes, we need to use the wall-aligned coordinate system
                // Extract the wall's orientation vectors
                val normalVector = lastPose!!.zAxis
                val worldUp = floatArrayOf(0f, 1f, 0f)

                // Calculate right vector (X-axis on the wall)
                val rightVector = floatArrayOf(
                    normalVector[1] * worldUp[2] - normalVector[2] * worldUp[1],
                    normalVector[2] * worldUp[0] - normalVector[0] * worldUp[2],
                    normalVector[0] * worldUp[1] - normalVector[1] * worldUp[0]
                )

                // Normalize right vector
                val rightMagnitude = Math.sqrt((rightVector[0] * rightVector[0] +
                                               rightVector[1] * rightVector[1] +
                                               rightVector[2] * rightVector[2]).toDouble()).toFloat()
                if (rightMagnitude > 0.0001f) {
                    rightVector[0] /= rightMagnitude
                    rightVector[1] /= rightMagnitude
                    rightVector[2] /= rightMagnitude
                }

                // Calculate up vector (Y-axis on the wall)
                val upVector = floatArrayOf(
                    rightVector[1] * normalVector[2] - rightVector[2] * normalVector[1],
                    rightVector[2] * normalVector[0] - rightVector[0] * normalVector[2],
                    rightVector[0] * normalVector[1] - rightVector[1] * normalVector[0]
                )

                // Transform the 2D screen movement (dx, dy) to 3D wall-aligned movement
                // dx moves along the right vector (X-axis on wall)
                // dy moves along the up vector (Y-axis on wall)
                val moveX = dx * 0.01f  // Scale factor for reasonable movement speed
                val moveY = dy * 0.01f

                // Apply movement in wall-aligned space
                modelMatrix[12] += rightVector[0] * moveX + upVector[0] * moveY
                modelMatrix[13] += rightVector[1] * moveX + upVector[1] * moveY
                modelMatrix[14] += rightVector[2] * moveX + upVector[2] * moveY

                android.util.Log.d("ARImageDebug", "Applied wall-aligned translation: right=$moveX, up=$moveY")
            } else {
                // Standard translation for horizontal planes or unknown plane types
                modelMatrix[12] += dx * 0.01f
                modelMatrix[13] += dy * 0.01f
            }
        } catch (e: Exception) {
            android.util.Log.e("ARImageDebug", "Error during translation: ${e.message}")
        }
    }

    fun scale(scaleFactor: Float, preserveAspectRatio: Boolean = true) {
        if (preserveAspectRatio) {
            // Uniform scaling
            for (i in 0..2) {
                modelMatrix[i * 4 + 0] *= scaleFactor
                modelMatrix[i * 4 + 1] *= scaleFactor
                modelMatrix[i * 4 + 2] *= scaleFactor
            }
        } else {
            // Non-uniform scaling (distorts image)
            modelMatrix[0] *= scaleFactor  // X scale
            modelMatrix[5] *= scaleFactor  // Y scale
        }
    }

    fun rotate(angleDegrees: Float) {
        try {
            android.util.Log.d("ARImageDebug", "Rotating image: angle=$angleDegrees, planeType=$currentPlaneType")

            if (currentPlaneType == com.google.ar.core.Plane.Type.VERTICAL && lastPose != null) {
                // For vertical planes, we need to rotate around the normal vector (perpendicular to the wall)
                val normalVector = lastPose!!.zAxis

                // Create a rotation matrix around the normal vector
                val rotationMatrix = FloatArray(16)
                Matrix.setRotateM(rotationMatrix, 0, angleDegrees, normalVector[0], normalVector[1], normalVector[2])

                // Apply the rotation
                val tempMatrix = FloatArray(16)
                Matrix.multiplyMM(tempMatrix, 0, rotationMatrix, 0, modelMatrix, 0)
                System.arraycopy(tempMatrix, 0, modelMatrix, 0, 16)

                android.util.Log.d("ARImageDebug", "Applied wall-normal rotation: angle=$angleDegrees")
            } else {
                // Standard rotation around Z-axis for horizontal planes or unknown plane types
                Matrix.rotateM(modelMatrix, 0, angleDegrees, 0f, 0f, 1f)
            }
        } catch (e: Exception) {
            android.util.Log.e("ARImageDebug", "Error during rotation: ${e.message}")
        }
    }

    /**
     * Rotates the image around the X-axis (pitch rotation)
     */
    fun rotateX(angleDegrees: Float) {
        try {
            android.util.Log.d("ARImageDebug", "Rotating image around X-axis: angle=$angleDegrees")

            if (currentPlaneType == com.google.ar.core.Plane.Type.VERTICAL && lastPose != null) {
                // For vertical planes, get the right vector (X-axis on wall)
                val rightVector = lastPose!!.xAxis

                // Create a rotation matrix around the right vector (X-axis on wall)
                val rotationMatrix = FloatArray(16)
                Matrix.setRotateM(rotationMatrix, 0, angleDegrees, rightVector[0], rightVector[1], rightVector[2])

                // Apply the rotation
                val tempMatrix = FloatArray(16)
                Matrix.multiplyMM(tempMatrix, 0, rotationMatrix, 0, modelMatrix, 0)
                System.arraycopy(tempMatrix, 0, modelMatrix, 0, 16)

                android.util.Log.d("ARImageDebug", "Applied X-axis rotation on vertical plane: angle=$angleDegrees")
            } else {
                // Standard rotation around X-axis for horizontal planes or unknown plane types
                Matrix.rotateM(modelMatrix, 0, angleDegrees, 1f, 0f, 0f)
                android.util.Log.d("ARImageDebug", "Applied standard X-axis rotation: angle=$angleDegrees")
            }
        } catch (e: Exception) {
            android.util.Log.e("ARImageDebug", "Error during X-axis rotation: ${e.message}")
        }
    }

    /**
     * Rotates the image around the Y-axis (yaw rotation)
     */
    fun rotateY(angleDegrees: Float) {
        try {
            android.util.Log.d("ARImageDebug", "Rotating image around Y-axis: angle=$angleDegrees")

            if (currentPlaneType == com.google.ar.core.Plane.Type.VERTICAL && lastPose != null) {
                // For vertical planes, get the up vector (Y-axis on wall)
                val upVector = lastPose!!.yAxis

                // Create a rotation matrix around the up vector (Y-axis on wall)
                val rotationMatrix = FloatArray(16)
                Matrix.setRotateM(rotationMatrix, 0, angleDegrees, upVector[0], upVector[1], upVector[2])

                // Apply the rotation
                val tempMatrix = FloatArray(16)
                Matrix.multiplyMM(tempMatrix, 0, rotationMatrix, 0, modelMatrix, 0)
                System.arraycopy(tempMatrix, 0, modelMatrix, 0, 16)

                android.util.Log.d("ARImageDebug", "Applied Y-axis rotation on vertical plane: angle=$angleDegrees")
            } else {
                // Standard rotation around Y-axis for horizontal planes or unknown plane types
                Matrix.rotateM(modelMatrix, 0, angleDegrees, 0f, 1f, 0f)
                android.util.Log.d("ARImageDebug", "Applied standard Y-axis rotation: angle=$angleDegrees")
            }
        } catch (e: Exception) {
            android.util.Log.e("ARImageDebug", "Error during Y-axis rotation: ${e.message}")
        }
    }

    /**
     * Rotates the image around the Z-axis (roll rotation) - same as original rotate method
     */
    fun rotateZ(angleDegrees: Float) {
        // This is the same as the original rotate method
        rotate(angleDegrees)
    }

    /**
     * Flips the image horizontally by applying a scale transformation
     */
    fun flipHorizontal() {
        try {
            android.util.Log.d("ARImageDebug", "Flipping image horizontally")

            // Create a flip matrix (scale by -1 on X-axis)
            val flipMatrix = FloatArray(16)
            Matrix.setIdentityM(flipMatrix, 0)
            Matrix.scaleM(flipMatrix, 0, -1f, 1f, 1f)

            // Apply the flip
            val tempMatrix = FloatArray(16)
            Matrix.multiplyMM(tempMatrix, 0, modelMatrix, 0, flipMatrix, 0)
            System.arraycopy(tempMatrix, 0, modelMatrix, 0, 16)

            android.util.Log.d("ARImageDebug", "Applied horizontal flip transformation")
        } catch (e: Exception) {
            android.util.Log.e("ARImageDebug", "Error during horizontal flip: ${e.message}")
        }
    }

    private fun loadTexture(bitmap: Bitmap): Int {
        try {
            // Clear any existing OpenGL errors before starting
            while (GLES30.glGetError() != GLES30.GL_NO_ERROR) {
                // Clear error queue
            }

            val textureHandle = IntArray(1)

            // Generate texture
            GLES30.glGenTextures(1, textureHandle, 0)

            // Check if texture generation was successful
            if (textureHandle[0] == 0) {
                android.util.Log.e("ARImageDebug", "Failed to generate texture")
                return 0
            }

            android.util.Log.d("ARImageDebug", "Generated texture ID: ${textureHandle[0]}")

            // Make sure we're using the right texture unit
            GLES30.glActiveTexture(GLES30.GL_TEXTURE0)

            // Bind to the texture in OpenGL
            GLES30.glBindTexture(GLES30.GL_TEXTURE_2D, textureHandle[0])

            // Check for OpenGL errors after binding
            val postBindError = GLES30.glGetError()
            if (postBindError != GLES30.GL_NO_ERROR) {
                android.util.Log.e("ARImageDebug", "OpenGL error after binding texture: $postBindError")
                // Continue anyway - we'll try to recover
            }

            // Set filtering with error checking
            GLES30.glTexParameteri(GLES30.GL_TEXTURE_2D, GLES30.GL_TEXTURE_MIN_FILTER, GLES30.GL_LINEAR)
            checkGlError("glTexParameteri MIN_FILTER")

            GLES30.glTexParameteri(GLES30.GL_TEXTURE_2D, GLES30.GL_TEXTURE_MAG_FILTER, GLES30.GL_LINEAR)
            checkGlError("glTexParameteri MAG_FILTER")

            // Set wrapping to prevent artifacts at edges
            GLES30.glTexParameteri(GLES30.GL_TEXTURE_2D, GLES30.GL_TEXTURE_WRAP_S, GLES30.GL_CLAMP_TO_EDGE)
            checkGlError("glTexParameteri WRAP_S")

            GLES30.glTexParameteri(GLES30.GL_TEXTURE_2D, GLES30.GL_TEXTURE_WRAP_T, GLES30.GL_CLAMP_TO_EDGE)
            checkGlError("glTexParameteri WRAP_T")

            // Create a mutable copy of the bitmap to ensure it's in a format OpenGL can handle
            val mutableBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true)

            // Load the bitmap into the bound texture
            GLUtils.texImage2D(GLES30.GL_TEXTURE_2D, 0, mutableBitmap, 0)

            // Check for OpenGL errors after loading bitmap
            val postLoadError = GLES30.glGetError()
            if (postLoadError != GLES30.GL_NO_ERROR) {
                android.util.Log.e("ARImageDebug", "OpenGL error after loading bitmap: $postLoadError")

                // Try a different approach if texImage2D failed
                try {
                    // Generate mipmaps to ensure proper rendering
                    GLES30.glGenerateMipmap(GLES30.GL_TEXTURE_2D)
                } catch (e: Exception) {
                    android.util.Log.e("ARImageDebug", "Error generating mipmaps: ${e.message}")
                }
            } else {
                android.util.Log.d("ARImageDebug", "Texture loaded successfully")

                // Generate mipmaps for better quality
                GLES30.glGenerateMipmap(GLES30.GL_TEXTURE_2D)
            }

            // Clean up the mutable bitmap if it's different from the original
            if (mutableBitmap != bitmap) {
                mutableBitmap.recycle()
            }

            return textureHandle[0]
        } catch (e: Exception) {
            android.util.Log.e("ARImageDebug", "Error loading texture: ${e.message}")
            android.util.Log.e("ARImageDebug", "Stack trace: ${e.stackTraceToString()}")
            return 0
        }
    }

    /**
     * Helper function to check for OpenGL errors and log them
     */
    private fun checkGlError(op: String) {
        val error = GLES30.glGetError()
        if (error != GLES30.GL_NO_ERROR) {
            android.util.Log.e("ARImageDebug", "$op: glError $error")
        }
    }

    private fun loadShader(type: Int, shaderCode: String): Int {
        val shader = GLES30.glCreateShader(type)
        GLES30.glShaderSource(shader, shaderCode)
        GLES30.glCompileShader(shader)

        // Check compilation status
        val compiled = IntArray(1)
        GLES30.glGetShaderiv(shader, GLES30.GL_COMPILE_STATUS, compiled, 0)
        if (compiled[0] == 0) {
            android.util.Log.e("ARImageDebug", "Could not compile shader $type: ${GLES30.glGetShaderInfoLog(shader)}")
            GLES30.glDeleteShader(shader)
            return 0
        }

        return shader
    }

    /**
     * Recreates the shader program to recover from OpenGL errors
     */
    private fun recreateShaderProgram() {
        android.util.Log.d("ARImageDebug", "Recreating shader program")

        try {
            // Delete the old program if it exists
            if (mProgram != 0) {
                GLES30.glDeleteProgram(mProgram)
            }

            // Prepare shaders and OpenGL program
            val vertexShader = loadShader(GLES30.GL_VERTEX_SHADER, vertexShaderCode)
            val fragmentShader = loadShader(GLES30.GL_FRAGMENT_SHADER, fragmentShaderCode)

            // Check if shaders were compiled successfully
            if (vertexShader == 0 || fragmentShader == 0) {
                android.util.Log.e("ARImageDebug", "Failed to compile one or both shaders during recreation")
                return
            }

            // Create the program and attach shaders
            mProgram = GLES30.glCreateProgram()
            if (mProgram == 0) {
                android.util.Log.e("ARImageDebug", "Failed to create GL program during recreation")
                return
            }

            GLES30.glAttachShader(mProgram, vertexShader)
            GLES30.glAttachShader(mProgram, fragmentShader)
            GLES30.glLinkProgram(mProgram)

            // Check linking status
            val linkStatus = IntArray(1)
            GLES30.glGetProgramiv(mProgram, GLES30.GL_LINK_STATUS, linkStatus, 0)
            if (linkStatus[0] != GLES30.GL_TRUE) {
                android.util.Log.e("ARImageDebug", "Could not link program during recreation: ${GLES30.glGetProgramInfoLog(mProgram)}")
                GLES30.glDeleteProgram(mProgram)
                mProgram = 0
                return
            }

            // Get handles to shader variables
            mPositionHandle = GLES30.glGetAttribLocation(mProgram, "vPosition")
            mTexCoordHandle = GLES30.glGetAttribLocation(mProgram, "vTexCoord")
            mMVPMatrixHandle = GLES30.glGetUniformLocation(mProgram, "uMVPMatrix")
            mTextureHandle = GLES30.glGetUniformLocation(mProgram, "uTexture")
            mOpacityHandle = GLES30.glGetUniformLocation(mProgram, "uOpacity")

            android.util.Log.d("ARImageDebug", "Shader program recreated successfully")
            android.util.Log.d("ARImageDebug", "New shader handles - Position: $mPositionHandle, TexCoord: $mTexCoordHandle, MVP: $mMVPMatrixHandle, Texture: $mTextureHandle, Opacity: $mOpacityHandle")
        } catch (e: Exception) {
            android.util.Log.e("ARImageDebug", "Error recreating shader program: ${e.message}")
            android.util.Log.e("ARImageDebug", "Stack trace: ${e.stackTraceToString()}")
        }
    }

    fun release() {
        // Delete the texture when no longer needed
        if (textureId != -1) {
            GLES30.glDeleteTextures(1, intArrayOf(textureId), 0)
            textureId = -1
        }
    }
}