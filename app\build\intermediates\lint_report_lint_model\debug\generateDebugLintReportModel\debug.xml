<variant
    name="debug"
    package="com.deace.deacear"
    minSdkVersion="29"
    targetSdkVersion="35"
    debuggable="true"
    mergedManifest="build\intermediates\merged_manifest\debug\processDebugMainManifest\AndroidManifest.xml"
    manifestMergeReport="build\outputs\logs\manifest-merger-debug-report.txt"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android.txt-8.9.2"
    partialResultsDir="build\intermediates\lint_partial_results\debug\lintAnalyzeDebug\out">
  <buildFeatures
      viewBinding="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\debug\java;src\main\kotlin;src\debug\kotlin"
        resDirectories="src\main\res;src\debug\res"
        assetsDirectories="src\main\assets;src\debug\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build\intermediates\javac\debug\compileDebugJavaWithJavac\classes;build\tmp\kotlin-classes\debug;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\debug\processDebugResources\R.jar"
      type="MAIN"
      applicationId="com.deace.deacear"
      generatedSourceFolders="build\generated\ap_generated_sources\debug\out;build\generated\source\buildConfig\debug;build\generated\data_binding_base_class_source_out\debug\out"
      generatedResourceFolders="build\generated\res\resValues\debug"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b2470fa599ee8209bbbb15e1deb2ff\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
