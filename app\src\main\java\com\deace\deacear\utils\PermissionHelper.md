Here's the complete implementation of `PermissionHelper.kt` for the Graffiti AR Projection Assistant:

```kotlin
package com.example.graffitiar.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

object PermissionHelper {

    // Define all permissions needed by the app
    private val REQUIRED_PERMISSIONS = arrayOf(
        Manifest.permission.CAMERA,
        Manifest.permission.READ_EXTERNAL_STORAGE,
        Manifest.permission.WRITE_EXTERNAL_STORAGE
    )

    // Check if all permissions are granted
    fun hasAllPermissions(context: Context): Boolean {
        return REQUIRED_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }

    // Check if a specific permission is granted
    fun hasPermission(context: Context, permission: String): Boolean {
        return ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
    }

    // Check if camera permission is granted (used frequently in AR apps)
    fun hasCameraPermission(context: Context): Boolean {
        return hasPermission(context, Manifest.permission.CAMERA)
    }

    // Check if storage permissions are granted
    fun hasStoragePermissions(context: Context): Boolean {
        return hasPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE) &&
                hasPermission(context, Manifest.permission.WRITE_EXTERNAL_STORAGE)
    }

    // Request permissions from an Activity
    fun requestPermissions(activity: Activity, requestCode: Int) {
        ActivityCompat.requestPermissions(activity, REQUIRED_PERMISSIONS, requestCode)
    }

    // Request specific permissions
    fun requestSpecificPermissions(activity: Activity, permissions: Array<String>, requestCode: Int) {
        ActivityCompat.requestPermissions(activity, permissions, requestCode)
    }

    // Check if all requested permissions were granted in onRequestPermissionsResult
    fun allPermissionsGranted(grantResults: IntArray): Boolean {
        return grantResults.all { it == PackageManager.PERMISSION_GRANTED }
    }

    // Check if explanation should be shown for any permission
    fun shouldShowRationale(activity: Activity, permission: String): Boolean {
        return ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)
    }

    // Special handling for Android 11+ (Scoped Storage)
    fun isScopedStorageRequired(): Boolean {
        return android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R
    }

    // Handle the case where permissions are permanently denied
    fun arePermissionsPermanentlyDenied(activity: Activity, permissions: Array<String>): Boolean {
        return permissions.any { permission ->
            !ActivityCompat.shouldShowRequestPermissionRationale(activity, permission) &&
                    !hasPermission(activity, permission)
        }
    }
}
```

### Key Features:

1. **Comprehensive Permission Management**:
   - Handles all permissions needed for AR functionality (camera and storage)
   - Provides both bulk and individual permission checks

2. **Convenience Methods**:
   - Specialized methods for common permissions (camera, storage)
   - Easy-to-use request methods with request codes

3. **Permission Rationale Handling**:
   - Checks if rationale should be shown to users
   - Identifies permanently denied permissions

4. **Modern Android Support**:
   - Includes special handling for Scoped Storage (Android 11+)
   - Works with both old and new permission models

5. **Result Processing**:
   - Helper to analyze permission grant results
   - Simplifies the permission callback handling

### Usage Examples:

1. **Checking permissions**:
```kotlin
if (!PermissionHelper.hasCameraPermission(this)) {
    PermissionHelper.requestSpecificPermissions(
        this, 
        arrayOf(Manifest.permission.CAMERA), 
        CAMERA_PERMISSION_REQUEST_CODE
    )
}
```

2. **Handling permission results**:
```kotlin
override fun onRequestPermissionsResult(
    requestCode: Int,
    permissions: Array<String>,
    grantResults: IntArray
) {
    super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    when (requestCode) {
        CAMERA_PERMISSION_REQUEST_CODE -> {
            if (PermissionHelper.allPermissionsGranted(grantResults)) {
                // Permission granted, proceed with AR
                initializeAR()
            } else {
                // Handle denied permission
                if (PermissionHelper.arePermissionsPermanentlyDenied(this, permissions)) {
                    showPermissionSettingsDialog()
                }
            }
        }
    }
}
```

3. **Requesting all required permissions**:
```kotlin
if (!PermissionHelper.hasAllPermissions(this)) {
    PermissionHelper.requestPermissions(this, PERMISSIONS_REQUEST_CODE)
}
```

### Integration Notes:

1. Add these permissions to your AndroidManifest.xml:
```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

2. For Android 10+ (API 29+), add this to your manifest:
```xml
<application
    ...
    android:requestLegacyExternalStorage="true">
```

3. Recommended to define permission request codes in a companion object:
```kotlin
companion object {
    private const val CAMERA_PERMISSION_REQUEST_CODE = 100
    private const val STORAGE_PERMISSION_REQUEST_CODE = 101
    private const val ALL_PERMISSIONS_REQUEST_CODE = 200
}
```

This implementation provides a clean, reusable way to handle all permission-related tasks in your AR application, following best practices for permission management on Android. The helper is designed to work seamlessly with the other components in your Graffiti AR Projection Assistant.