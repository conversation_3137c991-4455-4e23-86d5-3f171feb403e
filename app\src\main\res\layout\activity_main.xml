<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <android.opengl.GLSurfaceView
        android:id="@+id/ar_surface_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- Loading screen -->
    <LinearLayout
        android:id="@+id/loading_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80000000"
        android:gravity="center"
        android:orientation="vertical">

        <androidx.core.widget.ContentLoadingProgressBar
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:indeterminate="true" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/loading_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/scanning_environment"
            android:textColor="@color/white"
            android:textSize="16sp" />
    </LinearLayout>

    <!-- Main menu -->
    <LinearLayout
        android:id="@+id/main_menu_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_margin="16dp"
        android:background="@drawable/rounded_panel_background"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp">

        <!-- Deace Logo Sticker - Adjusted to align with other icons -->
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/deace_logo"
            android:layout_width="32dp"
            android:layout_height="48dp"
            android:layout_marginEnd="8dp"
            android:contentDescription="@string/app_name"
            android:scaleType="fitCenter"
            android:layout_gravity="center_vertical"
            android:src="@drawable/deace_logo_sticker" />

        <!-- Menu Icons Container -->
        <LinearLayout
            android:id="@+id/menu_icons_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/grid_tab_button"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/grid_tab"
                android:padding="6dp"
                android:scaleType="fitCenter"
                android:layout_gravity="center"
                android:src="@drawable/ic_grid" />

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/image_tab_button"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/image_tab"
                android:padding="6dp"
                android:scaleType="fitCenter"
                android:layout_gravity="center"
                android:src="@drawable/ic_image" />

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/export_tab_button"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/export_tab"
                android:padding="6dp"
                android:scaleType="fitCenter"
                android:layout_gravity="center"
                android:src="@drawable/ic_export" />

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/config_tab_button"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/config_tab"
                android:padding="6dp"
                android:scaleType="fitCenter"
                android:layout_gravity="center"
                android:src="@drawable/ic_settings" />
        </LinearLayout>

    </LinearLayout>

    <!-- Grid panel - Positioned to the right of the grid icon button -->
    <LinearLayout
        android:id="@+id/grid_panel_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_toRightOf="@id/main_menu_layout"
        android:layout_marginTop="16dp"
        android:background="@drawable/rounded_panel_background"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp"
        android:visibility="gone">

        <!-- Grid Format label -->
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/grid_format"
            android:textSize="12sp"
            android:textColor="@color/white" />

        <!-- Icon buttons for grid formats -->
        <ImageButton
            android:id="@+id/vertical_format_button"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginStart="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/vertical"
            android:src="@drawable/ic_grid_vertical" />

        <ImageButton
            android:id="@+id/square_format_button"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginStart="4dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/square"
            android:src="@drawable/ic_grid_square" />

        <ImageButton
            android:id="@+id/horizontal_format_button"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginStart="4dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/horizontal"
            android:src="@drawable/ic_grid_horizontal" />

        <!-- Fix Grid button with rounded corners -->
        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/fix_grid_button"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_marginStart="8dp"
            android:minWidth="70dp"
            android:textSize="11sp"
            android:padding="4dp"
            android:background="@drawable/rounded_button_background"
            android:textColor="@color/white"
            android:text="@string/fix_grid" />

        <!-- Grid Opacity section -->
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="@string/grid_opacity"
            android:textSize="12sp"
            android:textColor="@color/white" />

        <androidx.appcompat.widget.AppCompatSeekBar
            android:id="@+id/grid_opacity_seekbar"
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:max="100"
            android:progress="50"
            style="@style/WhiteSeekBar" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/grid_opacity_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="50%"
            android:textSize="11sp"
            android:textColor="@color/white" />
    </LinearLayout>

    <!-- Image panel - Simplified to a single line like grid panel -->
    <LinearLayout
        android:id="@+id/image_panel_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_toRightOf="@id/main_menu_layout"
        android:layout_marginTop="16dp"
        android:background="@drawable/rounded_panel_background"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp"
        android:visibility="gone">

        <!-- Load Image button with rounded corners, styled like Fix Grid button -->
        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/load_image_button"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:minWidth="100dp"
            android:textSize="11sp"
            android:padding="4dp"
            android:background="@drawable/rounded_button_background"
            android:textColor="@color/white"
            android:text="@string/load_image" />

        <!-- Thumbnail of selected image -->
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/image_thumbnail"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginStart="4dp"
            android:background="@drawable/rounded_panel_background"
            android:padding="2dp"
            android:scaleType="centerCrop"
            android:visibility="gone" />

        <!-- Image Selected Status Message - Shows filename when image is selected -->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/image_selected_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="No image selected"
            android:textSize="12sp"
            android:textColor="@color/white" />

        <!-- Fix Image button with rounded corners -->
        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/fix_image_button"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_marginStart="8dp"
            android:minWidth="70dp"
            android:textSize="11sp"
            android:padding="4dp"
            android:background="@drawable/rounded_button_background"
            android:textColor="@color/white"
            android:text="@string/fix_image" />

        <!-- Test button for debugging tap detection -->
        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/test_image_placement_button"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_marginStart="8dp"
            android:minWidth="90dp"
            android:textSize="11sp"
            android:padding="4dp"
            android:background="@drawable/rounded_button_background"
            android:textColor="#FFFFFF"
            android:backgroundTint="#FF5722"
            android:text="TEST: Place" />

        <!-- Hidden elements that we'll keep for functionality but not show in the UI -->
        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/release_image_button"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_marginStart="8dp"
            android:minWidth="90dp"
            android:textSize="11sp"
            android:padding="4dp"
            android:background="@drawable/rounded_button_background"
            android:textColor="@color/white"
            android:visibility="gone"
            android:text="@string/release_image" />

        <androidx.appcompat.widget.AppCompatSeekBar
            android:id="@+id/image_opacity_seekbar"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            android:max="100"
            android:progress="50" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/image_opacity_value"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            android:text="@string/opacity_format_50" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/preserve_aspect_switch"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            android:checked="true" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/image_size_text"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            android:text="@string/no_image_loaded" />
    </LinearLayout>

    <!-- Export panel - Positioned to the right of the main menu, like grid panel -->
    <LinearLayout
        android:id="@+id/export_panel_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_toRightOf="@id/main_menu_layout"
        android:layout_marginTop="16dp"
        android:background="@drawable/rounded_panel_background"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp"
        android:visibility="gone">

        <!-- Export button with rounded corners, styled like Fix Grid button -->
        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/export_button"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:minWidth="120dp"
            android:textSize="11sp"
            android:padding="4dp"
            android:background="@drawable/rounded_button_background"
            android:textColor="@color/white"
            android:text="@string/export_ar_view" />
    </LinearLayout>

    <!-- Deace panel - Positioned to the right of the main menu, like grid panel -->
    <LinearLayout
        android:id="@+id/deace_panel_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_toRightOf="@id/main_menu_layout"
        android:layout_marginTop="16dp"
        android:background="@drawable/rounded_panel_background"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="8dp"
        android:visibility="gone">

        <!-- Confirm Reinit App button with rounded corners, styled like Fix Grid button -->
        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/confirm_reinit_button"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:minWidth="150dp"
            android:textSize="11sp"
            android:padding="4dp"
            android:background="@drawable/rounded_button_background"
            android:textColor="@color/white"
            android:text="@string/confirm_reinit_app" />
    </LinearLayout>

    <!-- Config panel - Positioned below the main menu, full width -->
    <ScrollView
        android:id="@+id/config_panel_scroll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/main_menu_layout"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginTop="8dp"
        android:visibility="gone">

        <LinearLayout
            android:id="@+id/config_panel_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/rounded_panel_background"
            android:orientation="vertical"
            android:padding="12dp">

            <!-- AR Settings Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/ar_settings"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/show_planes_switch"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/show_planes"
                android:textColor="@color/white"
                android:textSize="12sp" />

            <Button
                android:id="@+id/reset_session_button"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_marginTop="8dp"
                android:background="@drawable/rounded_button_background"
                android:padding="4dp"
                android:text="@string/reset_session"
                android:textColor="@color/white"
                android:textSize="11sp" />

            <!-- Grid Options Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/grid_options"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/default_grid_format"
                android:textColor="@color/white"
                android:textSize="12sp" />

            <Spinner
                android:id="@+id/default_grid_format_spinner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/default_opacity"
                android:textColor="@color/white"
                android:textSize="12sp" />

            <androidx.appcompat.widget.AppCompatSeekBar
                android:id="@+id/default_grid_opacity_seekbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:max="100"
                android:progress="50"
                style="@style/WhiteSeekBar" />

            <!-- Export Settings Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/export_settings"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <RadioGroup
                android:id="@+id/image_quality_radio_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/normal_quality_radio"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/normal_quality"
                    android:textColor="@color/white"
                    android:textSize="12sp" />

                <RadioButton
                    android:id="@+id/max_quality_radio"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="@string/max_quality"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </RadioGroup>

            <RadioGroup
                android:id="@+id/file_format_radio_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/jpg_format_radio"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/jpg_format"
                    android:textColor="@color/white"
                    android:textSize="12sp" />

                <RadioButton
                    android:id="@+id/png_format_radio"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="@string/png_format"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </RadioGroup>

            <!-- App Settings Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/app_settings"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <Button
                android:id="@+id/reset_settings_button"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_marginTop="8dp"
                android:background="@drawable/rounded_button_background"
                android:padding="4dp"
                android:text="@string/reset_settings"
                android:textColor="@color/white"
                android:textSize="11sp" />

            <Button
                android:id="@+id/help_about_button"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/rounded_button_background"
                android:padding="4dp"
                android:text="@string/help_about"
                android:textColor="@color/white"
                android:textSize="11sp" />
        </LinearLayout>
    </ScrollView>

    <!-- Reticle for targeting -->
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/reticle"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_centerInParent="true"
        android:src="@drawable/ic_reticle"
        android:visibility="invisible" />

    <!-- Tracking Quality Indicator (spec 4.3) - Enhanced version -->
    <LinearLayout
        android:id="@+id/tracking_indicator_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentBottom="true"
        android:layout_margin="16dp"
        android:background="@drawable/rounded_panel_background"
        android:padding="8dp"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="visible">

        <!-- Status row with indicator and text -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <View
                android:id="@+id/tracking_quality_indicator"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:background="@drawable/circle_shape"
                android:backgroundTint="#FF0000" /> <!-- Default to red -->

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tracking_quality_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:text="@string/tracking_lost"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:maxWidth="200dp" />

            <!-- Help button removed as requested -->
        </LinearLayout>

        <!-- Tracking initialization message -->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tracking_init_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="@string/scanning_environment"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:gravity="center"
            android:maxWidth="250dp"
            android:visibility="visible" />

        <!-- Tracking progress bar -->
        <ProgressBar
            android:id="@+id/tracking_progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:layout_marginTop="4dp"
            android:progressDrawable="@drawable/tracking_progress_background"
            android:max="100"
            android:progress="0"
            android:visibility="visible" />
    </LinearLayout>

    <!-- Help Button (spec 4.4) -->
    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/help_button"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_margin="16dp"
        android:background="@drawable/rounded_panel_background"
        android:contentDescription="@string/help"
        android:src="@drawable/ic_help" />

    <!-- Lock Image Button - Added for quick image locking -->
    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/quick_lock_image_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_toLeftOf="@id/help_button"
        android:layout_marginBottom="16dp"
        android:layout_marginRight="16dp"
        android:background="@drawable/rounded_button_background"
        android:padding="8dp"
        android:text="Lock Image"
        android:textColor="@color/white"
        android:visibility="gone" />
</RelativeLayout>