Provide full code for an Android project using ARCore, to be developed with Android Studio.
Functionalities are described below :

---

**Version:** 0.1
**Date:** 2025-04-20
**Project:** Graffiti AR Projection Assistant

---

# **Enhanced Specifications: AR Graffiti Projection App**

## 🎯 **1. Core Functionality & Vision**

*   **Objective:** Provide an Android application utilizing ARCore to accurately project a user-selected 2D image or a reference grid onto a real-world vertical flat surface (wall).
*   **AR Detection:** The application continuously scans the environment using the device's rear camera and ARCore's plane detection capabilities, specifically targeting **vertical planes**. Visual feedback will guide the user during the detection phase.
*   **Interaction:** Once a suitable vertical plane is detected and targeted by the user, they can virtually place ("anchor") a grid or an image onto it. The placed object can then be manipulated (moved, resized, opacity adjusted) before being fixed.
*   **Use Case:** Primarily designed to assist artists (e.g., graffiti artists, muralists) in visualizing and scaling their designs onto real walls before painting.
*   **Export:** The final augmented view (projected image on the wall, from the user's perspective) can be captured and saved as a standard image file, excluding auxiliary UI elements like the grid or menus.

## 📲 **2. User Interface (UI) & User Experience (UX)**

### **2.1. Main Menu (`Menu Principal`)**

*   **Type:** Floating panel, anchored to the **top-left corner** of the screen by default.
*   **Appearance:** Semi-transparent background to minimize obstruction of the AR view. Clear iconography for each tab.
*   **Tabs:**
    *   `[Icon] Grille`
    *   `[Icon] Image`
    *   `[Icon] Export`
    *   `[Icon] Config`
*   **Behavior:**
    *   Tapping a tab expands its associated options panel horizontally below or adjacent to the main menu.
    *   Only **one tab's options panel** can be visible at a time. Tapping another tab closes the current one and opens the new one. Tapping the active tab again closes its options panel.
    *   **Minimize/Maximize:** A dedicated button (`[Icon] Minimize/Maximize`) on the main menu panel allows collapsing it to a small icon/button (perhaps just the app logo or a menu icon) and expanding it back. Its state (minimized/maximized) should persist across sessions.
    *   **Orientation:** The menu consistently re-anchors to the top-left corner after device rotation (portrait/landscape), adjusting its layout gracefully.
*   **Contextual Controls:** Options associated with each tab (buttons, sliders) appear within their respective expanded panels, ideally organized logically.

### **2.2. AR View & Plane Detection Feedback**

*   **Scanning Phase:** While ARCore is initializing and scanning for planes:
    *   Display subtle visual cues on screen (e.g., animated dots or a reticle) indicating the scanning process.
    *   Show a persistent textual hint like: "Pointez votre appareil vers un mur et déplacez-le lentement."
*   **Plane Detection:**
    *   Once vertical planes are detected, overlay them with a **subtle, semi-transparent texture or outline** to make them visible to the user. This visualization should be toggleable via the `Config` tab.
    *   Provide a **targeting reticle** (e.g., a crosshair or circle) in the center of the screen. When this reticle hovers over a valid detected vertical plane, change its color or shape to indicate that placement is possible.
*   **Placement Initiation:** Placement of a grid or image occurs at the intersection point of the central reticle and the targeted detected plane when the user performs the placement action (e.g., taps a "Place" button or taps the screen).

### **2.3. "Grille" Tab (`Onglet Grille`)**

*   **Purpose:** Allows placing and adjusting a reference grid on the detected wall.
*   **Initial State:** When the tab is opened, if no grid is currently placed, the format selection buttons are shown.
*   **Format Selection:**
    *   Buttons: `[Icon] Rectangle Vertical` (e.g., 9:16), `[Icon] Carré` (1:1), `[Icon] Rectangle Horizontal` (e.g., 16:9). *Specify exact default ratios in `Config`.*
    *   Tapping a format button instantly displays a preview of the grid in AR, centered on the reticle's target point on the wall, oriented vertically relative to gravity. The grid is initially placed but not "fixed".
*   **Manipulation (Before Fixing):**
    *   **Move:** Drag with **one finger** on the grid object to reposition it across the surface of the *same* detected plane.
    *   **Resize:** Use a **two-finger pinch gesture** on the grid object to scale it up or down uniformly.
    *   **Rotate:** Use a **two-finger twist gesture** on the grid object to rotate it around its center point (parallel to the wall surface). *Consider if rotation is truly needed for a reference grid, might add complexity.*
    *   **Opacity Control:**
        *   Slider: A horizontal slider (0% to 100%) for intuitive adjustment.
        *   Buttons: `+` / `-` buttons for fine-tuning opacity in fixed increments (e.g., 10%).
        *   Display: A text label shows the current opacity percentage (e.g., "Opacité : 50%").
*   **Placement (Fixing):**
    *   Button: `[Icon] Fixer la grille`
        *   Action: Creates a persistent **ARCore Anchor** at the grid's current pose (position and orientation). This attaches the grid firmly to that point in the real world.
        *   State Change: The grid is now "fixed". Manipulation gestures (move, resize, rotate) are disabled. The format selection buttons are hidden. The "Fixer la grille" button is replaced by "Libérer la grille".
    *   Button: `[Icon] Libérer la grille`
        *   Action: Detaches the grid from its ARCore Anchor, making it manipulable again.
        *   State Change: Re-enables manipulation gestures, shows format selection buttons (or perhaps an "Edit" button), and replaces "Libérer la grille" with "Fixer la grille".
*   **Visual Feedback:** Highlight the grid or show bounding box handles during manipulation.

### **2.4. "Image" Tab (`Onglet Image`)**

*   **Purpose:** Allows loading a user image, placing it (potentially aligned with the grid), manipulating it, and fixing it.
*   **Image Loading:**
    *   Button: `[Icon] Charger une image`
        *   Action: Opens the standard Android **Storage Access Framework** (SAF) picker, filtering for common image types (JPG, PNG, WEBP).
        *   Error Handling: Display user-friendly messages for file access errors, unsupported formats, or excessively large files (implement size check/downsampling if needed).
*   **AR Display & Initial Placement:**
    *   Upon successful loading, the image is displayed in AR.
    *   **Placement Logic:**
        *   **If a fixed grid exists:** The image is initially placed centered **relative to the fixed grid's anchor**, potentially scaled to fit within the grid boundaries by default (configurable?).
        *   **If no fixed grid exists:** The image is placed centered on the reticle's target point on the detected wall, similar to the grid placement. An ARCore Anchor may be created implicitly or upon fixing.
    *   The image initially respects its original aspect ratio.
*   **Manipulation (Before Fixing):**
    *   **Move:** Drag with **one finger** on the image object.
    *   **Resize:** **Two-finger pinch gesture**. Offer an option in `Config` or a toggle button: `[ ] Conserver les proportions` (Preserve Aspect Ratio). If unchecked, pinching can distort the image; if checked, scaling is uniform.
    *   **Rotate:** Optional **two-finger twist gesture**, similar to the grid.
    *   **Opacity Control:** Same slider/buttons/display mechanism as the grid, but potentially with different step increments (e.g., 5% as specified in `Config`).
*   **Placement (Fixing):**
    *   Button: `[Icon] Fixer l'image`
        *   Action: Creates/updates an ARCore Anchor for the image's pose. Locks position, size, rotation, and opacity.
        *   State Change: Disables manipulation gestures and the "Fixer l'image" button. Displays the "Libérer l'image" button.
    *   Button: `[Icon] Libérer l'image`
        *   Action: Detaches the image from its anchor, making it manipulable again.
        *   State Change: Re-enables manipulation, replaces "Libérer l'image" with "Fixer l'image".
*   **Layering:** The user image should always render **on top** of the reference grid if both are present.

### **2.5. "Export" Tab (`Onglet Export`)**

*   **Purpose:** Capture the current AR scene as a static image file.
*   **Function:**
    *   Button: `[Icon] Exporter la vue AR` (More descriptive than just "Exporter image")
    *   Action:
        1.  Captures the **current camera feed** combined with the **rendered virtual content (user image ONLY)**.
        2.  **Excludes:** The reference grid (if visible), all UI elements (menus, buttons, reticle, plane visualizations).
        3.  Saves the composite image as a file (PNG or JPG, based on `Config`) to the location specified in `Config`.
        4.  Uses the filename pattern defined in `Config`, handling potential naming conflicts with incrementing numbers.
        5.  Provides user feedback: A brief confirmation message (e.g., "Image sauvegardée dans /Pictures/Graffiti_AR/") or an error message if saving fails.
*   **Consideration:** The exported image resolution will depend on the camera feed resolution and the `Config` setting (screen vs. max photo resolution). Capturing at max photo resolution might introduce a slight delay and requires careful handling of ARCore rendering alignment.

### **2.6. "Config" Tab (`Onglet Config`)**

*   **Purpose:** Centralized location for user preferences and application settings. Settings are saved persistently (e.g., using SharedPreferences or a dedicated file).
*   **Structure:** Organize settings into collapsible sections for clarity.

    *   **1. Paramètres AR (`AR Settings`)**
        *   `[Button]` **Réinitialiser la session AR:** Clears existing anchors and restarts plane detection (useful if tracking is lost).
        *   `[Switch]` **Visualiser les plans détectés:** Toggle visibility of the semi-transparent overlays on detected walls.
        *   *(Future)* Potentially add options for detection sensitivity if ARCore API allows.

    *   **2. Options de la Grille (`Grid Options`)**
        *   **Format par défaut:** Radio buttons or Dropdown: `Vertical (9:16)`, `Carré (1:1)`, `Horizontal (16:9)` (Select one as the initial default).
        *   **Transparence par défaut:** Slider or input field (0-100%), default `50%`.
        *   **Couleur de la grille:** Color picker (default: e.g., white or cyan).
        *   **Épaisseur des lignes:** Dropdown or input (e.g., `Fine`, `Moyenne`, `Épaisse`).

    *   **3. Options de l'Image Projetée (`Projected Image Options`)**
        *   **Transparence par défaut:** Slider or input field (0-100%), default `50%`.
        *   `[Switch]` **Conserver les proportions par défaut:** Checked/Unchecked (default: Checked). Controls initial state of aspect ratio lock during resize.
        *   *(Future)* Default scaling mode when placed relative to grid (e.g., Fit Inside, Fill).

    *   **4. Paramètres d'Export (`Export Settings`)**
        *   **Qualité d'image:** Radio buttons: `Normale` (matches screen/preview resolution), `Maximale` (attempts highest photo resolution - explain potential delay/limitations).
        *   **Format de fichier:** Radio buttons: `PNG` (sans perte, transparence supportée si besoin futur), `JPG` (compressé, plus petit).
        *   **Modèle de nom de fichier:** Text field (editable, default: `Graffiti_AR_{NNN}`). `{NNN}` represents an auto-incrementing number. Include date/time variables? e.g., `Graffiti_AR_{YYYYMMDD}_{HHMMSS}`.
        *   **Répertoire de sauvegarde:** Display current path (e.g., `/Pictures/Graffiti_AR/`). `[Button]` **Choisir le dossier** (opens SAF directory picker).

    *   **5. Général (`General`)**
        *   `[Button]` **Restaurer les paramètres par défaut:** Resets all config options to their initial state.
        *   `[Link]` **Aide / À Propos:** Display :"Graffiti AR Deace v0.1 2025. ©DEACE :  Auteur membre de l’ADAGP. Une autorisation est nécessaire pour toute utilisation des œuvres (www.adagp.fr). More on www.deace.com"

## **3. Non-Functional Requirements**

*   **Performance:** Maintain a smooth frame rate (ideally 30+ FPS) during AR interaction. Optimize rendering and image loading.
*   **Battery Consumption:** Be mindful of battery usage, as AR processing and camera use are intensive.
*   **Compatibility:** Target a reasonable range of ARCore-compatible Android devices and OS versions. Clearly state minimum requirements.
*   **Error Handling:** Implement robust error handling for file operations, network (if any future features added), and ARCore state changes (e.g., tracking loss).
*   **Permissions:** Request necessary permissions (Camera, Storage) gracefully with clear explanations.

## **4. Future Enhancements (Optional Ideas)**

*   **Video Recording:** Export the AR session as a video.
*   **Multiple Images/Layers:** Allow placing and managing multiple images.
*   **Depth API Integration:** Use depth information for more realistic occlusion (parts of the image hidden behind real-world objects).
*   **Measurement Tools:** Add tools to measure distances or areas on the detected wall using AR.
*   **Cloud Sync/Sharing:** Save projects or export images to cloud storage or share directly.
*   **Grid Customization:** Allow more complex grid types (e.g., perspective lines, custom subdivisions).
*   **Color Picking:** Sample colors from the real-world environment.

---
