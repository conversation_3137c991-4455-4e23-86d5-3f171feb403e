// Generated by view binder compiler. Do not edit!
package com.deace.deacear.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatSeekBar;
import androidx.appcompat.widget.AppCompatSpinner;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.deace.deacear.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class GridPanelBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final AppCompatButton fixGridButton;

  @NonNull
  public final AppCompatButton gridColorButton;

  @NonNull
  public final AppCompatSpinner gridLineThicknessSpinner;

  @NonNull
  public final AppCompatSeekBar gridOpacitySeekbar;

  @NonNull
  public final AppCompatTextView gridOpacityValue;

  @NonNull
  public final AppCompatButton horizontalFormatButton;

  @NonNull
  public final AppCompatButton releaseGridButton;

  @NonNull
  public final AppCompatButton squareFormatButton;

  @NonNull
  public final AppCompatButton verticalFormatButton;

  private GridPanelBinding(@NonNull LinearLayout rootView, @NonNull AppCompatButton fixGridButton,
      @NonNull AppCompatButton gridColorButton, @NonNull AppCompatSpinner gridLineThicknessSpinner,
      @NonNull AppCompatSeekBar gridOpacitySeekbar, @NonNull AppCompatTextView gridOpacityValue,
      @NonNull AppCompatButton horizontalFormatButton, @NonNull AppCompatButton releaseGridButton,
      @NonNull AppCompatButton squareFormatButton, @NonNull AppCompatButton verticalFormatButton) {
    this.rootView = rootView;
    this.fixGridButton = fixGridButton;
    this.gridColorButton = gridColorButton;
    this.gridLineThicknessSpinner = gridLineThicknessSpinner;
    this.gridOpacitySeekbar = gridOpacitySeekbar;
    this.gridOpacityValue = gridOpacityValue;
    this.horizontalFormatButton = horizontalFormatButton;
    this.releaseGridButton = releaseGridButton;
    this.squareFormatButton = squareFormatButton;
    this.verticalFormatButton = verticalFormatButton;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static GridPanelBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static GridPanelBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.grid_panel, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static GridPanelBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.fix_grid_button;
      AppCompatButton fixGridButton = ViewBindings.findChildViewById(rootView, id);
      if (fixGridButton == null) {
        break missingId;
      }

      id = R.id.grid_color_button;
      AppCompatButton gridColorButton = ViewBindings.findChildViewById(rootView, id);
      if (gridColorButton == null) {
        break missingId;
      }

      id = R.id.grid_line_thickness_spinner;
      AppCompatSpinner gridLineThicknessSpinner = ViewBindings.findChildViewById(rootView, id);
      if (gridLineThicknessSpinner == null) {
        break missingId;
      }

      id = R.id.grid_opacity_seekbar;
      AppCompatSeekBar gridOpacitySeekbar = ViewBindings.findChildViewById(rootView, id);
      if (gridOpacitySeekbar == null) {
        break missingId;
      }

      id = R.id.grid_opacity_value;
      AppCompatTextView gridOpacityValue = ViewBindings.findChildViewById(rootView, id);
      if (gridOpacityValue == null) {
        break missingId;
      }

      id = R.id.horizontal_format_button;
      AppCompatButton horizontalFormatButton = ViewBindings.findChildViewById(rootView, id);
      if (horizontalFormatButton == null) {
        break missingId;
      }

      id = R.id.release_grid_button;
      AppCompatButton releaseGridButton = ViewBindings.findChildViewById(rootView, id);
      if (releaseGridButton == null) {
        break missingId;
      }

      id = R.id.square_format_button;
      AppCompatButton squareFormatButton = ViewBindings.findChildViewById(rootView, id);
      if (squareFormatButton == null) {
        break missingId;
      }

      id = R.id.vertical_format_button;
      AppCompatButton verticalFormatButton = ViewBindings.findChildViewById(rootView, id);
      if (verticalFormatButton == null) {
        break missingId;
      }

      return new GridPanelBinding((LinearLayout) rootView, fixGridButton, gridColorButton,
          gridLineThicknessSpinner, gridOpacitySeekbar, gridOpacityValue, horizontalFormatButton,
          releaseGridButton, squareFormatButton, verticalFormatButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
