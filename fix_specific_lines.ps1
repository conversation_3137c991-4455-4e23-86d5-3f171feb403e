# PowerShell script to fix specific lines in ARSessionManager.kt

# Read the file content
$content = Get-Content -Path "app/src/main/java/com/deace/deacear/ar/ARSessionManager.kt" -Raw

# Fix line 310
$content = $content -replace "val distance = calculateDistance\(hitPose, plane.centerPose\)", "val distance = DistanceUtils.calculateDistance(hitPose, plane.centerPose)"

# Fix line 352
$content = $content -replace "val distance = calculateDistance\(hitPose, plane.centerPose\)", "val distance = DistanceUtils.calculateDistance(hitPose, plane.centerPose)"

# Fix line 738
$content = $content -replace "val distance = calculateDistance\(hitPose, plane.centerPose\)", "val distance = DistanceUtils.calculateDistance(hitPose, plane.centerPose)"

# Fix line 1458
$content = $content -replace "val planeDistance = calculateDistance\(", "val planeDistance = DistanceUtils.calculateDistance("

# Fix line 2261
$content = $content -replace "val distance = calculateDistance\(", "val distance = DistanceUtils.calculateDistance("

# Write the modified content back to the file
$content | Set-Content -Path "app/src/main/java/com/deace/deacear/ar/ARSessionManager.kt"

Write-Host "Fixed specific lines in ARSessionManager.kt"
