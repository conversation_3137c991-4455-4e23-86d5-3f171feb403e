http://schemas.android.com/apk/res-auto;;${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/pulse.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/arrays.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_panel_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_grid_on_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_grid.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_grid_vertical.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_image_black_20.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/baseline_image_black_20.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/baseline_image_black_20.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/baseline_image_black_20.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/baseline_image_black_20.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/selected_button_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_export.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_image_24.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_grid_on_black_24.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/baseline_grid_on_black_24.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/baseline_grid_on_black_24.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/baseline_grid_on_black_24.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/baseline_grid_on_black_24.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_image.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_help.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_grid_on_black_48.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/baseline_grid_on_black_48.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/baseline_grid_on_black_48.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/baseline_grid_on_black_48.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/baseline_grid_on_black_48.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/toast_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/circle_shape.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_reticle.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_image_black_36.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/baseline_image_black_36.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/baseline_image_black_36.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/baseline_image_black_36.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/baseline_image_black_36.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_button_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/save_alt.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_image_black_18.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/baseline_image_black_18.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/baseline_image_black_18.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/baseline_image_black_18.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/baseline_image_black_18.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/deace_logo_sticker.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_grid_on_black_18.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/baseline_grid_on_black_18.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/baseline_grid_on_black_18.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/baseline_grid_on_black_18.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/baseline_grid_on_black_18.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_maximize.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/tracking_progress_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_grid_on_black_36.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/baseline_grid_on_black_36.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/baseline_grid_on_black_36.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/baseline_grid_on_black_36.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/baseline_grid_on_black_36.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_grid_horizontal.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/splash_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_image_20.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_minimize.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_grid_on_black_20.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/baseline_grid_on_black_20.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/baseline_grid_on_black_20.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/baseline_grid_on_black_20.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/baseline_grid_on_black_20.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_image_black_24.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/baseline_image_black_24.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/baseline_image_black_24.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/baseline_image_black_24.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/baseline_image_black_24.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_grid_square.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_image_black_48.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/baseline_image_black_48.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/baseline_image_black_48.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/baseline_image_black_48.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/baseline_image_black_48.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/config_panel.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/grid_panel.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/image_panel.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_tracking_help.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/export_panel.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/custom_toast.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher_round.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+anim:pulse,0,F;+array:line_thickness_options,1,V4000800e9,13000c017e,;Fine,Medium,Thick,;grid_formats,1,V400020039,13000600e1,;Vertical (9\:16),Square (1\:1),Horizontal (16\:9),;grid_formats_array,2,V4006b1763,13006f1811,;Vertical (9\:16),Square (1\:1),Horizontal (16\:9),;+color:colorPrimaryDark,3,V4000400aa,32000400d8,;"#3700B3";item_pressed,3,V400340986,30003409b2,;"#1AFFFFFF";panel_divider,3,V4003008bd,31003008ea,;"#33FFFFFF";gradient_start,3,V4002b07c8,3c002b0800,;"@color/colorPrimary";ar_grid_color,3,V4001b049a,31001b04c7,;"#4DFFFFFF";error,3,V4000e02aa,27000e02cd,;"#CF6679";ar_reticle_color,3,V4001d054d,32001d057b,;"#FF4081";transparent,3,V40023065d,2f00230688,;"#00000000";colorAccent,3,V4000500f3,2d0005011c,;"#03DAC6";notification_warning,3,V4002706fe,**********,;"#FFC107";ar_plane_color,3,V4001c04f3,32001c0521,;"#4D00E5FF";statusBarBackground,3,V400080166,45000801a7,;"@color/colorPrimaryDark";panel_background,3,V4002f0867,34002f0897,;"#CC1E1E1E";white,3,V400210607,290021062c,;"#FFFFFFFF";gradient_end,3,V4002c0806,39002c083b,;"@color/colorAccent";notification_error,3,V400280746,3400280776,;"#F44336";ar_anchor_color,3,V4001e0598,31001e05c5,;"#FFD600";colorPrimary,3,V400030065,2e0003008f,;"#6200EE";item_selected,3,V400330932,310033095f,;"#33FFFFFF";textSecondary,3,V400120338,2f00120363,;"#B3B3B3";surface,3,V4000d0267,29000d028c,;"#1E1E1E";notification_success,3,V4002606b6,36002606e8,;"#4CAF50";buttonDisabled,3,V400180441,300018046d,;"#424242";black,3,V400220632,2900220657,;"#FF000000";navigationBarBackground,3,V4000901ad,3e000901e7,;"@color/black";buttonNormal,3,V4001603bb,3a001603f1,;"@color/colorPrimary";background,3,V4000c0213,2c000c023b,;"#121212";textPrimary,3,V400110309,2d00110332,;"#FFFFFF";textHint,3,V400130369,2a0013038f,;"#666666";buttonPressed,3,V4001703f7,2f00170422,;"#7F3FD4";+dimen:main_menu_minimized_width,4,V4000500a3,38000500d7,;"32dp";+drawable:rounded_panel_background,5,F;baseline_grid_on_24,6,F;ic_grid,7,F;ic_grid_vertical,8,F;baseline_image_black_20,9,F;baseline_image_black_20,10,F;baseline_image_black_20,11,F;baseline_image_black_20,12,F;baseline_image_black_20,13,F;selected_button_background,14,F;ic_export,15,F;baseline_image_24,16,F;baseline_grid_on_black_24,17,F;baseline_grid_on_black_24,18,F;baseline_grid_on_black_24,19,F;baseline_grid_on_black_24,20,F;baseline_grid_on_black_24,21,F;ic_image,22,F;ic_help,23,F;baseline_grid_on_black_48,24,F;baseline_grid_on_black_48,25,F;baseline_grid_on_black_48,26,F;baseline_grid_on_black_48,27,F;baseline_grid_on_black_48,28,F;toast_background,29,F;circle_shape,30,F;ic_reticle,31,F;baseline_image_black_36,32,F;baseline_image_black_36,33,F;baseline_image_black_36,34,F;baseline_image_black_36,35,F;baseline_image_black_36,36,F;rounded_button_background,37,F;save_alt,38,F;baseline_image_black_18,39,F;baseline_image_black_18,40,F;baseline_image_black_18,41,F;baseline_image_black_18,42,F;baseline_image_black_18,43,F;deace_logo_sticker,44,F;baseline_grid_on_black_18,45,F;baseline_grid_on_black_18,46,F;baseline_grid_on_black_18,47,F;baseline_grid_on_black_18,48,F;baseline_grid_on_black_18,49,F;ic_maximize,50,F;tracking_progress_background,51,F;baseline_grid_on_black_36,52,F;baseline_grid_on_black_36,53,F;baseline_grid_on_black_36,54,F;baseline_grid_on_black_36,55,F;baseline_grid_on_black_36,56,F;ic_grid_horizontal,57,F;splash_background,58,F;ic_launcher_foreground,59,F;ic_settings,60,F;baseline_image_20,61,F;ic_minimize,62,F;baseline_grid_on_black_20,63,F;baseline_grid_on_black_20,64,F;baseline_grid_on_black_20,65,F;baseline_grid_on_black_20,66,F;baseline_grid_on_black_20,67,F;ic_launcher_background,68,F;baseline_image_black_24,69,F;baseline_image_black_24,70,F;baseline_image_black_24,71,F;baseline_image_black_24,72,F;baseline_image_black_24,73,F;ic_grid_square,74,F;baseline_image_black_48,75,F;baseline_image_black_48,76,F;baseline_image_black_48,77,F;baseline_image_black_48,78,F;baseline_image_black_48,79,F;+id:save_directory_text,80,F;release_grid_button,81,F;image_opacity_seekbar,82,F;image_opacity_seekbar,83,F;tracking_progress_bar,82,F;rotate_180_button,83,F;rotate_right_15_button,83,F;config_panel_scroll,82,F;normal_quality_radio,82,F;normal_quality_radio,80,F;image_panel_layout,82,F;reset_settings_button,82,F;reset_settings_button,80,F;btn_close_tracking_help,84,F;export_button,82,F;export_button,85,F;rotate_right_90_button,83,F;image_tab_button,82,F;default_grid_format_spinner,82,F;default_grid_format_spinner,80,F;grid_line_thickness_spinner,80,F;grid_line_thickness_spinner,81,F;grid_opacity_seekbar,82,F;grid_opacity_seekbar,81,F;deace_logo,82,F;show_planes_switch,82,F;image_quality_radio_group,82,F;image_quality_radio_group,80,F;save_directory_button,80,F;ar_surface_view,82,F;square_format_button,82,F;square_format_button,81,F;default_image_opacity_seekbar,80,F;filename_pattern_edittext,80,F;tracking_indicator_container,82,F;menu_icons_container,82,F;horizontal_format_button,82,F;horizontal_format_button,81,F;max_quality_radio,82,F;max_quality_radio,80,F;export_panel_layout,82,F;reticle,82,F;grid_color_button,80,F;grid_color_button,81,F;toast_text,86,F;flip_horizontal_button,83,F;confirm_reinit_button,82,F;image_selected_status,82,F;grid_panel_layout,82,F;tracking_quality_indicator,82,F;release_image_button,82,F;release_image_button,83,F;image_thumbnail,82,F;file_format_radio_group,82,F;file_format_radio_group,80,F;config_tab_button,82,F;load_image_button,82,F;load_image_button,83,F;quick_lock_image_button,82,F;main_menu_layout,82,F;image_size_text,82,F;image_size_text,83,F;help_button,82,F;fix_grid_button,82,F;fix_grid_button,81,F;image_opacity_value,82,F;image_opacity_value,83,F;jpg_format_radio,82,F;jpg_format_radio,80,F;rotate_left_90_button,83,F;config_panel_layout,82,F;rotate_left_15_button,83,F;grid_tab_button,82,F;fix_image_button,82,F;fix_image_button,83,F;loading_text,82,F;default_grid_opacity_seekbar,82,F;default_grid_opacity_seekbar,80,F;tracking_quality_text,82,F;export_tab_button,82,F;tracking_init_text,82,F;deace_panel_layout,82,F;png_format_radio,82,F;png_format_radio,80,F;loading_layout,82,F;vertical_format_button,82,F;vertical_format_button,81,F;reset_session_button,82,F;reset_session_button,80,F;grid_opacity_value,82,F;grid_opacity_value,81,F;preserve_aspect_switch,82,F;preserve_aspect_switch,80,F;preserve_aspect_switch,83,F;help_about_button,82,F;help_about_button,80,F;test_image_placement_button,82,F;test_image_placement_button,83,F;+layout:activity_main,82,F;image_panel,83,F;config_panel,80,F;dialog_tracking_help,84,F;custom_toast,86,F;export_panel,85,F;grid_panel,81,F;+mipmap:ic_launcher_round,87,F;ic_launcher_round,88,F;ic_launcher_round,89,F;ic_launcher_round,90,F;ic_launcher_round,91,F;ic_launcher_round,92,F;ic_launcher,93,F;ic_launcher,94,F;ic_launcher,95,F;ic_launcher,96,F;ic_launcher,97,F;ic_launcher,98,F;+string:cancel,2,V4003f0d8b,29003f0db0,;"Cancel";prompt_placeholder,2,V4001e05e8,4d001e0631,;"Describe what you want to bake";storage_permission_explanation,2,V4003c0bea,fb003c0ce1,;"To save images from the app\, we need permission to access your device storage.

After clicking \"GRANT PERMISSION\"\, please also approve the permission in the system dialog that will appear.";normal_quality,2,V4005a1395,39005a13ca,;"Normal Quality";tracking,2,V40072184a,2d00721873,;"Tracking";fix_image,2,V4002e088e,2f002e08b9,;"Fix Image";image_load_error,2,V400360a84,4400360ac4,;"Error loading image\: %s";reset_ar_session,2,V4004c1047,3d004c1080,;"Reset AR Session";image_tab,2,V400120413,2b0012043a,;"Image";grid_opacity,2,V4002807cf,3500280800,;"Grid Opacity";export_ar_view,2,V400390ae7,3900390b1c,;"Export AR View";storage_permission_required,2,V4003b0b83,65003b0be4,;"Storage permission is required to save images";failed_to_capture_image,2,V400400db6,4d00400dff,;"Failed to capture AR view";png,2,V4005e146b,23005e148a,;"PNG";export_error,2,V400450f14,4200450f52,;"Error exporting image\: %s";installing_arcore,2,V40005011b,3f00050156,;"Installing ARCore";tracking_found_surfaces,2,V400791a02,4500791a43,;"Surfaces detected";tracking_lost,2,V4007518df,2e00751909,;"Lost";exit,2,V4000c0346,25000c0367,;"Exit";confirm_reinit_app,2,V400190520,3a00190556,;"RESTART APP";ar_settings,2,V4004b1012,33004b1041,;"AR Settings";opacity_format,2,V4000d036d,2f000d0398,;"%d%%";grid_color,2,V400541239,3100541266,;"Grid Color";png_format,2,V4005f1490,31005f14bd,;"PNG Format";help_about,2,V40068166d,330068169c,;"Help / About";deace_tab,2,V40015049e,2b001504c5,;"Deace";default_opacity,2,V4005311fc,3b00531233,;"Default Opacity";tracking_ready,2,V4007a1a49,39007a1a7e,;"Tracking ready";tracking_help_good,2,V4007d1b32,55007d1b83,;"Good tracking - tap on a wall to place";results_placeholder,2,V4001f0637,46001f0679,;"Results will show here";filename_pattern,2,V40062151b,3d00621554,;"Filename Pattern";tracking_looking_for_surfaces,2,V4007819b1,4f007819fc,;"Looking for surfaces…";opacity_format_50,2,V4003309c1,31003309ee,;"50%";projected_image_options,2,V4005612a7,4b005612ee,;"Projected Image Options";fix_grid,2,V400260769,2d00260792,;"Fix Grid";image_saved_to,2,V400440ed4,3e00440f0e,;"Image saved to\:
%s";minimize_menu,2,V4001604cb,37001604fe,;"Minimize menu";preserve_aspect_ratio,2,V4003409f4,4700340a37,;"Preserve Aspect Ratio";camera_permission_required_message,2,V4000a0288,8d000a0311,;"This app requires camera permission to function. Please enable it in settings.";save_directory,2,V40063155a,390063158f,;"Save Directory";app_name,2,V400010040,3600010072,;"Graffiti AR Deace";help,2,V400801ba6,2500801bc7,;"Help";app_restarting,2,V4001a055c,3f001a0597,;"App is restarting...";export_instructions,2,V4003a0b22,5f003a0b7d,;"Exports the current AR view without UI elements";tracking_move_device,2,V400771959,56007719ab,;"Move your device slowly to scan walls";opacity_value,2,V4000e039e,2e000e03c8,;"%d%%";maximum,2,V4005b13d0,2b005b13f7,;"Maximum";show_planes,2,V4004e10bf,33004e10ee,;"Show Planes";max_quality,2,V4005c13fd,37005c1430,;"Maximum Quality";arcore_not_supported,2,V4000801ea,570008023d,;"ARCore is not supported on this device";config_tab,2,V40014046f,2d00140498,;"Config";horizontal,2,V40025072f,3800250763,;"Horizontal (16\:9)";requesting_permissions,2,V40007019c,4c000701e4,;"Requesting permissions...";tracking_help_limited,2,V4007c1adb,55007c1b2c,;"Continue moving to improve tracking";grid_format,2,V40022069a,2c002206c2,;"Grid";settings,2,V4000b0317,2d000b0340,;"Settings";no_image_loaded,2,V400310931,3b00310968,;"No image loaded";settings_reset,2,V400490fb4,3900490fe9,;"Settings Reset";reset_session,2,V4004d1086,37004d10b9,;"Reset Session";tracking_limited,2,V4007418a9,34007418d9,;"Limited";export_settings,2,V4005712f4,3b0057132b,;"Export Settings";release_grid,2,V400270798,35002707c9,;"Release Grid";tracking_initializing,2,V40076190f,4800761953,;"Initializing tracking…";baking_title,2,V4001d05ad,39001d05e2,;"Baking Assistant";choose_folder,2,V400641595,37006415c8,;"Choose Folder";square,2,V4002406fe,2f00240729,;"Square (1\:1)";export_success,2,V400430e96,3c00430ece,;"Export Successful";image_opacity,2,V4003008f8,370030092b,;"Image Opacity";default_format,2,V40051117c,39005111b1,;"Default Format";reset_settings,2,V400671632,3900671667,;"Reset Settings";load_image,2,V4002d085b,31002d0888,;"Load Image";release_image,2,V4002f08bf,37002f08f2,;"Release Image";vertical,2,V4002306c8,34002306f8,;"Vertical (9\:16)";default_grid_format,2,V4005211b7,43005211f6,;"Default Grid Format";general,2,V4006515ce,2b006515f5,;"General";visualize_detected_planes,2,V4004f10f4,4f004f113f,;"Visualize Detected Planes";storage_permission_denied,2,V4003d0ce7,63003d0d46,;"Cannot save images without storage permission";jpg_format,2,V4006114e8,3100611515,;"JPG Format";failed_to_load_image,2,V400350a3d,4500350a7e,;"Failed to load image";about_text,2,V4006916a2,bd0069175b,;"Graffiti AR Deace v0.1 2025. ©DEACE \: Author member of ADAGP. Authorization is required for any use of the works (www.adagp.fr). More on www.deace.com";ar_session_not_ready,2,V400410e05,4800410e49,;"AR session is not ready";failed_to_save_image,2,V400420e4f,4500420e90,;"Failed to save image";ok,2,V4004a0fef,21004a100c,;"OK";scanning_environment,2,V400040097,8200040115,DuplicateDefinition;"Point your device at a wall and move it slowly";file_format,2,V4005d1436,33005d1465,;"File Format";tracking_good,2,V400731879,2e007318a3,;"Good";jpg,2,V4006014c3,23006014e2,;"JPG";tracking_help_lost,2,V4007b1a84,55007b1ad5,;"Move device slowly to scan environment";normal,2,V40059136a,290059138f,;"Normal";choose_color,2,V400290806,3500290837,;"Choose Color";initializing_ar,2,V40006015c,3e00060196,;"Initializing AR...";grid_tab,2,V4001103e8,290011040d,;"Grid";app_settings,2,V4006615fb,350066162c,;"App Settings";grid_options,2,V400501145,3500501176,;"Grid Options";grant_permission,2,V4003e0d4c,3d003e0d85,;"Grant Permission";image_quality,2,V400581331,3700581364,;"Image Quality";permission_required,2,V400090243,4300090282,;"Permission Required";ar_session_reset,2,V400480f75,3d00480fae,;"AR Session Reset";image_size_format,2,V40032096e,51003209bb,;"Image size\: %d×%d";export_tab,2,V400130440,2d00130469,;"Export";line_thickness,2,V40055126c,39005512a1,;"Line Thickness";+style:WhiteSwitch,99,V4000a01e2,c000f0355,;DWidget.AppCompat.CompoundButton.Switch,colorControlActivated:@android\:color/white,android\:colorControlActivated:@android\:color/white,android\:thumbTint:@android\:color/white,android\:trackTint:@android\:color/white,;DialogButtonStyle,100,V4001b0506,c001d05a5,;DWidget.AppCompat.Button.ButtonBar.AlertDialog,android\:textColor:@color/white,;Theme.SplashScreen,100,V4000b01c6,c000e0296,;DTheme.DeaceAR,android\:windowBackground:@drawable/splash_background,android\:windowFullscreen:true,;Theme.DeaceAR.Dialog,100,V4001102b6,c00190500,;DTheme.AppCompat.Dialog.Alert,colorPrimary:@color/black,colorAccent:@color/black,buttonBarPositiveButtonStyle:@style/DialogButtonStyle,buttonBarNegativeButtonStyle:@style/DialogButtonStyle,buttonBarNeutralButtonStyle:@style/DialogButtonStyle,android\:textColorPrimary:@color/white,android\:background:@drawable/rounded_panel_background,;Theme.DeaceAR,100,V400020037,c0008019f,;DTheme.AppCompat.Light.NoActionBar,colorPrimary:@color/black,colorPrimaryDark:@color/black,colorAccent:@color/black,android\:windowBackground:@android\:color/white,;WhiteSeekBar,99,V400030077,c000701a0,;DWidget.AppCompat.SeekBar,android\:progressTint:@android\:color/white,android\:thumbTint:@android\:color/white,android\:progressBackgroundTint:@android\:color/darker_gray,;+xml:data_extraction_rules,101,F;backup_rules,102,F;