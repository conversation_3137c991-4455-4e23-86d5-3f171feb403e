<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.9.2">

    <issue
        id="CustomSplashScreen"
        severity="Warning"
        message="The application should not provide its own launch screen"
        category="Correctness"
        priority="5"
        summary="Application-defined Launch Screen"
        explanation="Starting in Android 12 (API 31+), the application&apos;s Launch Screen is provided by the system and the application should not create its own, otherwise the user will see two splashscreens. Please check the `SplashScreen` class to check how the Splash Screen can be controlled and customized."
        url="https://developer.android.com/guide/topics/ui/splash-screen"
        urls="https://developer.android.com/guide/topics/ui/splash-screen"
        errorLine1="class SplashActivity : AppCompatActivity() {"
        errorLine2="      ~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\SplashActivity.kt"
            line="9"
            column="7"/>
    </issue>

    <issue
        id="InflateParams"
        severity="Warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        category="Correctness"
        priority="5"
        summary="Layout Inflation without a Parent"
        explanation="When inflating a layout, avoid passing in null as the parent view, since otherwise any layout parameters on the root of the inflated layout will be ignored."
        url="https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/"
        urls="https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/"
        errorLine1="            val layout = inflater.inflate(R.layout.custom_toast, null)"
        errorLine2="                                                                 ~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\utils\MessageUtils.kt"
            line="27"
            column="66"/>
    </issue>

    <issue
        id="SelectedPhotoAccess"
        severity="Warning"
        message="Your app is currently not handling Selected Photos Access introduced in Android 14+"
        category="Correctness"
        priority="5"
        summary="Behavior change when requesting photo library access"
        explanation="Selected Photo Access is a new ability for users to share partial access to their photo library when apps request access to their device storage on Android 14+.&#xA;&#xA;Instead of letting the system manage the selection lifecycle, we recommend you adapt your app to handle partial access to the photo library."
        url="https://developer.android.com/about/versions/14/changes/partial-photo-video-access"
        urls="https://developer.android.com/about/versions/14/changes/partial-photo-video-access"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.READ_MEDIA_IMAGES&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\AndroidManifest.xml"
            line="11"
            column="36"/>
    </issue>

    <issue
        id="VectorRaster"
        severity="Warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more"
        category="Correctness"
        priority="5"
        summary="Vector Image Generation"
        explanation="Vector icons require API 21 or API 24 depending on used features, but when `minSdkVersion` is less than 21 or 24 and Android Gradle plugin 1.4 or higher is used, a vector drawable placed in the `drawable` folder is automatically moved to `drawable-anydpi-v21` or `drawable-anydpi-v24` and bitmap images are generated for different screen resolutions for backwards compatibility.&#xA;&#xA;However, there are some limitations to this raster image generation, and this lint check flags elements and attributes that are not fully supported. You should manually check whether the generated output is acceptable for those older devices."
        errorLine1="    android:width=&quot;240dp&quot;"
        errorLine2="                   ~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable\deace_logo_sticker.xml"
            line="3"
            column="20"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        severity="Warning"
        message="A newer version of com.android.application than 8.9.2 is available: 8.10.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.10.0 is difficult: 8.9.3)"
        category="Correctness"
        priority="4"
        summary="Obsolete Android Gradle Plugin Version"
        explanation="This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="agp = &quot;8.9.2&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        severity="Warning"
        message="A newer version of com.android.application than 8.9.2 is available: 8.10.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.10.0 is difficult: 8.9.3)"
        category="Correctness"
        priority="4"
        summary="Obsolete Android Gradle Plugin Version"
        explanation="This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="agp = &quot;8.9.2&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        severity="Warning"
        message="A newer version of com.android.application than 8.9.2 is available: 8.10.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.10.0 is difficult: 8.9.3)"
        category="Correctness"
        priority="4"
        summary="Obsolete Android Gradle Plugin Version"
        explanation="This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="agp = &quot;8.9.2&quot;"
        errorLine2="      ~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="2"
            column="7"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.android.material:material than 1.11.0 is available: 1.12.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation(&quot;com.google.android.material:material:1.11.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\build.gradle.kts"
            line="62"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="coreKtx = &quot;1.10.1&quot;"
        errorLine2="          ~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="4"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="coreKtx = &quot;1.10.1&quot;"
        errorLine2="          ~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="4"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="coreKtx = &quot;1.10.1&quot;"
        errorLine2="          ~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="4"
            column="11"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="junitVersion = &quot;1.1.5&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="6"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="junitVersion = &quot;1.1.5&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="6"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="junitVersion = &quot;1.1.5&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="6"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="espressoCore = &quot;3.5.1&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="7"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="espressoCore = &quot;3.5.1&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="7"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="espressoCore = &quot;3.5.1&quot;"
        errorLine2="               ~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="7"
            column="16"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="lifecycleRuntimeKtx = &quot;2.6.1&quot;"
        errorLine2="                      ~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="8"
            column="23"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="lifecycleRuntimeKtx = &quot;2.6.1&quot;"
        errorLine2="                      ~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="8"
            column="23"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="lifecycleRuntimeKtx = &quot;2.6.1&quot;"
        errorLine2="                      ~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="8"
            column="23"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.6.1 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="lifecycleViewmodelCompose = &quot;2.6.1&quot;"
        errorLine2="                            ~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="9"
            column="29"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.6.1 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="lifecycleViewmodelCompose = &quot;2.6.1&quot;"
        errorLine2="                            ~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="9"
            column="29"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.6.1 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="lifecycleViewmodelCompose = &quot;2.6.1&quot;"
        errorLine2="                            ~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="9"
            column="29"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="activityCompose = &quot;1.8.0&quot;"
        errorLine2="                  ~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="10"
            column="19"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="activityCompose = &quot;1.8.0&quot;"
        errorLine2="                  ~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="10"
            column="19"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="activityCompose = &quot;1.8.0&quot;"
        errorLine2="                  ~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="10"
            column="19"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.05.01"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="composeBom = &quot;2024.09.00&quot;"
        errorLine2="             ~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="11"
            column="14"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.05.01"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="composeBom = &quot;2024.09.00&quot;"
        errorLine2="             ~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="11"
            column="14"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.05.01"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="composeBom = &quot;2024.09.00&quot;"
        errorLine2="             ~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="11"
            column="14"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.ar:core than 1.48.0 is available: 1.49.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="core = &quot;1.48.0&quot;"
        errorLine2="       ~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="15"
            column="8"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.ar:core than 1.48.0 is available: 1.49.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="core = &quot;1.48.0&quot;"
        errorLine2="       ~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="15"
            column="8"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.ar:core than 1.48.0 is available: 1.49.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="core = &quot;1.48.0&quot;"
        errorLine2="       ~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\gradle\libs.versions.toml"
            line="15"
            column="8"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 29"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\ImagePanel.kt"
            line="327"
            column="13"/>
    </issue>

    <issue
        id="VectorPath"
        severity="Warning"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        category="Performance"
        priority="5"
        summary="Long vector paths"
        explanation="Using long vector paths is bad for performance. There are several ways to make the `pathData` shorter:&#xA;* Using less precision&#xA;* Removing some minor details&#xA;* Using the Android Studio vector conversion tool&#xA;* Rasterizing the image (converting to PNG)"
        errorLine1="      android:pathData=&quot;M19.14,12.94c0.04,-0.3 0.06,-0.61 0.06,-0.94c0,-0.32 -0.02,-0.64 -0.07,-0.94l2.03,-1.58c0.18,-0.14 0.23,-0.41 0.12,-0.61l-1.92,-3.32c-0.12,-0.22 -0.37,-0.29 -0.59,-0.22l-2.39,0.96c-0.5,-0.38 -1.03,-0.7 -1.62,-0.94L14.4,2.81c-0.04,-0.24 -0.24,-0.41 -0.48,-0.41h-3.84c-0.24,0 -0.43,0.17 -0.47,0.41L9.25,5.35C8.66,5.59 8.12,5.92 7.63,6.29L5.24,5.33c-0.22,-0.08 -0.47,0 -0.59,0.22L2.74,8.87C2.62,9.08 2.66,9.34 2.86,9.48l2.03,1.58C4.84,11.36 4.8,11.69 4.8,12s0.02,0.64 0.07,0.94l-2.03,1.58c-0.18,0.14 -0.23,0.41 -0.12,0.61l1.92,3.32c0.12,0.22 0.37,0.29 0.59,0.22l2.39,-0.96c0.5,0.38 1.03,0.7 1.62,0.94l0.36,2.54c0.05,0.24 0.24,0.41 0.48,0.41h3.84c0.24,0 0.44,-0.17 0.47,-0.41l0.36,-2.54c0.59,-0.24 1.13,-0.56 1.62,-0.94l2.39,0.96c0.22,0.08 0.47,0 0.59,-0.22l1.92,-3.32c0.12,-0.22 0.07,-0.47 -0.12,-0.61L19.14,12.94zM12,15.6c-1.98,0 -3.6,-1.62 -3.6,-3.6s1.62,-3.6 3.6,-3.6s3.6,1.62 3.6,3.6S13.98,15.6 12,15.6z&quot;/>"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable\ic_settings.xml"
            line="9"
            column="25"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.array.grid_formats` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string-array name=&quot;grid_formats&quot;>"
        errorLine2="                  ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\arrays.xml"
            line="3"
            column="19"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.array.line_thickness_options` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string-array name=&quot;line_thickness_options&quot;>"
        errorLine2="                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\arrays.xml"
            line="9"
            column="19"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.baseline_grid_on_24` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable\baseline_grid_on_24.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.baseline_grid_on_black_18` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_grid_on_black_18.png"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.baseline_grid_on_black_20` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_grid_on_black_20.png"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.baseline_grid_on_black_24` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_grid_on_black_24.png"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.baseline_grid_on_black_36` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_grid_on_black_36.png"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.baseline_grid_on_black_48` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_grid_on_black_48.png"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.baseline_image_20` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable\baseline_image_20.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.baseline_image_24` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable\baseline_image_24.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.baseline_image_black_18` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_image_black_18.png"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.baseline_image_black_20` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_image_black_20.png"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.baseline_image_black_24` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_image_black_24.png"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.baseline_image_black_36` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_image_black_36.png"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.baseline_image_black_48` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_image_black_48.png"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.colorPrimary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;colorPrimary&quot;>#6200EE&lt;/color>  &lt;!-- Purple 500 -->"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.colorPrimaryDark` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;colorPrimaryDark&quot;>#3700B3&lt;/color>  &lt;!-- Purple 700 -->"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="5"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.colorAccent` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;colorAccent&quot;>#03DAC6&lt;/color>  &lt;!-- Teal 200 -->"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="6"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.statusBarBackground` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;statusBarBackground&quot;>@color/colorPrimaryDark&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="9"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.navigationBarBackground` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;navigationBarBackground&quot;>@color/black&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="10"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;background&quot;>#121212&lt;/color>  &lt;!-- Dark background for AR view -->"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="13"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.surface` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;surface&quot;>#1E1E1E&lt;/color>  &lt;!-- Cards/dialogs -->"
        errorLine2="           ~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="14"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.error` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;error&quot;>#CF6679&lt;/color>  &lt;!-- Error color -->"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="15"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.textPrimary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;textPrimary&quot;>#FFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="18"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.textSecondary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;textSecondary&quot;>#B3B3B3&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="19"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.textHint` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;textHint&quot;>#666666&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="20"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.buttonNormal` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;buttonNormal&quot;>@color/colorPrimary&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="23"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.buttonPressed` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;buttonPressed&quot;>#7F3FD4&lt;/color>  &lt;!-- Lighter purple -->"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="24"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.buttonDisabled` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;buttonDisabled&quot;>#424242&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="25"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.ar_grid_color` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;ar_grid_color&quot;>#4DFFFFFF&lt;/color>  &lt;!-- White grid with 30% opacity -->"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="28"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.ar_plane_color` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;ar_plane_color&quot;>#4D00E5FF&lt;/color>  &lt;!-- Cyan plane with 30% opacity -->"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="29"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.ar_reticle_color` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;ar_reticle_color&quot;>#FF4081&lt;/color>  &lt;!-- Pink reticle -->"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="30"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.ar_anchor_color` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;ar_anchor_color&quot;>#FFD600&lt;/color>  &lt;!-- Yellow anchor -->"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="31"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.transparent` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;transparent&quot;>#00000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="36"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.notification_success` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;notification_success&quot;>#4CAF50&lt;/color>  &lt;!-- Green -->"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="39"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.notification_warning` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;notification_warning&quot;>#FFC107&lt;/color>  &lt;!-- Amber -->"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="40"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.notification_error` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;notification_error&quot;>#F44336&lt;/color>  &lt;!-- Red -->"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="41"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.gradient_start` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;gradient_start&quot;>@color/colorPrimary&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="44"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.gradient_end` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;gradient_end&quot;>@color/colorAccent&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="45"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.panel_background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;panel_background&quot;>#CC1E1E1E&lt;/color>  &lt;!-- Semi-transparent dark -->"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="48"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.panel_divider` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;panel_divider&quot;>#33FFFFFF&lt;/color>  &lt;!-- Very light divider -->"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="49"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.item_selected` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;item_selected&quot;>#33FFFFFF&lt;/color>  &lt;!-- White with 20% opacity -->"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="52"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.item_pressed` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;item_pressed&quot;>#1AFFFFFF&lt;/color>  &lt;!-- White with 10% opacity -->"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\colors.xml"
            line="53"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.layout.config_panel` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;ScrollView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\config_panel.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.main_menu_minimized_width` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;main_menu_minimized_width&quot;>32dp&lt;/dimen> &lt;!-- Adjusted to match new button size -->"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\dimens.xml"
            line="6"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.layout.export_panel` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;LinearLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\export_panel.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.layout.grid_panel` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;LinearLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\grid_panel.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_maximize` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable\ic_maximize.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_minimize` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable\ic_minimize.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.layout.image_panel` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;LinearLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\image_panel.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.save_alt` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;svg xmlns=&quot;http://www.w3.org/2000/svg&quot; height=&quot;24&quot; viewBox=&quot;0 0 24 24&quot; width=&quot;24&quot;>&lt;path d=&quot;M19 12v7H5v-7H3v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7h-2zm-6 .67l2.59-2.58L17 11.5l-5 5-5-5 1.41-1.41L11 12.67V3h2z&quot;/>&lt;path d=&quot;M0 0h24v24H0z&quot; fill=&quot;none&quot;/>&lt;/svg>"
        errorLine2="~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable\save_alt.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.opacity_value` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;opacity_value&quot;>%d%%&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="15"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.deace_tab` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;deace_tab&quot;>Deace&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="22"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.minimize_menu` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;minimize_menu&quot;>Minimize menu&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="23"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.baking_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;baking_title&quot;>Baking Assistant&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="30"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.prompt_placeholder` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;prompt_placeholder&quot;>Describe what you want to bake&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="31"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.results_placeholder` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;results_placeholder&quot;>Results will show here&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="32"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.release_grid` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;release_grid&quot;>Release Grid&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="40"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.choose_color` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;choose_color&quot;>Choose Color&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="42"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.image_opacity` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;image_opacity&quot;>Image Opacity&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="49"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.preserve_aspect_ratio` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;preserve_aspect_ratio&quot;>Preserve Aspect Ratio&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="53"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.export_instructions` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;export_instructions&quot;>Exports the current AR view without UI elements&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="59"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.reset_ar_session` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;reset_ar_session&quot;>Reset AR Session&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="77"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.visualize_detected_planes` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;visualize_detected_planes&quot;>Visualize Detected Planes&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="80"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.default_format` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;default_format&quot;>Default Format&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="82"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.grid_color` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;grid_color&quot;>Grid Color&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="85"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.line_thickness` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;line_thickness&quot;>Line Thickness&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="86"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.projected_image_options` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;projected_image_options&quot;>Projected Image Options&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="87"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.image_quality` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;image_quality&quot;>Image Quality&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="89"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.normal` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;normal&quot;>Normal&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="90"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.maximum` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;maximum&quot;>Maximum&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="92"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.file_format` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;file_format&quot;>File Format&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="94"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.png` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;png&quot;>PNG&lt;/string>"
        errorLine2="            ~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="95"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.jpg` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;jpg&quot;>JPG&lt;/string>"
        errorLine2="            ~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="97"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.filename_pattern` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;filename_pattern&quot;>Filename Pattern&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="99"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.save_directory` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;save_directory&quot;>Save Directory&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="100"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.choose_folder` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;choose_folder&quot;>Choose Folder&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="101"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.general` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;general&quot;>General&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="102"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.tracking` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;tracking&quot;>Tracking&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="115"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.tracking_good` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;tracking_good&quot;>Good&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="116"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.tracking_move_device` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;tracking_move_device&quot;>Move your device slowly to scan walls&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="120"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.tracking_ready` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;tracking_ready&quot;>Tracking ready&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="123"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.style.WhiteSwitch` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;style name=&quot;WhiteSwitch&quot; parent=&quot;Widget.AppCompat.CompoundButton.Switch&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\styles.xml"
            line="11"
            column="12"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        severity="Warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        category="Usability:Typography"
        priority="5"
        summary="Ellipsis string can be replaced with ellipsis character"
        explanation="You can replace the string &quot;...&quot; with a dedicated ellipsis character, ellipsis character (\u2026, &amp;#8230;). This can help make the text more readable."
        url="https://en.wikipedia.org/wiki/Ellipsis"
        urls="https://en.wikipedia.org/wiki/Ellipsis"
        errorLine1="    &lt;string name=&quot;initializing_ar&quot;>Initializing AR...&lt;/string>"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="7"
            column="36"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        severity="Warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        category="Usability:Typography"
        priority="5"
        summary="Ellipsis string can be replaced with ellipsis character"
        explanation="You can replace the string &quot;...&quot; with a dedicated ellipsis character, ellipsis character (\u2026, &amp;#8230;). This can help make the text more readable."
        url="https://en.wikipedia.org/wiki/Ellipsis"
        urls="https://en.wikipedia.org/wiki/Ellipsis"
        errorLine1="    &lt;string name=&quot;requesting_permissions&quot;>Requesting permissions...&lt;/string>"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="8"
            column="43"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        severity="Warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        category="Usability:Typography"
        priority="5"
        summary="Ellipsis string can be replaced with ellipsis character"
        explanation="You can replace the string &quot;...&quot; with a dedicated ellipsis character, ellipsis character (\u2026, &amp;#8230;). This can help make the text more readable."
        url="https://en.wikipedia.org/wiki/Ellipsis"
        urls="https://en.wikipedia.org/wiki/Ellipsis"
        errorLine1="    &lt;string name=&quot;app_restarting&quot;>App is restarting...&lt;/string>"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\values\strings.xml"
            line="27"
            column="35"/>
    </issue>

    <issue
        id="IconXmlAndPng"
        severity="Warning"
        message="The following images appear both as density independent `.xml` files and as bitmap files: src\main\res\mipmap-anydpi\ic_launcher.xml, src\main\res\mipmap-hdpi\ic_launcher.webp"
        category="Usability:Icons"
        priority="7"
        summary="Icon is specified both as `.xml` file and as a bitmap"
        explanation="If a drawable resource appears as an `.xml` file in the `drawable/` folder, it&apos;s usually not intentional for it to also appear as a bitmap using the same name; generally you expect the drawable XML file to define states and each state has a corresponding drawable bitmap.">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\mipmap-xhdpi\ic_launcher.webp"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\mipmap-mdpi\ic_launcher.webp"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\mipmap-hdpi\ic_launcher.webp"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\mipmap-anydpi\ic_launcher.xml"/>
    </issue>

    <issue
        id="IconXmlAndPng"
        severity="Warning"
        message="The following images appear both as density independent `.xml` files and as bitmap files: src\main\res\mipmap-anydpi\ic_launcher_round.xml, src\main\res\mipmap-hdpi\ic_launcher_round.webp"
        category="Usability:Icons"
        priority="7"
        summary="Icon is specified both as `.xml` file and as a bitmap"
        explanation="If a drawable resource appears as an `.xml` file in the `drawable/` folder, it&apos;s usually not intentional for it to also appear as a bitmap using the same name; generally you expect the drawable XML file to define states and each state has a corresponding drawable bitmap.">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml"/>
    </issue>

    <issue
        id="IconDuplicates"
        severity="Warning"
        message="The following unrelated icon files have identical contents: baseline_grid_on_black_24.png, baseline_grid_on_black_36.png, baseline_grid_on_black_18.png"
        category="Usability:Icons"
        priority="3"
        summary="Duplicated icons under different names"
        explanation="If an icon is repeated under different names, you can consolidate and just use one of the icons and delete the others to make your application smaller. However, duplicated icons usually are not intentional and can sometimes point to icons that were accidentally overwritten or accidentally not updated.">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_grid_on_black_18.png"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-mdpi\baseline_grid_on_black_36.png"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_grid_on_black_24.png"/>
    </issue>

    <issue
        id="IconDuplicates"
        severity="Warning"
        message="The following unrelated icon files have identical contents: baseline_grid_on_black_36.png, baseline_grid_on_black_18.png"
        category="Usability:Icons"
        priority="3"
        summary="Duplicated icons under different names"
        explanation="If an icon is repeated under different names, you can consolidate and just use one of the icons and delete the others to make your application smaller. However, duplicated icons usually are not intentional and can sometimes point to icons that were accidentally overwritten or accidentally not updated.">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxhdpi\baseline_grid_on_black_18.png"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_grid_on_black_36.png"/>
    </issue>

    <issue
        id="IconDuplicates"
        severity="Warning"
        message="The following unrelated icon files have identical contents: baseline_grid_on_black_48.png, baseline_grid_on_black_36.png, baseline_grid_on_black_24.png, baseline_grid_on_black_18.png"
        category="Usability:Icons"
        priority="3"
        summary="Duplicated icons under different names"
        explanation="If an icon is repeated under different names, you can consolidate and just use one of the icons and delete the others to make your application smaller. However, duplicated icons usually are not intentional and can sometimes point to icons that were accidentally overwritten or accidentally not updated.">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_grid_on_black_18.png"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxhdpi\baseline_grid_on_black_24.png"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_grid_on_black_36.png"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_grid_on_black_48.png"/>
    </issue>

    <issue
        id="IconDuplicates"
        severity="Warning"
        message="The following unrelated icon files have identical contents: baseline_grid_on_black_48.png, baseline_grid_on_black_24.png"
        category="Usability:Icons"
        priority="3"
        summary="Duplicated icons under different names"
        explanation="If an icon is repeated under different names, you can consolidate and just use one of the icons and delete the others to make your application smaller. However, duplicated icons usually are not intentional and can sometimes point to icons that were accidentally overwritten or accidentally not updated.">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_grid_on_black_24.png"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-mdpi\baseline_grid_on_black_48.png"/>
    </issue>

    <issue
        id="IconDuplicates"
        severity="Warning"
        message="The following unrelated icon files have identical contents: baseline_grid_on_black_48.png, baseline_grid_on_black_24.png"
        category="Usability:Icons"
        priority="3"
        summary="Duplicated icons under different names"
        explanation="If an icon is repeated under different names, you can consolidate and just use one of the icons and delete the others to make your application smaller. However, duplicated icons usually are not intentional and can sometimes point to icons that were accidentally overwritten or accidentally not updated.">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_grid_on_black_24.png"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_grid_on_black_48.png"/>
    </issue>

    <issue
        id="IconDuplicates"
        severity="Warning"
        message="The following unrelated icon files have identical contents: baseline_grid_on_black_48.png, baseline_grid_on_black_36.png"
        category="Usability:Icons"
        priority="3"
        summary="Duplicated icons under different names"
        explanation="If an icon is repeated under different names, you can consolidate and just use one of the icons and delete the others to make your application smaller. However, duplicated icons usually are not intentional and can sometimes point to icons that were accidentally overwritten or accidentally not updated.">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_grid_on_black_36.png"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxhdpi\baseline_grid_on_black_48.png"/>
    </issue>

    <issue
        id="IconDuplicates"
        severity="Warning"
        message="The following unrelated icon files have identical contents: baseline_image_black_24.png, baseline_image_black_36.png, baseline_image_black_18.png"
        category="Usability:Icons"
        priority="3"
        summary="Duplicated icons under different names"
        explanation="If an icon is repeated under different names, you can consolidate and just use one of the icons and delete the others to make your application smaller. However, duplicated icons usually are not intentional and can sometimes point to icons that were accidentally overwritten or accidentally not updated.">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_image_black_18.png"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-mdpi\baseline_image_black_36.png"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_image_black_24.png"/>
    </issue>

    <issue
        id="IconDuplicates"
        severity="Warning"
        message="The following unrelated icon files have identical contents: baseline_image_black_36.png, baseline_image_black_18.png"
        category="Usability:Icons"
        priority="3"
        summary="Duplicated icons under different names"
        explanation="If an icon is repeated under different names, you can consolidate and just use one of the icons and delete the others to make your application smaller. However, duplicated icons usually are not intentional and can sometimes point to icons that were accidentally overwritten or accidentally not updated.">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxhdpi\baseline_image_black_18.png"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_image_black_36.png"/>
    </issue>

    <issue
        id="IconDuplicates"
        severity="Warning"
        message="The following unrelated icon files have identical contents: baseline_image_black_48.png, baseline_image_black_36.png, baseline_image_black_24.png, baseline_image_black_18.png"
        category="Usability:Icons"
        priority="3"
        summary="Duplicated icons under different names"
        explanation="If an icon is repeated under different names, you can consolidate and just use one of the icons and delete the others to make your application smaller. However, duplicated icons usually are not intentional and can sometimes point to icons that were accidentally overwritten or accidentally not updated.">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_image_black_18.png"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxhdpi\baseline_image_black_24.png"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_image_black_36.png"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-hdpi\baseline_image_black_48.png"/>
    </issue>

    <issue
        id="IconDuplicates"
        severity="Warning"
        message="The following unrelated icon files have identical contents: baseline_image_black_48.png, baseline_image_black_24.png"
        category="Usability:Icons"
        priority="3"
        summary="Duplicated icons under different names"
        explanation="If an icon is repeated under different names, you can consolidate and just use one of the icons and delete the others to make your application smaller. However, duplicated icons usually are not intentional and can sometimes point to icons that were accidentally overwritten or accidentally not updated.">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_image_black_24.png"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-mdpi\baseline_image_black_48.png"/>
    </issue>

    <issue
        id="IconDuplicates"
        severity="Warning"
        message="The following unrelated icon files have identical contents: baseline_image_black_48.png, baseline_image_black_24.png"
        category="Usability:Icons"
        priority="3"
        summary="Duplicated icons under different names"
        explanation="If an icon is repeated under different names, you can consolidate and just use one of the icons and delete the others to make your application smaller. However, duplicated icons usually are not intentional and can sometimes point to icons that were accidentally overwritten or accidentally not updated.">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_image_black_24.png"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xhdpi\baseline_image_black_48.png"/>
    </issue>

    <issue
        id="IconDuplicates"
        severity="Warning"
        message="The following unrelated icon files have identical contents: baseline_image_black_48.png, baseline_image_black_36.png"
        category="Usability:Icons"
        priority="3"
        summary="Duplicated icons under different names"
        explanation="If an icon is repeated under different names, you can consolidate and just use one of the icons and delete the others to make your application smaller. However, duplicated icons usually are not intentional and can sometimes point to icons that were accidentally overwritten or accidentally not updated.">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxxhdpi\baseline_image_black_36.png"/>
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\drawable-xxhdpi\baseline_image_black_48.png"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="            &lt;Button"
        errorLine2="             ~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\config_panel.xml"
            line="355"
            column="14"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="            &lt;Button"
        errorLine2="             ~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\config_panel.xml"
            line="362"
            column="14"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX function `createBitmap` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="            val bitmap = Bitmap.createBitmap(screenWidth, screenHeight, Bitmap.Config.ARGB_8888)"
        errorLine2="                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ar\ARRenderer.kt"
            line="502"
            column="26"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX function `createBitmap` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="            val flippedBitmap = Bitmap.createBitmap("
        errorLine2="                                ^">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ar\ARRenderer.kt"
            line="525"
            column="33"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `Bitmap.get` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                    flippedBitmap.setPixel(x, bitmap.height - y - 1, bitmap.getPixel(x, y))"
        errorLine2="                                                                     ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ar\ARRenderer.kt"
            line="531"
            column="70"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `Bitmap.set` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                    flippedBitmap.setPixel(x, bitmap.height - y - 1, bitmap.getPixel(x, y))"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ar\ARRenderer.kt"
            line="531"
            column="21"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `Bitmap.scale` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        val scaledBitmap = Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, true)"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ar\ARSessionManager.kt"
            line="2127"
            column="28"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension property `View.isVisible` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        return scrollView.visibility == View.VISIBLE"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\ConfigPanel.kt"
            line="260"
            column="16"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension property `View.isVisible` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        return panelLayout.visibility == View.VISIBLE"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\DeacePanel.kt"
            line="105"
            column="16"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension property `View.isVisible` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        return panelLayout.visibility == View.VISIBLE"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\ExportPanel.kt"
            line="229"
            column="16"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension property `View.isVisible` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        return panelLayout.visibility == View.VISIBLE"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\GridPanel.kt"
            line="339"
            column="16"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `Bitmap.scale` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                        val scaledBitmap = Bitmap.createScaledBitmap(loadedBitmap, thumbnailSize, thumbnailSize, true)"
        errorLine2="                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\ImagePanel.kt"
            line="571"
            column="44"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension property `View.isVisible` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        return panelLayout.visibility == View.VISIBLE"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\ImagePanel.kt"
            line="750"
            column="16"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension property `View.isVisible` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                Log.d(&quot;ImageDebug&quot;, &quot;Button state: enabled=${loadButton.isEnabled}, visible=${loadButton.visibility == View.VISIBLE}&quot;)"
        errorLine2="                                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\MainActivity.kt"
            line="1762"
            column="95"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension property `View.isVisible` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="            } else if (!shouldShow &amp;&amp; binding.quickLockImageButton.visibility == View.VISIBLE) {"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\MainActivity.kt"
            line="1884"
            column="39"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation(&quot;com.google.android.material:material:1.11.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\build.gradle.kts"
            line="62"
            column="20"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        severity="Warning"
        message="Custom view ``GLSurfaceView`` has `setOnTouchListener` called on it but does not override `performClick`"
        category="Accessibility"
        priority="6"
        summary="Accessibility in Custom Views"
        explanation="If a `View` that overrides `onTouchEvent` or uses an `OnTouchListener` does not also implement `performClick` and call it when clicks are detected, the `View` may not handle accessibility actions properly. Logic handling the click actions should ideally be placed in `View#performClick` as some accessibility services invoke `performClick` when a click action should occur."
        errorLine1="        binding.arSurfaceView.setOnTouchListener { _, event ->"
        errorLine2="        ^">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\MainActivity.kt"
            line="1174"
            column="9"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        severity="Warning"
        message="`onTouch` lambda should call `View#performClick` when a click is detected"
        category="Accessibility"
        priority="6"
        summary="Accessibility in Custom Views"
        explanation="If a `View` that overrides `onTouchEvent` or uses an `OnTouchListener` does not also implement `performClick` and call it when clicks are detected, the `View` may not handle accessibility actions properly. Logic handling the click actions should ideally be placed in `View#performClick` as some accessibility services invoke `performClick` when a click action should occur."
        errorLine1="        binding.arSurfaceView.setOnTouchListener { _, event ->"
        errorLine2="                                                 ^">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\MainActivity.kt"
            line="1174"
            column="50"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            opacityValueText.text = &quot;$initialProgress%&quot;"
        errorLine2="                                    ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\GridPanel.kt"
            line="83"
            column="37"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                opacityValueText.text = &quot;$progress%&quot;"
        errorLine2="                                        ~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\GridPanel.kt"
            line="157"
            column="41"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            opacityValueText.text = &quot;$currentProgress%&quot;"
        errorLine2="                                    ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\GridPanel.kt"
            line="269"
            column="37"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            opacityValueText.text = &quot;${opacitySeekBar.progress}%&quot;"
        errorLine2="                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\GridPanel.kt"
            line="275"
            column="37"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                imageSelectedStatus.text = &quot;No image selected&quot;"
        errorLine2="                                            ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\ImagePanel.kt"
            line="132"
            column="45"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            imageSelectedStatus.text = &quot;No image selected&quot;"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\java\com\deace\deacear\ui\ImagePanel.kt"
            line="768"
            column="41"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;50%&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;50%&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml"
            line="202"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;No image selected&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;No image selected&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml"
            line="250"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;TEST: Place&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;TEST: Place&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml"
            line="279"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Lock Image&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Lock Image&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml"
            line="662"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Graffiti_AR_{NNN}&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:hint=&quot;Graffiti_AR_{NNN}&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\config_panel.xml"
            line="303"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;/Pictures/Graffiti_AR/&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;/Pictures/Graffiti_AR/&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\config_panel.xml"
            line="325"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Tracking Help&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Tracking Help&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Red: Tracking lost. Move your device slowly to scan the environment.&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;Red: Tracking lost. Move your device slowly to scan the environment.&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml"
            line="34"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Yellow: Limited tracking. Continue moving to improve tracking quality.&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;Yellow: Limited tracking. Continue moving to improve tracking quality.&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml"
            line="55"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Green: Good tracking. Tap on a wall to place content.&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;Green: Good tracking. Tap on a wall to place content.&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml"
            line="76"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Tips for Detecting Vertical Walls:&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Tips for Detecting Vertical Walls:&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml"
            line="85"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;• Move your device VERY SLOWLY in a figure-8 pattern\n• Point camera directly at walls (not at an angle)\n• Scan textured walls with patterns or objects\n• Avoid plain white or reflective surfaces\n• Keep 0.5-1.5m distance from walls\n• Ensure bright, even lighting\n• Make sure camera lens is clean\n• Try different walls if one isn&apos;t detected&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;• Move your device VERY SLOWLY in a figure-8 pattern\n• Point camera directly at walls (not at an angle)\n• Scan textured walls with patterns or objects\n• Avoid plain white or reflective surfaces\n• Keep 0.5-1.5m distance from walls\n• Ensure bright, even lighting\n• Make sure camera lens is clean\n• Try different walls if one isn&apos;t detected&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml"
            line="94"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Got it&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Got it&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml"
            line="103"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;TEST: Place&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;TEST: Place&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\image_panel.xml"
            line="48"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Image Rotation&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;Image Rotation&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\image_panel.xml"
            line="119"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;↺ 90°&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;↺ 90°&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\image_panel.xml"
            line="136"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;↻ 90°&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;↻ 90°&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\image_panel.xml"
            line="149"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;180°&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;180°&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\image_panel.xml"
            line="161"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;↺ 15°&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;↺ 15°&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\image_panel.xml"
            line="182"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;↻ 15°&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;↻ 15°&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\image_panel.xml"
            line="195"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Flip H&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;Flip H&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\image_panel.xml"
            line="207"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_alignParentLeft` with `android:layout_alignParentStart=&quot;true&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:layout_alignParentLeft=&quot;true&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml"
            line="42"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_toRightOf` with `android:layout_toEndOf=&quot;@id/main_menu_layout&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:layout_toRightOf=&quot;@id/main_menu_layout&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml"
            line="122"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_toRightOf` with `android:layout_toEndOf=&quot;@id/main_menu_layout&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:layout_toRightOf=&quot;@id/main_menu_layout&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml"
            line="213"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_toRightOf` with `android:layout_toEndOf=&quot;@id/main_menu_layout&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:layout_toRightOf=&quot;@id/main_menu_layout&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml"
            line="331"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_toRightOf` with `android:layout_toEndOf=&quot;@id/main_menu_layout&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:layout_toRightOf=&quot;@id/main_menu_layout&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml"
            line="358"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_alignParentLeft` with `android:layout_alignParentStart=&quot;true&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:layout_alignParentLeft=&quot;true&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml"
            line="577"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;8dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="                android:layout_marginLeft=&quot;8dp&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml"
            line="604"
            column="17"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_alignParentRight` with `android:layout_alignParentEnd=&quot;true&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:layout_alignParentRight=&quot;true&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml"
            line="644"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_toLeftOf` with `android:layout_toStartOf=&quot;@id/help_button&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:layout_toLeftOf=&quot;@id/help_button&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml"
            line="657"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_marginRight` with `android:layout_marginEnd=&quot;16dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="        android:layout_marginRight=&quot;16dp&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\activity_main.xml"
            line="659"
            column="9"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;8dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="            android:layout_marginLeft=&quot;8dp&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml"
            line="37"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;8dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="            android:layout_marginLeft=&quot;8dp&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml"
            line="58"
            column="13"/>
    </issue>

    <issue
        id="RtlHardcoded"
        severity="Warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;8dp&quot;` to better support right-to-left layouts"
        category="Internationalization:Bidirectional Text"
        priority="5"
        summary="Using left/right instead of start/end attributes"
        explanation="Using `Gravity#LEFT` and `Gravity#RIGHT` can lead to problems when a layout is rendered in locales where text flows from right to left. Use `Gravity#START` and `Gravity#END` instead. Similarly, in XML `gravity` and `layout_gravity` attributes, use `start` rather than `left`.&#xA;&#xA;For XML attributes such as paddingLeft and `layout_marginLeft`, use `paddingStart` and `layout_marginStart`. **NOTE**: If your `minSdkVersion` is less than 17, you should add **both** the older left/right attributes **as well as** the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.&#xA;&#xA;(Note: For `Gravity#LEFT` and `Gravity#START`, you can use these constants even when targeting older platforms, because the `start` bitmask is a superset of the `left` bitmask. Therefore, you can use `gravity=&quot;start&quot;` rather than `gravity=&quot;left|start&quot;`.)"
        errorLine1="            android:layout_marginLeft=&quot;8dp&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\_DEV\DeaceAR\app\src\main\res\layout\dialog_tracking_help.xml"
            line="79"
            column="13"/>
    </issue>

</issues>
