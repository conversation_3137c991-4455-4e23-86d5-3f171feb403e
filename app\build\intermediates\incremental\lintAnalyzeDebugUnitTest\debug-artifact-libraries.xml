<libraries>
  <library
      name=":@@:app::debug"
      project=":app"/>
  <library
      name="androidx.databinding:viewbinding:8.9.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e39128d7bd0e3eac4c081a71e59aa52d\transformed\viewbinding-8.9.2\jars\classes.jar"
      resolved="androidx.databinding:viewbinding:8.9.2"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e39128d7bd0e3eac4c081a71e59aa52d\transformed\viewbinding-8.9.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.11.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62494de1b82b6a4133faf551b79540cf\transformed\material-1.11.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.11.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62494de1b82b6a4133faf551b79540cf\transformed\material-1.11.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e73d3ad1bdb44ead29d81c4af7ca7cb3\transformed\appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e73d3ad1bdb44ead29d81c4af7ca7cb3\transformed\appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ede64893417e8f3717030de8a2fb59c\transformed\constraintlayout-2.0.1\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ede64893417e8f3717030de8a2fb59c\transformed\constraintlayout-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13ac21140bd1ac37ddb6065c0cefbb98\transformed\appcompat-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\13ac21140bd1ac37ddb6065c0cefbb98\transformed\appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25d651fae6e07668680b3e34d76bf818\transformed\viewpager2-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\25d651fae6e07668680b3e34d76bf818\transformed\viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9105e0cf63ea7790dcb4e1306d5286\transformed\fragment-1.5.4\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9105e0cf63ea7790dcb4e1306d5286\transformed\fragment-1.5.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1eed5f4811a242e5a07b9ebc697e49e6\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1eed5f4811a242e5a07b9ebc697e49e6\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9392d28b8c4054011aa1877b252e835\transformed\coordinatorlayout-1.1.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9392d28b8c4054011aa1877b252e835\transformed\coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1ad13e20a9891ed372536de6516e627\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d1ad13e20a9891ed372536de6516e627\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\931beaad1c7549a4b8e425c9e524b37c\transformed\recyclerview-1.1.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\931beaad1c7549a4b8e425c9e524b37c\transformed\recyclerview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5587ebedf624f4cca69b8639b9bf6be6\transformed\transition-1.2.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5587ebedf624f4cca69b8639b9bf6be6\transformed\transition-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82043049c60ce058469ac205d7041fa2\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82043049c60ce058469ac205d7041fa2\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e90fe16cb6d00093149cdcc339954cae\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e90fe16cb6d00093149cdcc339954cae\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ceb3bd60058000323c695a13e5ac629\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ceb3bd60058000323c695a13e5ac629\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb25e4e4df109a55781ba65e9caaab63\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb25e4e4df109a55781ba65e9caaab63\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\582738024d2f275f7bcf0b95268c3407\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\582738024d2f275f7bcf0b95268c3407\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcbe775112d5b3fc0b893f7a21dbfbe8\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcbe775112d5b3fc0b893f7a21dbfbe8\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38d3a933af6ee7c8873273933d358b25\transformed\core-1.13.1\jars\classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38d3a933af6ee7c8873273933d358b25\transformed\core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d121bd0e3758b97aca4eb7f27431f7d\transformed\lifecycle-livedata-core-ktx-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d121bd0e3758b97aca4eb7f27431f7d\transformed\lifecycle-livedata-core-ktx-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5c5b3a55b9cb40d648da79388fa66f3\transformed\lifecycle-livedata-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5c5b3a55b9cb40d648da79388fa66f3\transformed\lifecycle-livedata-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72dd89e55d146269ce714b9a0904c008\transformed\lifecycle-runtime-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72dd89e55d146269ce714b9a0904c008\transformed\lifecycle-runtime-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.8.3\7174a594afb73a9ad9ac9074ce78b94af3cc52a7\lifecycle-common-jvm-2.8.3.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.8.3"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc6788774f2825a10d9c6e2066906704\transformed\lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc6788774f2825a10d9c6e2066906704\transformed\lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\794be9a8aa00c898e57fe7a0d3af5d20\transformed\lifecycle-livedata-core-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\794be9a8aa00c898e57fe7a0d3af5d20\transformed\lifecycle-livedata-core-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d6547db1fd0f5cf804cc78f751d803d\transformed\lifecycle-viewmodel-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d6547db1fd0f5cf804cc78f751d803d\transformed\lifecycle-viewmodel-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0d457ebac1a0262024e1620c4770c36\transformed\lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0d457ebac1a0262024e1620c4770c36\transformed\lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94b14353c20374702245d4b52fc0ba87\transformed\lifecycle-viewmodel-ktx-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94b14353c20374702245d4b52fc0ba87\transformed\lifecycle-viewmodel-ktx-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f7c29ecfb2011a33158785227393cdb\transformed\lifecycle-viewmodel-savedstate-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f7c29ecfb2011a33158785227393cdb\transformed\lifecycle-viewmodel-savedstate-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b686dbbd33a61a0d6388eba0222a073\transformed\lifecycle-runtime-ktx-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b686dbbd33a61a0d6388eba0222a073\transformed\lifecycle-runtime-ktx-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8d543842df9db3596bcb286760fbb56\transformed\lifecycle-viewmodel-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8d543842df9db3596bcb286760fbb56\transformed\lifecycle-viewmodel-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material3:material3-android:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\639fc7211423cf0ea25f09e1d5e9e363\transformed\material3-release\jars\classes.jar"
      resolved="androidx.compose.material3:material3-android:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\639fc7211423cf0ea25f09e1d5e9e363\transformed\material3-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f2c2ccd34f2995b6d6132661c5f8c0b\transformed\ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f2c2ccd34f2995b6d6132661c5f8c0b\transformed\ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f02b88921013cebaad5ea25c13447fd\transformed\ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f02b88921013cebaad5ea25c13447fd\transformed\ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ab77110f73c70910916b7114de7afc2\transformed\ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ab77110f73c70910916b7114de7afc2\transformed\ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81ff990f43fe7bad7e60551ce6f07d31\transformed\foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81ff990f43fe7bad7e60551ce6f07d31\transformed\foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10c2c0ca83342cff91682885c52993a9\transformed\material-ripple-release\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10c2c0ca83342cff91682885c52993a9\transformed\material-ripple-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506ee9a002923a3d39e68c9961476d91\transformed\foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506ee9a002923a3d39e68c9961476d91\transformed\foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ebf487fc670220d9d2a6b6213636767\transformed\animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ebf487fc670220d9d2a6b6213636767\transformed\animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61c8e6d09b9804995ff7cf8e165436e\transformed\animation-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61c8e6d09b9804995ff7cf8e165436e\transformed\animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b7b7aa7a052b5d0e5275e92ca7f23b1\transformed\ui-geometry-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b7b7aa7a052b5d0e5275e92ca7f23b1\transformed\ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-data-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d142b9daa022a1884c52cc2d61b7e3f6\transformed\ui-tooling-data-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-data-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d142b9daa022a1884c52cc2d61b7e3f6\transformed\ui-tooling-data-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bcd01d85c785df397624298de5bd11c\transformed\ui-tooling-preview-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bcd01d85c785df397624298de5bd11c\transformed\ui-tooling-preview-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d22a1ae198a5cd25c196042d29e5d34a\transformed\ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d22a1ae198a5cd25c196042d29e5d34a\transformed\ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2730e0e22ca1704411a3dbc0fdd01c99\transformed\material-icons-core-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2730e0e22ca1704411a3dbc0fdd01c99\transformed\material-icons-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2d97a54756ab03a521f7c01e4fafab2\transformed\ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2d97a54756ab03a521f7c01e4fafab2\transformed\ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1c2c4edfb41ee161dcd1248ce713ab0\transformed\ui-tooling-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1c2c4edfb41ee161dcd1248ce713ab0\transformed\ui-tooling-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-test-manifest:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\678ddbea4e488a705d3a59ea742b9914\transformed\ui-test-manifest-1.7.0\jars\classes.jar"
      resolved="androidx.compose.ui:ui-test-manifest:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\678ddbea4e488a705d3a59ea742b9914\transformed\ui-test-manifest-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53a9669848583e22915a2ba78d3ad0cf\transformed\activity-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53a9669848583e22915a2ba78d3ad0cf\transformed\activity-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c515dd852effcca4eadd8a84bd257b3c\transformed\activity-compose-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-compose:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c515dd852effcca4eadd8a84bd257b3c\transformed\activity-compose-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b14fb249cab85f948a63ba7c031ca8d7\transformed\activity-ktx-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b14fb249cab85f948a63ba7c031ca8d7\transformed\activity-ktx-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ca0f9e68e6fcd94766029a20e487c4c\transformed\core-ktx-1.13.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ca0f9e68e6fcd94766029a20e487c4c\transformed\core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.ai.client.generativeai:generativeai:0.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3c36383787c59533e49319a11bb550a\transformed\generativeai-0.9.0\jars\classes.jar"
      resolved="com.google.ai.client.generativeai:generativeai:0.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3c36383787c59533e49319a11bb550a\transformed\generativeai-0.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\013cb4abc6eecae1937cd2a5760cda78\transformed\runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\013cb4abc6eecae1937cd2a5760cda78\transformed\runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7bfaa45e6172fd6bd8231714da01000\transformed\runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7bfaa45e6172fd6bd8231714da01000\transformed\runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.22\b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1\kotlin-stdlib-jdk8-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5de9457de7f0a054f839b729e22b1b4\transformed\savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5de9457de7f0a054f839b729e22b1b4\transformed\savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\490ecf9bcd6ee4fb8be9ce0166995cd3\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\490ecf9bcd6ee4fb8be9ce0166995cd3\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1707d1090b91231a2137bbd3f92d2273\transformed\annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1707d1090b91231a2137bbd3f92d2273\transformed\annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.ar:core:1.48.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d3a9e4a1ac0a7dfaa2a05d1d85bb1b7\transformed\core-1.48.0\jars\classes.jar"
      resolved="com.google.ar:core:1.48.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d3a9e4a1ac0a7dfaa2a05d1d85bb1b7\transformed\core-1.48.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d761927f8744d8bcfbf34b217523f8d\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d761927f8744d8bcfbf34b217523f8d\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\101f8a88b8b051bff325836b59fd2907\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\101f8a88b8b051bff325836b59fd2907\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a08c7e78304da0f0a6fbc87649b73d5\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a08c7e78304da0f0a6fbc87649b73d5\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28514dc793006c208cc3bb601c860929\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28514dc793006c208cc3bb601c860929\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-jvm:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.0\e209fb7bd1183032f55a0408121c6251a81acb49\collection-jvm-1.4.0.jar"
      resolved="androidx.collection:collection-jvm:1.4.0"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7a98c5c3870aefc2362092bebfdd74a\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d7a98c5c3870aefc2362092bebfdd74a\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d91fee69f7bf12d9dc21f7e6f8321c92\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d91fee69f7bf12d9dc21f7e6f8321c92\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34750184aea0b58849847527b7a1dee0\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34750184aea0b58849847527b7a1dee0\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49be89a05ca22eeadc49f0d3a5f9f92f\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49be89a05ca22eeadc49f0d3a5f9f92f\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.8.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.8.0\b8a16fe526014b7941c1debaccaf9c5153692dbb\annotation-jvm-1.8.0.jar"
      resolved="androidx.annotation:annotation-jvm:1.8.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.8.22\4dabb8248310d833bb6a8b516024a91fd3d275c\kotlin-stdlib-jdk7-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.21\618b539767b4899b4660a83006e052b63f1db551\kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"/>
  <library
      name="junit:junit:4.13.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\junit\junit\4.13.2\8ac9e16d933b6fb43bc7f576336b8f4d7eb5ba12\junit-4.13.2.jar"
      resolved="junit:junit:4.13.2"/>
  <library
      name="org.hamcrest:hamcrest-core:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-core\1.3\42a25dc3219429f0e5d060061f71acb49bf010a0\hamcrest-core-1.3.jar"
      resolved="org.hamcrest:hamcrest-core:1.3"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-solver\2.0.1\30988fe2d77f3fe3bf7551bb8a8b795fad7e7226\constraintlayout-solver-2.0.1.jar"
      resolved="androidx.constraintlayout:constraintlayout-solver:2.0.1"/>
  <library
      name="com.google.ai.client.generativeai:common:0.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\150a4091118bf4f3bd8d956444cd51cf\transformed\common-0.8.0\jars\classes.jar"
      resolved="com.google.ai.client.generativeai:common:0.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\150a4091118bf4f3bd8d956444cd51cf\transformed\common-0.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-android:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54fd0642d73366eb73188f705ca46903\transformed\material-release\jars\classes.jar"
      resolved="androidx.compose.material:material-android:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54fd0642d73366eb73188f705ca46903\transformed\material-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2eef37407c22c432e04cb71b42bf29a\transformed\emoji2-views-helper-1.3.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2eef37407c22c432e04cb71b42bf29a\transformed\emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\340e93631b2af3e5164b76efc0a657e6\transformed\emoji2-1.3.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.11.1\transforms\340e93631b2af3e5164b76efc0a657e6\transformed\emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\340e93631b2af3e5164b76efc0a657e6\transformed\emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db05ef9d9485614a0a6bc4fc03852620\transformed\autofill-1.0.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db05ef9d9485614a0a6bc4fc03852620\transformed\autofill-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.graphics:graphics-path:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5409cbc8b5ac43f7c20102e74453e383\transformed\graphics-path-1.0.1\jars\classes.jar"
      resolved="androidx.graphics:graphics-path:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5409cbc8b5ac43f7c20102e74453e383\transformed\graphics-path-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1835bce47598e616fdab1ac36a9d9633\transformed\customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1835bce47598e616fdab1ac36a9d9633\transformed\customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.8.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e90c633f1537cb1e5a77ac30442fd6b0\transformed\lifecycle-process-2.8.3\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.8.3"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e90c633f1537cb1e5a77ac30442fd6b0\transformed\lifecycle-process-2.8.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.8.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.8.3\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.8.3.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.8.3"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2b545182f8c2b3200dae13aeac0d959\transformed\profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.2.0-alpha03@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.2.0-alpha03\719a071a5b713cddbb81f3b2c94cb8246bd064ad\concurrent-futures-1.2.0-alpha03.jar"
      resolved="androidx.concurrent:concurrent-futures:1.2.0-alpha03"/>
  <library
      name="androidx.concurrent:concurrent-futures-ktx:1.2.0-alpha03@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures-ktx\1.2.0-alpha03\33bfff0b4d1b25c76bd2d355fec8f188d7bae241\concurrent-futures-ktx-1.2.0-alpha03.jar"
      resolved="androidx.concurrent:concurrent-futures-ktx:1.2.0-alpha03"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-reactive:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-reactive\1.7.3\3f30c8dd5cd0993ca2804591210141be762786f8\kotlinx-coroutines-reactive-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-reactive:1.7.3"/>
  <library
      name="io.ktor:ktor-client-okhttp-jvm:2.3.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-client-okhttp-jvm\2.3.2\5ffb6be71200b631b23479fd197131136f70bc2d\ktor-client-okhttp-jvm-2.3.2.jar"
      resolved="io.ktor:ktor-client-okhttp-jvm:2.3.2"/>
  <library
      name="io.ktor:ktor-client-content-negotiation-jvm:2.3.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-client-content-negotiation-jvm\2.3.2\9ed23f86f2a006c73f13f99a5b77e01208f9b733\ktor-client-content-negotiation-jvm-2.3.2.jar"
      resolved="io.ktor:ktor-client-content-negotiation-jvm:2.3.2"/>
  <library
      name="io.ktor:ktor-client-logging-jvm:2.3.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-client-logging-jvm\2.3.2\9d9410538b16be2656089559961434888c6dc8d5\ktor-client-logging-jvm-2.3.2.jar"
      resolved="io.ktor:ktor-client-logging-jvm:2.3.2"/>
  <library
      name="io.ktor:ktor-client-core-jvm:2.3.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-client-core-jvm\2.3.2\b6ccf5b17afc2df0c2d6882f321fd66bb42c7ae9\ktor-client-core-jvm-2.3.2.jar"
      resolved="io.ktor:ktor-client-core-jvm:2.3.2"/>
  <library
      name="io.ktor:ktor-serialization-kotlinx-json-jvm:2.3.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-serialization-kotlinx-json-jvm\2.3.2\fba15b370a2933abb5076f958c539ca0dcb8d1fd\ktor-serialization-kotlinx-json-jvm-2.3.2.jar"
      resolved="io.ktor:ktor-serialization-kotlinx-json-jvm:2.3.2"/>
  <library
      name="io.ktor:ktor-events-jvm:2.3.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-events-jvm\2.3.2\436c568ca8381a3fef4a694f81b1b0d62ce670c3\ktor-events-jvm-2.3.2.jar"
      resolved="io.ktor:ktor-events-jvm:2.3.2"/>
  <library
      name="io.ktor:ktor-websocket-serialization-jvm:2.3.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-websocket-serialization-jvm\2.3.2\c741c542219ad84a95af6da0d78ccbeace2002f6\ktor-websocket-serialization-jvm-2.3.2.jar"
      resolved="io.ktor:ktor-websocket-serialization-jvm:2.3.2"/>
  <library
      name="io.ktor:ktor-serialization-kotlinx-jvm:2.3.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-serialization-kotlinx-jvm\2.3.2\fd32c467882ca1f1a049a08e2813af9937b821aa\ktor-serialization-kotlinx-jvm-2.3.2.jar"
      resolved="io.ktor:ktor-serialization-kotlinx-jvm:2.3.2"/>
  <library
      name="io.ktor:ktor-serialization-jvm:2.3.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-serialization-jvm\2.3.2\23ca3bc995da97cd29de4e9e047da0009ebeedd9\ktor-serialization-jvm-2.3.2.jar"
      resolved="io.ktor:ktor-serialization-jvm:2.3.2"/>
  <library
      name="io.ktor:ktor-websockets-jvm:2.3.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-websockets-jvm\2.3.2\210ba61f35da17e7b57bd5c493f330db7de88c95\ktor-websockets-jvm-2.3.2.jar"
      resolved="io.ktor:ktor-websockets-jvm:2.3.2"/>
  <library
      name="io.ktor:ktor-http-jvm:2.3.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-http-jvm\2.3.2\a7a872627b3355eab152932c28101454f82e95b0\ktor-http-jvm-2.3.2.jar"
      resolved="io.ktor:ktor-http-jvm:2.3.2"/>
  <library
      name="io.ktor:ktor-utils-jvm:2.3.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-utils-jvm\2.3.2\ca2818dc811d46ad8719cb9b207c75864921b3ec\ktor-utils-jvm-2.3.2.jar"
      resolved="io.ktor:ktor-utils-jvm:2.3.2"/>
  <library
      name="io.ktor:ktor-io-jvm:2.3.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.ktor\ktor-io-jvm\2.3.2\461667ef8369e8e488d05fdaa39ce0e3bf465e4c\ktor-io-jvm-2.3.2.jar"
      resolved="io.ktor:ktor-io-jvm:2.3.2"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-jdk8:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-jdk8\1.7.3\263611b87f7546402852e43a8dc7ef3223ada42a\kotlinx-coroutines-jdk8-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-jdk8:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-slf4j\1.7.3\34086fbfd556969d86b45ee2a69b816ee2413701\kotlinx-coroutines-slf4j-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:1.7.3"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.11.0\436932d695b2c43f2c86b8111c596179cd133d56\okhttp-4.11.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.11.0"/>
  <library
      name="com.squareup.okio:okio-jvm:3.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.2.0\332d1c5dc82b0241cb1d35bb0901d28470cc89ca\okio-jvm-3.2.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.2.0"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.5.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-core-jvm\1.5.1\e26cf5dfbcfe3e82ca196694dfd305753b1a49b9\kotlinx-serialization-core-jvm-1.5.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.5.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.5.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-json-jvm\1.5.1\21ac91884e0b9462b9106d1a83e5e0d200170c65\kotlinx-serialization-json-jvm-1.5.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.5.1"/>
  <library
      name="androidx.collection:collection-ktx:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.4.0\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.4.0.jar"
      resolved="androidx.collection:collection-ktx:1.4.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d891f4fd8bca37cb07a202f6b57524f4\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d891f4fd8bca37cb07a202f6b57524f4\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b5ab4ef4b55d84fa38c3c518aac577\transformed\tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b5ab4ef4b55d84fa38c3c518aac577\transformed\tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.slf4j:slf4j-nop:2.0.9@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.slf4j\slf4j-nop\2.0.9\fa0627049304bf501bf6a0e4d1416bcaec9a72a9\slf4j-nop-2.0.9.jar"
      resolved="org.slf4j:slf4j-nop:2.0.9"/>
  <library
      name="org.reactivestreams:reactive-streams:1.0.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.reactivestreams\reactive-streams\1.0.3\d9fb7a7926ffa635b3dcaa5049fb2bfa25b3e7d0\reactive-streams-1.0.3.jar"
      resolved="org.reactivestreams:reactive-streams:1.0.3"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.15.0\38c8485a652f808c8c149150da4e5c2b0bd17f9a\error_prone_annotations-2.15.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.15.0"/>
  <library
      name="org.slf4j:slf4j-api:2.0.9@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.slf4j\slf4j-api\2.0.9\7cf2726fdcfbc8610f9a71fb3ed639871f315340\slf4j-api-2.0.9.jar"
      resolved="org.slf4j:slf4j-api:2.0.9"/>
</libraries>
