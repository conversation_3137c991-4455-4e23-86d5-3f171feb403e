<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/rounded_panel_background">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Tracking Help"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="12dp">

        <View
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:background="@drawable/circle_shape"
            android:backgroundTint="#FF0000" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Red: Tracking lost. Move your device slowly to scan the environment."
            android:textColor="@color/white"
            android:textSize="14sp"
            android:layout_marginLeft="8dp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="12dp">

        <View
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:background="@drawable/circle_shape"
            android:backgroundTint="#FFFF00" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Yellow: Limited tracking. Continue moving to improve tracking quality."
            android:textColor="@color/white"
            android:textSize="14sp"
            android:layout_marginLeft="8dp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <View
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:background="@drawable/circle_shape"
            android:backgroundTint="#00FF00" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Green: Good tracking. Tap on a wall to place content."
            android:textColor="@color/white"
            android:textSize="14sp"
            android:layout_marginLeft="8dp" />
    </LinearLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Tips for Detecting Vertical Walls:"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:layout_marginBottom="8dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="• Move your device VERY SLOWLY in a figure-8 pattern\n• Point camera directly at walls (not at an angle)\n• Scan textured walls with patterns or objects\n• Avoid plain white or reflective surfaces\n• Keep 0.5-1.5m distance from walls\n• Ensure bright, even lighting\n• Make sure camera lens is clean\n• Try different walls if one isn't detected"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:layout_marginBottom="16dp" />

    <Button
        android:id="@+id/btn_close_tracking_help"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Got it"
        android:textColor="@color/white"
        android:background="@drawable/rounded_button_background" />

</LinearLayout>
