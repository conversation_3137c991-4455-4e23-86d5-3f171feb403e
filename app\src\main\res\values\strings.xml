<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">Graffiti AR Deace</string>

    <!-- Common strings -->
    <string name="scanning_environment" tools:ignore="DuplicateDefinition">Point your device at a wall and move it slowly</string>
    <string name="installing_arcore">Installing ARCore</string>
    <string name="initializing_ar">Initializing AR...</string>
    <string name="requesting_permissions">Requesting permissions...</string>
    <string name="arcore_not_supported">ARCore is not supported on this device</string>
    <string name="permission_required">Permission Required</string>
    <string name="camera_permission_required_message">This app requires camera permission to function. Please enable it in settings.</string>
    <string name="settings">Settings</string>
    <string name="exit">Exit</string>
    <string name="opacity_format">%d%%</string>
    <string name="opacity_value">%d%%</string>

    <!-- Main menu -->
    <string name="grid_tab">Grid</string>
    <string name="image_tab">Image</string>
    <string name="export_tab">Export</string>
    <string name="config_tab">Config</string>
    <string name="deace_tab">Deace</string>
    <string name="minimize_menu">Minimize menu</string>

    <!-- Deace panel -->
    <string name="confirm_reinit_app">RESTART APP</string>
    <string name="app_restarting">App is restarting...</string>

    <!-- -->
    <string name="baking_title">Baking Assistant</string>
    <string name="prompt_placeholder">Describe what you want to bake</string>
    <string name="results_placeholder">Results will show here</string>

    <!-- Grid panel -->
    <string name="grid_format">Grid</string>
    <string name="vertical">Vertical (9:16)</string>
    <string name="square">Square (1:1)</string>
    <string name="horizontal">Horizontal (16:9)</string>
    <string name="fix_grid">Fix Grid</string>
    <string name="release_grid">Release Grid</string>
    <string name="grid_opacity">Grid Opacity</string>
    <string name="choose_color">Choose Color</string>


    <!-- Image panel -->
    <string name="load_image">Load Image</string>
    <string name="fix_image">Fix Image</string>
    <string name="release_image">Release Image</string>
    <string name="image_opacity">Image Opacity</string>
    <string name="no_image_loaded">No image loaded</string>
    <string name="image_size_format" formatted="false">Image size: %d×%d</string>
    <string name="opacity_format_50">50%</string>
    <string name="preserve_aspect_ratio">Preserve Aspect Ratio</string>
    <string name="failed_to_load_image">Failed to load image</string>
    <string name="image_load_error">Error loading image: %s</string>

    <!-- Export panel -->
    <string name="export_ar_view">Export AR View</string>
    <string name="export_instructions">Exports the current AR view without UI elements</string>
    <string name="storage_permission_required">Storage permission is required to save images</string>
    <string name="storage_permission_explanation">To save images from the app, we need permission to access your device storage.\n\nAfter clicking \"GRANT PERMISSION\", please also approve the permission in the system dialog that will appear.</string>
    <string name="storage_permission_denied">Cannot save images without storage permission</string>
    <string name="grant_permission">Grant Permission</string>
    <string name="cancel">Cancel</string>
    <string name="failed_to_capture_image">Failed to capture AR view</string>
    <string name="ar_session_not_ready">AR session is not ready</string>
    <string name="failed_to_save_image">Failed to save image</string>
    <string name="export_success">Export Successful</string>
    <string name="image_saved_to">Image saved to:\n%s</string>
    <string name="export_error">Error exporting image: %s</string>

    <!-- Config panel -->
    <string name="ar_session_reset">AR Session Reset</string>
    <string name="settings_reset">Settings Reset</string>
    <string name="ok">OK</string>
    <string name="ar_settings">AR Settings</string>
    <string name="reset_ar_session">Reset AR Session</string>
    <string name="reset_session">Reset Session</string>
    <string name="show_planes">Show Planes</string>
    <string name="visualize_detected_planes">Visualize Detected Planes</string>
    <string name="grid_options">Grid Options</string>
    <string name="default_format">Default Format</string>
    <string name="default_grid_format">Default Grid Format</string>
    <string name="default_opacity">Default Opacity</string>
    <string name="grid_color">Grid Color</string>
    <string name="line_thickness">Line Thickness</string>
    <string name="projected_image_options">Projected Image Options</string>
    <string name="export_settings">Export Settings</string>
    <string name="image_quality">Image Quality</string>
    <string name="normal">Normal</string>
    <string name="normal_quality">Normal Quality</string>
    <string name="maximum">Maximum</string>
    <string name="max_quality">Maximum Quality</string>
    <string name="file_format">File Format</string>
    <string name="png">PNG</string>
    <string name="png_format">PNG Format</string>
    <string name="jpg">JPG</string>
    <string name="jpg_format">JPG Format</string>
    <string name="filename_pattern">Filename Pattern</string>
    <string name="save_directory">Save Directory</string>
    <string name="choose_folder">Choose Folder</string>
    <string name="general">General</string>
    <string name="app_settings">App Settings</string>
    <string name="reset_settings">Reset Settings</string>
    <string name="help_about">Help / About</string>
    <string name="about_text">Graffiti AR Deace v0.1 2025. ©DEACE : Author member of ADAGP. Authorization is required for any use of the works (www.adagp.fr). More on www.deace.com</string>

    <string-array name="grid_formats_array">
        <item>Vertical (9:16)</item>
        <item>Square (1:1)</item>
        <item>Horizontal (16:9)</item>
    </string-array>

    <!-- Tracking quality indicator strings -->
    <string name="tracking">Tracking</string>
    <string name="tracking_good">Good</string>
    <string name="tracking_limited">Limited</string>
    <string name="tracking_lost">Lost</string>
    <string name="tracking_initializing">Initializing tracking…</string>
    <string name="tracking_move_device">Move your device slowly to scan walls</string>
    <string name="tracking_looking_for_surfaces">Looking for surfaces…</string>
    <string name="tracking_found_surfaces">Surfaces detected</string>
    <string name="tracking_ready">Tracking ready</string>
    <string name="tracking_help_lost">Move device slowly to scan environment</string>
    <string name="tracking_help_limited">Continue moving to improve tracking</string>
    <string name="tracking_help_good">Good tracking - tap on a wall to place</string>

    <!-- Help strings -->
    <string name="help">Help</string>


</resources>