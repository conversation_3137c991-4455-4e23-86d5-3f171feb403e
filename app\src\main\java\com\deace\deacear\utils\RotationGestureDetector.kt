package com.deace.deacear.utils

import android.view.MotionEvent
import kotlin.math.atan2

/**
 * Detects rotation gestures using the supplied MotionEvents.
 * The [OnRotationGestureListener] callback will notify clients when a rotation
 * gesture occurs with the initial angle and the rotating angle.
 */
class RotationGestureDetector(private val mListener: OnRotationGestureListener) {
    private var mPointer1Id = INVALID_POINTER_ID
    private var mPointer2Id = INVALID_POINTER_ID
    private var mAngle = 0f
    private var mPrevAngle = 0f
    
    /**
     * Sets whether rotation should be reported even if the fingers haven't moved.
     * When true, rotations will be reported even when the user holds still.
     * Default is false, meaning rotation is only reported when the angle changes.
     */
    var isRotationEnabled = true
    
    /**
     * The angle of the current rotation gesture, in degrees.
     * Positive angles indicate clockwise rotation; negative angles counterclockwise.
     * This value is only meaningful when a rotation is in progress.
     */
    val rotationAngleDegrees get() = Math.toDegrees(mAngle.toDouble()).toFloat()
    
    /**
     * The change in angle since the previous rotation event, in degrees.
     */
    val rotationChangeDegrees get() = Math.toDegrees((mAngle - mPrevAngle).toDouble()).toFloat()
    
    /**
     * Listener to notify when rotation gestures are detected.
     */
    interface OnRotationGestureListener {
        /**
         * Called when a rotation gesture is detected.
         * @param rotationDetector the detector reporting the event
         * @return true if the event is consumed, false if other gestures should still process it
         */
        fun onRotation(rotationDetector: RotationGestureDetector): Boolean
    }
    
    /**
     * Process a MotionEvent to detect rotation gesture.
     */
    fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                mPointer1Id = event.getPointerId(0)
            }
            
            MotionEvent.ACTION_POINTER_DOWN -> {
                // We have multiple pointers - we need at least 2 for rotation
                mPointer2Id = event.getPointerId(event.actionIndex)
                updateAngle(event)
                mPrevAngle = mAngle
            }
            
            MotionEvent.ACTION_MOVE -> {
                if (mPointer1Id != INVALID_POINTER_ID && mPointer2Id != INVALID_POINTER_ID) {
                    val prevAngle = mAngle
                    updateAngle(event)
                    
                    // Only report rotation if the angle actually changed
                    if (isRotationEnabled || mAngle != prevAngle) {
                        mPrevAngle = prevAngle
                        mListener.onRotation(this)
                    }
                }
            }
            
            MotionEvent.ACTION_UP -> {
                mPointer1Id = INVALID_POINTER_ID
            }
            
            MotionEvent.ACTION_POINTER_UP -> {
                val pointerId = event.getPointerId(event.actionIndex)
                if (pointerId == mPointer1Id) {
                    // Choose another pointer as the primary
                    val newIndex = if (event.findPointerIndex(mPointer2Id) != -1) mPointer2Id else {
                        // Or find another active pointer if pointer2 is already gone
                        val newIdx = if (event.actionIndex == 0) 1 else 0
                        if (newIdx < event.pointerCount) event.getPointerId(newIdx) else INVALID_POINTER_ID
                    }
                    mPointer1Id = newIndex
                    updateAngle(event)
                    mPrevAngle = mAngle
                } else if (pointerId == mPointer2Id) {
                    mPointer2Id = INVALID_POINTER_ID
                }
            }
            
            MotionEvent.ACTION_CANCEL -> {
                mPointer1Id = INVALID_POINTER_ID
                mPointer2Id = INVALID_POINTER_ID
            }
        }
        
        return true
    }
    
    private fun updateAngle(event: MotionEvent) {
        if (mPointer1Id == INVALID_POINTER_ID || mPointer2Id == INVALID_POINTER_ID) {
            return
        }
        
        val idx1 = event.findPointerIndex(mPointer1Id)
        val idx2 = event.findPointerIndex(mPointer2Id)
        
        if (idx1 == -1 || idx2 == -1) {
            return
        }
        
        val x1 = event.getX(idx1)
        val y1 = event.getY(idx1)
        val x2 = event.getX(idx2)
        val y2 = event.getY(idx2)
        
        // Calculate the angle between the two pointers
        mAngle = atan2(y2 - y1, x2 - x1)
    }
    
    companion object {
        private const val INVALID_POINTER_ID = -1
    }
}