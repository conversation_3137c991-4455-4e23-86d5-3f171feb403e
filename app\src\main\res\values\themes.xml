<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Theme.DeaceAR" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/black</item>
        <item name="colorPrimaryDark">@color/black</item>
        <item name="colorAccent">@color/black</item>
        <item name="android:windowBackground">@android:color/white</item>
    </style>

    <!-- Splash screen theme -->
    <style name="Theme.SplashScreen" parent="Theme.DeaceAR">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <!-- Dialog theme -->
    <style name="Theme.DeaceAR.Dialog" parent="Theme.AppCompat.Dialog.Alert">
        <item name="colorPrimary">@color/black</item>
        <item name="colorAccent">@color/black</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:background">@drawable/rounded_panel_background</item>
        <item name="buttonBarPositiveButtonStyle">@style/DialogButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/DialogButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">@style/DialogButtonStyle</item>
    </style>

    <style name="DialogButtonStyle" parent="Widget.AppCompat.Button.ButtonBar.AlertDialog">
        <item name="android:textColor">@color/white</item>
    </style>
</resources>