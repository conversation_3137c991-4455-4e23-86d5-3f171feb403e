# DeaceAR App Messages Workflow

This document outlines all user-facing messages in the DeaceAR application, organized by context and workflow stage. It provides a comprehensive guide to understand when and why specific messages appear to the user.

## 1. App Launch & Permission Messages

### Initial Launch
- **"Initializing AR..."** - Displayed during ARCore initialization
- **"Installing ARCore"** - Shown if ARCore needs to be installed
- **"Requesting permissions..."** - Displayed when requesting camera/storage permissions

### Permission Requests
- **"This app requires camera permission to function. Please enable it in settings."** - Shown when camera permission is denied
- **"Storage permission is required to save images"** - Displayed when attempting to export without storage permission
- **"To save images from the app, we need permission to access your device storage."** - Explanation in storage permission dialog
- **"Cannot save images without storage permission"** - Shown after storage permission denial

### Error States
- **"ARCore is not supported on this device"** - Displayed when device doesn't support ARCore
- **"Camera unavailable: [error message]"** - Shown when camera cannot be accessed despite permissions

## 2. Tracking Status Messages

### Tracking Quality Indicator
These messages appear next to the colored tracking indicator dot:

| Color  | Status Text | Description Text | Context |
|--------|-------------|------------------|---------|
| Red    | **"Lost"**  | **"Move your device slowly to scan environment"** | Tracking completely lost |
| Yellow | **"Limited"** | **"Continue moving to improve tracking"** | Partial tracking, needs improvement |
| Green  | **"Good"**  | **"Good tracking - tap on a wall to place"** | Tracking is stable and ready |

### Tracking Progress Messages
These messages appear during the scanning/initialization phase:

1. **"Point your device at a wall and move it slowly"** - Initial scanning guidance
2. **"Initializing tracking…"** - Shown during ARCore initialization
3. **"Looking for surfaces…"** - Displayed when tracking is good but no planes detected yet
4. **"Surfaces detected"** - Shown when horizontal planes are found
5. **"Surfaces detected (vertical)"** - Shown when vertical planes (walls) are found
6. **"Tracking ready"** - Displayed when tracking is stable and content can be placed

### Tracking Guidance Toasts
These toast messages provide additional guidance during tracking:

- **"Move your device slowly to scan walls and floors"** - Shown during initial scanning or when tracking is lost
- **"Vertical wall detected! Tap on it to place content"** - Displayed when a vertical plane is first detected
- **"Surfaces detected! You can place content now, or continue scanning for walls"** - Shown when horizontal planes are detected
- **"Tracking is good! You can place content now"** - Displayed when tracking is stable even without specific planes
- **"Tracking ready - tap on a wall to place content"** - Shown when tracking is stable with vertical planes
- **"Tracking ready - tap to place content"** - Shown when tracking is stable with any planes

## 3. Content Placement Messages

### Grid Placement
- **"Tap on a detected surface to place a grid"** - Shown when grid panel is opened
- **"Tap on a detected surface to place a Vertical (9:16) grid"** - When vertical format is selected
- **"Tap on a detected surface to place a Square (1:1) grid"** - When square format is selected
- **"Tap on a detected surface to place a Horizontal (16:9) grid"** - When horizontal format is selected
- **"Grid placed successfully!"** - Confirmation after grid placement
- **"Grid position updated!"** - Shown when grid position is changed
- **"Grid fixed in place"** - Displayed when grid is locked in position

### Image Placement
- **"Image loaded. Now tap on a wall to place it"** - Shown after image selection from gallery
- **"Image placed! You can now adjust it with touch gestures."** - Confirmation after image placement
- **"Image position updated!"** - Shown when image position is changed
- **"Image locked in place"** - Displayed when image is fixed in position
- **"Tap on a surface to place or move content"** - General guidance for content placement

### Placement Error Messages
- **"No surface detected. Try moving around to detect surfaces"** - Shown when tap occurs but no planes are detected
- **"Tap directly on a highlighted surface to place content"** - Shown when tap misses detected planes

## 4. Feature Usage Messages

### First-Time User Guidance
- **"The menu in the top-left has tabs for Grid, Image, Export, and Settings"** - Initial menu guidance
- **"Tap anywhere to show controls if they're hidden. Tap the ? for help."** - UI interaction guidance

### Export Functionality
- **"Failed to capture AR view"** - Shown when export capture fails
- **"Failed to save image"** - Displayed when image saving fails
- **"Export error: [error message]"** - Detailed export error information
- **"Image saved to [file path]"** - Confirmation of successful export

### Settings and Configuration
- **"AR Session Reset"** - Confirmation after resetting AR session
- **"Settings Reset"** - Confirmation after resetting app settings

## 5. Message Flow by User Journey

### Initial Setup Flow
1. **"Initializing AR..."** → **"Point your device at a wall and move it slowly"** → **"Looking for surfaces..."**
2. If tracking lost: **"Lost"** + **"Move your device slowly to scan environment"**
3. If tracking limited: **"Limited"** + **"Continue moving to improve tracking"**
4. When planes detected: **"Surfaces detected"** → **"Good"** + **"Good tracking - tap on a wall to place"**

### Image Placement Flow
1. Select Image tab → **"Image loaded. Now tap on a wall to place it"**
2. If no surface detected: **"No surface detected. Try moving around to detect surfaces"**
3. If tap misses surface: **"Tap directly on a highlighted surface to place content"**
4. On successful placement: **"Image placed! You can now adjust it with touch gestures."**
5. After adjustments, tap Fix Image: **"Image locked in place"**

### Grid Placement Flow
1. Select Grid tab → **"Tap on a detected surface to place a grid"**
2. Select grid format → **"Tap on a detected surface to place a [format] grid"**
3. On successful placement: **"Grid placed successfully!"**
4. After adjustments, tap Fix Grid: **"Grid fixed in place"**

### Export Flow
1. Select Export tab → Tap Export button
2. If permission needed: **"Storage permission is required to save images"**
3. On successful export: **"Image saved to [file path]"**

## 6. Message Display Guidelines

- **Toast Duration**: Most guidance messages use `Toast.LENGTH_LONG` (3.5 seconds)
- **Status Messages**: Tracking status is always visible in the tracking indicator
- **Error Messages**: Should be descriptive and suggest recovery actions
- **Success Messages**: Should be brief confirmations of completed actions

This comprehensive message workflow ensures users receive appropriate guidance throughout their experience with the DeaceAR application.