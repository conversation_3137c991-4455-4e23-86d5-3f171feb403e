package com.deace.deacear.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

object PermissionHelper {

    // Define all permissions needed by the app
    val REQUIRED_PERMISSIONS = arrayOf(
        Manifest.permission.CAMERA
        // Storage permissions will be requested separately when needed
        // Storage permissions are not critical for AR functionality
        // Manifest.permission.READ_EXTERNAL_STORAGE,
        // Manifest.permission.WRITE_EXTERNAL_STORAGE
    )

    // Define storage permissions that are only needed for export functionality
    val STORAGE_PERMISSIONS = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
        arrayOf(Manifest.permission.READ_MEDIA_IMAGES)
    } else {
        arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )
    }

    // Check if all permissions are granted
    fun hasAllPermissions(context: Context): <PERSON><PERSON>an {
        return REQUIRED_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }

    // Check if a specific permission is granted
    private fun hasPermission(context: Context, permission: String): Boolean {
        return ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
    }

    // Check if camera permission is granted (used frequently in AR apps)
    fun hasCameraPermission(context: Context): Boolean {
        return hasPermission(context, Manifest.permission.CAMERA)
    }

    // Check if storage permissions are granted
    fun hasStoragePermissions(context: Context): Boolean {
        return if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            hasPermission(context, Manifest.permission.READ_MEDIA_IMAGES)
        } else {
            hasPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE) &&
                    hasPermission(context, Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }
    }

    // Request permissions from an Activity
    fun requestPermissions(activity: Activity, requestCode: Int) {
        ActivityCompat.requestPermissions(activity, REQUIRED_PERMISSIONS, requestCode)
    }

    // Request specific permissions
    fun requestSpecificPermissions(activity: Activity, permissions: Array<String>, requestCode: Int) {
        ActivityCompat.requestPermissions(activity, permissions, requestCode)
    }

    // Check if all requested permissions were granted in onRequestPermissionsResult
    fun allPermissionsGranted(grantResults: IntArray): Boolean {
        return grantResults.all { it == PackageManager.PERMISSION_GRANTED }
    }

    // Check if explanation should be shown for any permission
    fun shouldShowRationale(activity: Activity, permission: String): Boolean {
        return ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)
    }

    // Special handling for Android 11+ (Scoped Storage)
    fun isScopedStorageRequired(): Boolean {
        return android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R
    }

    // Handle the case where permissions are permanently denied
    fun arePermissionsPermanentlyDenied(activity: Activity, permissions: Array<String>): Boolean {
        return permissions.any { permission ->
            !ActivityCompat.shouldShowRequestPermissionRationale(activity, permission) &&
                    !hasPermission(activity, permission)
        }
    }
}