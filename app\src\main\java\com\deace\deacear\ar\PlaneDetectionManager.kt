package com.deace.deacear.ar

import android.util.Log
import com.google.ar.core.Frame
import com.google.ar.core.Plane
import com.google.ar.core.TrackingState
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

/**
 * Enhanced plane detection manager for improved vertical plane detection reliability
 */
class PlaneDetectionManager {
    
    companion object {
        private const val TAG = "PlaneDetectionManager"
        
        // Enhanced thresholds for better plane classification
        private const val MIN_PLANE_SIZE = 0.15f // Minimum size for any plane (increased from 0.1f)
        private const val WALL_SIZE_THRESHOLD = 0.5f // Wall-sized threshold (increased from 0.4f)
        private const val LARGE_WALL_THRESHOLD = 1.0f // Large wall threshold (increased from 0.8f)
        private const val VERY_LARGE_WALL_THRESHOLD = 1.5f // Very large wall threshold (increased from 1.2f)
        
        // Area thresholds for better assessment
        private const val MIN_WALL_AREA = 0.3f // Minimum area for wall consideration
        private const val GOOD_WALL_AREA = 0.8f // Good wall area threshold
        private const val EXCELLENT_WALL_AREA = 1.5f // Excellent wall area threshold
        
        // Aspect ratio thresholds for wall-like shapes
        private const val MIN_WALL_ASPECT_RATIO = 0.3f // Very wide walls
        private const val MAX_WALL_ASPECT_RATIO = 3.0f // Very tall walls
        
        // Tracking stability thresholds
        private const val MIN_TRACKING_FRAMES = 5 // Minimum frames a plane should be tracked
        private const val STABILITY_THRESHOLD = 0.1f // Maximum position change for stability
    }
    
    // Plane tracking history for stability assessment
    private val planeTrackingHistory = mutableMapOf<Int, PlaneTrackingInfo>()
    
    data class PlaneTrackingInfo(
        var firstSeenFrame: Long = 0,
        var lastSeenFrame: Long = 0,
        var frameCount: Int = 0,
        var lastPosition: FloatArray = floatArrayOf(0f, 0f, 0f),
        var positionStability: Float = 0f,
        var qualityScore: Int = 0
    )
    
    data class PlaneQualityAssessment(
        val plane: Plane,
        val qualityScore: Int,
        val isWallLike: Boolean,
        val sizeCategory: String,
        val trackingStability: Float,
        val recommendedForPlacement: Boolean
    )
    
    /**
     * Analyzes all planes in the current frame and returns quality assessments
     */
    fun analyzePlanes(frame: Frame): List<PlaneQualityAssessment> {
        val currentFrameTime = System.currentTimeMillis()
        val allPlanes = frame.getUpdatedTrackables(Plane::class.java)
        val assessments = mutableListOf<PlaneQualityAssessment>()
        
        // Update tracking history and assess each plane
        for (plane in allPlanes) {
            if (plane.trackingState != TrackingState.TRACKING || plane.subsumedBy != null) {
                continue
            }
            
            // Update tracking history
            updatePlaneTrackingHistory(plane, currentFrameTime)
            
            // Assess plane quality
            val assessment = assessPlaneQuality(plane)
            if (assessment != null) {
                assessments.add(assessment)
            }
        }
        
        // Clean up old tracking history
        cleanupTrackingHistory(currentFrameTime)
        
        // Sort assessments by quality score (highest first)
        return assessments.sortedByDescending { it.qualityScore }
    }
    
    /**
     * Gets the best vertical planes for image placement
     */
    fun getBestVerticalPlanes(frame: Frame, maxPlanes: Int = 5): List<Plane> {
        val assessments = analyzePlanes(frame)
        
        return assessments
            .filter { it.plane.type == Plane.Type.VERTICAL && it.recommendedForPlacement }
            .take(maxPlanes)
            .map { it.plane }
    }
    
    /**
     * Checks if there are reliable vertical planes available
     */
    fun hasReliableVerticalPlanes(frame: Frame): Boolean {
        val assessments = analyzePlanes(frame)
        return assessments.any { 
            it.plane.type == Plane.Type.VERTICAL && 
            it.recommendedForPlacement && 
            it.qualityScore >= 5 
        }
    }
    
    /**
     * Gets detection guidance based on current plane state
     */
    fun getDetectionGuidance(frame: Frame): String {
        val assessments = analyzePlanes(frame)
        val verticalPlanes = assessments.filter { it.plane.type == Plane.Type.VERTICAL }
        
        return when {
            verticalPlanes.isEmpty() -> 
                "Point camera at walls and move slowly to detect surfaces"
            
            verticalPlanes.none { it.recommendedForPlacement } -> 
                "Move closer to walls or scan larger wall areas"
            
            verticalPlanes.any { it.qualityScore >= 7 } -> 
                "Excellent wall detection! Tap on highlighted areas to place content"
            
            verticalPlanes.any { it.qualityScore >= 5 } -> 
                "Good wall detection. Continue scanning for better placement options"
            
            else -> 
                "Walls detected but need better tracking. Move camera slowly and steadily"
        }
    }
    
    private fun updatePlaneTrackingHistory(plane: Plane, currentTime: Long) {
        val planeId = plane.hashCode()
        val trackingInfo = planeTrackingHistory.getOrPut(planeId) {
            PlaneTrackingInfo(firstSeenFrame = currentTime)
        }
        
        // Update tracking information
        trackingInfo.lastSeenFrame = currentTime
        trackingInfo.frameCount++
        
        // Calculate position stability
        val currentPosition = floatArrayOf(
            plane.centerPose.tx(),
            plane.centerPose.ty(),
            plane.centerPose.tz()
        )
        
        if (trackingInfo.frameCount > 1) {
            val positionChange = calculatePositionChange(trackingInfo.lastPosition, currentPosition)
            trackingInfo.positionStability = (trackingInfo.positionStability + positionChange) / 2f
        }
        
        trackingInfo.lastPosition = currentPosition
    }
    
    private fun assessPlaneQuality(plane: Plane): PlaneQualityAssessment? {
        val extentX = plane.extentX
        val extentZ = plane.extentZ
        val planeArea = extentX * extentZ
        
        // Basic size validation
        if (extentX < MIN_PLANE_SIZE && extentZ < MIN_PLANE_SIZE) {
            return null
        }
        
        var qualityScore = 0
        var sizeCategory = "small"
        
        // Size scoring with enhanced thresholds
        when {
            extentX >= VERY_LARGE_WALL_THRESHOLD || extentZ >= VERY_LARGE_WALL_THRESHOLD -> {
                qualityScore += 4
                sizeCategory = "very_large"
            }
            extentX >= LARGE_WALL_THRESHOLD || extentZ >= LARGE_WALL_THRESHOLD -> {
                qualityScore += 3
                sizeCategory = "large"
            }
            extentX >= WALL_SIZE_THRESHOLD || extentZ >= WALL_SIZE_THRESHOLD -> {
                qualityScore += 2
                sizeCategory = "wall_sized"
            }
            else -> {
                qualityScore += 1
                sizeCategory = "small"
            }
        }
        
        // Area scoring with enhanced thresholds
        when {
            planeArea >= EXCELLENT_WALL_AREA -> qualityScore += 3
            planeArea >= GOOD_WALL_AREA -> qualityScore += 2
            planeArea >= MIN_WALL_AREA -> qualityScore += 1
        }
        
        // Aspect ratio assessment for wall-like shapes
        val aspectRatio = if (extentZ > 0) extentX / extentZ else 0f
        val isWallLike = aspectRatio >= MIN_WALL_ASPECT_RATIO && aspectRatio <= MAX_WALL_ASPECT_RATIO
        
        if (isWallLike) {
            qualityScore += 1
            
            // Bonus for typical wall aspect ratios
            if (aspectRatio <= 0.7f || aspectRatio >= 1.4f) {
                qualityScore += 1 // More wall-like
            }
        }
        
        // Tracking stability scoring
        val planeId = plane.hashCode()
        val trackingInfo = planeTrackingHistory[planeId]
        var trackingStability = 0f
        
        if (trackingInfo != null) {
            trackingStability = 1f - min(1f, trackingInfo.positionStability / STABILITY_THRESHOLD)
            
            // Stability bonus
            if (trackingInfo.frameCount >= MIN_TRACKING_FRAMES) {
                qualityScore += 1
                
                if (trackingStability > 0.8f) {
                    qualityScore += 1 // Very stable
                }
            }
        }
        
        // Vertical plane bonus
        if (plane.type == Plane.Type.VERTICAL) {
            qualityScore += 2 // Prefer vertical planes for our use case
        }
        
        // Determine if recommended for placement
        val recommendedForPlacement = qualityScore >= 4 && 
                                    planeArea >= MIN_WALL_AREA && 
                                    (trackingInfo?.frameCount ?: 0) >= MIN_TRACKING_FRAMES
        
        return PlaneQualityAssessment(
            plane = plane,
            qualityScore = qualityScore,
            isWallLike = isWallLike,
            sizeCategory = sizeCategory,
            trackingStability = trackingStability,
            recommendedForPlacement = recommendedForPlacement
        )
    }
    
    private fun calculatePositionChange(oldPos: FloatArray, newPos: FloatArray): Float {
        val dx = newPos[0] - oldPos[0]
        val dy = newPos[1] - oldPos[1]
        val dz = newPos[2] - oldPos[2]
        return kotlin.math.sqrt(dx * dx + dy * dy + dz * dz)
    }
    
    private fun cleanupTrackingHistory(currentTime: Long) {
        val maxAge = 10000 // 10 seconds
        planeTrackingHistory.entries.removeAll { (_, info) ->
            currentTime - info.lastSeenFrame > maxAge
        }
    }
    
    /**
     * Logs detailed plane detection statistics
     */
    fun logPlaneStatistics(frame: Frame) {
        val assessments = analyzePlanes(frame)
        val verticalPlanes = assessments.filter { it.plane.type == Plane.Type.VERTICAL }
        val recommendedPlanes = verticalPlanes.filter { it.recommendedForPlacement }
        
        Log.d(TAG, "=== PLANE DETECTION STATISTICS ===")
        Log.d(TAG, "Total planes: ${assessments.size}")
        Log.d(TAG, "Vertical planes: ${verticalPlanes.size}")
        Log.d(TAG, "Recommended for placement: ${recommendedPlanes.size}")
        
        if (recommendedPlanes.isNotEmpty()) {
            Log.d(TAG, "Best plane quality score: ${recommendedPlanes.first().qualityScore}")
            Log.d(TAG, "Best plane size: ${recommendedPlanes.first().sizeCategory}")
        }
        
        Log.d(TAG, "Detection guidance: ${getDetectionGuidance(frame)}")
        Log.d(TAG, "================================")
    }
}
