package com.deace.deacear.ar

import android.content.Context
import android.opengl.GLES11Ext
import android.opengl.GLES30
import com.google.ar.core.Frame
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.FloatBuffer

/**
 * This class renders the AR background from camera feed. It creates and hosts the texture
 * that will be written with the camera preview frame.
 */
class BackgroundRenderer(private val context: Context) {
    private var quadVertices: FloatBuffer
    private var quadTexCoords: FloatBuffer
    private var quadProgram = 0
    private var quadPositionParam = 0
    private var quadTexCoordParam = 0
    
    // Shader names
    private val vertexShaderCode = """
        attribute vec4 a_Position;
        attribute vec2 a_TexCoord;
        varying vec2 v_TexCoord;
        void main() {
           gl_Position = a_Position;
           v_TexCoord = a_TexCoord;
        }
    """

    private val fragmentShaderCode = """
        #extension GL_OES_EGL_image_external : require
        precision mediump float;
        uniform samplerExternalOES u_Texture;
        varying vec2 v_TexCoord;
        void main() {
            gl_FragColor = texture2D(u_Texture, v_TexCoord);
        }
    """
    
    // Texture
    private var cameraTextureId = -1
    
    init {
        // Create full screen quad
        val vertices = floatArrayOf(
            -1.0f, -1.0f, 0.0f,
            -1.0f, 1.0f, 0.0f,
            1.0f, -1.0f, 0.0f,
            1.0f, 1.0f, 0.0f
        )
        
        // Texture coordinates of the quad
        val texCoords = floatArrayOf(
            0.0f, 1.0f,
            0.0f, 0.0f,
            1.0f, 1.0f,
            1.0f, 0.0f
        )
        
        // Initialize vertex buffer objects
        quadVertices = ByteBuffer.allocateDirect(vertices.size * Float.SIZE_BYTES)
            .order(ByteOrder.nativeOrder())
            .asFloatBuffer()
        quadVertices.put(vertices).position(0)
        
        quadTexCoords = ByteBuffer.allocateDirect(texCoords.size * Float.SIZE_BYTES)
            .order(ByteOrder.nativeOrder())
            .asFloatBuffer()
        quadTexCoords.put(texCoords).position(0)
        
        // Set up shaders and create program
        setupShaders()
        
        // Create texture for camera preview
        createOnGlThread()
    }
    
    private fun setupShaders() {
        val vertexShader = loadShader(GLES30.GL_VERTEX_SHADER, vertexShaderCode)
        val fragmentShader = loadShader(GLES30.GL_FRAGMENT_SHADER, fragmentShaderCode)
        
        quadProgram = GLES30.glCreateProgram()
        GLES30.glAttachShader(quadProgram, vertexShader)
        GLES30.glAttachShader(quadProgram, fragmentShader)
        GLES30.glLinkProgram(quadProgram)
        
        // Get parameters
        quadPositionParam = GLES30.glGetAttribLocation(quadProgram, "a_Position")
        quadTexCoordParam = GLES30.glGetAttribLocation(quadProgram, "a_TexCoord")
        
        // Clean up shaders
        GLES30.glDeleteShader(vertexShader)
        GLES30.glDeleteShader(fragmentShader)
        
        // Check linking
        val linkStatus = IntArray(1)
        GLES30.glGetProgramiv(quadProgram, GLES30.GL_LINK_STATUS, linkStatus, 0)
        if (linkStatus[0] == 0) {
            android.util.Log.e(TAG, "Background shader program failed to link")
            GLES30.glDeleteProgram(quadProgram)
            quadProgram = 0
        }
    }
    
    private fun loadShader(type: Int, shaderCode: String): Int {
        val shader = GLES30.glCreateShader(type)
        GLES30.glShaderSource(shader, shaderCode)
        GLES30.glCompileShader(shader)
        
        // Get compilation status
        val compileStatus = IntArray(1)
        GLES30.glGetShaderiv(shader, GLES30.GL_COMPILE_STATUS, compileStatus, 0)
        if (compileStatus[0] == 0) {
            val infoLog = GLES30.glGetShaderInfoLog(shader)
            android.util.Log.e(TAG, "Shader compilation error: $infoLog")
            GLES30.glDeleteShader(shader)
            return 0
        }
        return shader
    }
    
    /**
     * Creates and initializes texture that will receive the camera preview frame.
     */
    fun createOnGlThread() {
        val textureIds = IntArray(1)
        GLES30.glGenTextures(1, textureIds, 0)
        cameraTextureId = textureIds[0]
        
        GLES30.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, cameraTextureId)
        GLES30.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES30.GL_TEXTURE_WRAP_S, GLES30.GL_CLAMP_TO_EDGE)
        GLES30.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES30.GL_TEXTURE_WRAP_T, GLES30.GL_CLAMP_TO_EDGE)
        GLES30.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES30.GL_TEXTURE_MIN_FILTER, GLES30.GL_LINEAR)
        GLES30.glTexParameteri(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, GLES30.GL_TEXTURE_MAG_FILTER, GLES30.GL_LINEAR)
    }
    
    /**
     * Draws the camera background. This should be called before drawing any AR objects.
     */
    fun draw(frame: Frame) {
        if (cameraTextureId <= 0 || quadProgram <= 0) {
            return
        }
        
        // In ARCore, the camera texture is automatically updated by the Session
        // when we called Session.setCameraTextureName() with our texture ID
        
        // Set the external texture
        GLES30.glActiveTexture(GLES30.GL_TEXTURE0)
        GLES30.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, cameraTextureId)
        
        // Disable depth test and depth write, so the background is always drawn
        GLES30.glDisable(GLES30.GL_DEPTH_TEST)
        GLES30.glDepthMask(false)
        
        // Use program
        GLES30.glUseProgram(quadProgram)
        
        // Set up vertex attributes
        GLES30.glEnableVertexAttribArray(quadPositionParam)
        GLES30.glVertexAttribPointer(
            quadPositionParam, 3, GLES30.GL_FLOAT, false, 0, quadVertices
        )
        
        GLES30.glEnableVertexAttribArray(quadTexCoordParam)
        GLES30.glVertexAttribPointer(
            quadTexCoordParam, 2, GLES30.GL_FLOAT, false, 0, quadTexCoords
        )
        
        // Draw the quad
        GLES30.glDrawArrays(GLES30.GL_TRIANGLE_STRIP, 0, 4)
        
        // Cleanup attributes
        GLES30.glDisableVertexAttribArray(quadPositionParam)
        GLES30.glDisableVertexAttribArray(quadTexCoordParam)
        
        // Re-enable depth test and write
        GLES30.glDepthMask(true)
        GLES30.glEnable(GLES30.GL_DEPTH_TEST)
        
        // Unbind texture
        GLES30.glBindTexture(GLES11Ext.GL_TEXTURE_EXTERNAL_OES, 0)
    }
    
    /**
     * Returns the ID of the camera texture.
     */
    fun getTextureId(): Int {
        return cameraTextureId
    }
    
    /**
     * Releases the resources used by this renderer.
     */
    fun release() {
        if (cameraTextureId != -1) {
            GLES30.glDeleteTextures(1, intArrayOf(cameraTextureId), 0)
            cameraTextureId = -1
        }
        if (quadProgram != 0) {
            GLES30.glDeleteProgram(quadProgram)
            quadProgram = 0
        }
    }

    companion object {
        private const val TAG = "BackgroundRenderer"
    }
}