R_DEF: Internal format may change without notice
local
anim pulse
array grid_formats
array grid_formats_array
array line_thickness_options
color ar_anchor_color
color ar_grid_color
color ar_plane_color
color ar_reticle_color
color background
color black
color buttonDisabled
color buttonNormal
color buttonPressed
color colorAccent
color colorPrimary
color colorPrimaryDark
color error
color gradient_end
color gradient_start
color item_pressed
color item_selected
color navigationBarBackground
color notification_error
color notification_success
color notification_warning
color panel_background
color panel_divider
color statusBarBackground
color surface
color textHint
color textPrimary
color textSecondary
color transparent
color white
dimen main_menu_minimized_width
drawable baseline_grid_on_24
drawable baseline_grid_on_black_18
drawable baseline_grid_on_black_20
drawable baseline_grid_on_black_24
drawable baseline_grid_on_black_36
drawable baseline_grid_on_black_48
drawable baseline_image_20
drawable baseline_image_24
drawable baseline_image_black_18
drawable baseline_image_black_20
drawable baseline_image_black_24
drawable baseline_image_black_36
drawable baseline_image_black_48
drawable circle_shape
drawable deace_logo_sticker
drawable ic_export
drawable ic_grid
drawable ic_grid_horizontal
drawable ic_grid_square
drawable ic_grid_vertical
drawable ic_help
drawable ic_image
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_maximize
drawable ic_minimize
drawable ic_reticle
drawable ic_settings
drawable rounded_button_background
drawable rounded_panel_background
drawable save_alt
drawable selected_button_background
drawable splash_background
drawable toast_background
drawable tracking_progress_background
id advanced_rotation_button
id ar_surface_view
id btn_close_tracking_help
id config_panel_layout
id config_panel_scroll
id config_tab_button
id confirm_reinit_button
id deace_logo
id deace_panel_layout
id default_grid_format_spinner
id default_grid_opacity_seekbar
id default_image_opacity_seekbar
id expanded_image_panel_scroll
id export_button
id export_panel_layout
id export_tab_button
id file_format_radio_group
id filename_pattern_edittext
id fix_grid_button
id fix_image_button
id flip_horizontal_button
id grid_color_button
id grid_line_thickness_spinner
id grid_opacity_seekbar
id grid_opacity_value
id grid_panel_layout
id grid_tab_button
id help_about_button
id help_button
id horizontal_format_button
id image_opacity_seekbar
id image_opacity_value
id image_panel_layout
id image_quality_radio_group
id image_selected_status
id image_size_text
id image_tab_button
id image_thumbnail
id jpg_format_radio
id load_image_button
id loading_layout
id loading_text
id main_menu_layout
id max_quality_radio
id menu_icons_container
id normal_quality_radio
id png_format_radio
id preserve_aspect_switch
id quick_lock_image_button
id release_grid_button
id release_image_button
id reset_session_button
id reset_settings_button
id reticle
id rotate_180_button
id rotate_left_15_button
id rotate_left_90_button
id rotate_right_15_button
id rotate_right_90_button
id rotate_x_minus_15_button
id rotate_x_minus_90_button
id rotate_x_plus_15_button
id rotate_x_plus_90_button
id rotate_y_minus_15_button
id rotate_y_minus_90_button
id rotate_y_plus_15_button
id rotate_y_plus_90_button
id rotate_z_minus_15_button
id rotate_z_minus_90_button
id rotate_z_plus_15_button
id rotate_z_plus_90_button
id save_directory_button
id save_directory_text
id show_planes_switch
id square_format_button
id test_image_placement_button
id toast_text
id tracking_indicator_container
id tracking_init_text
id tracking_progress_bar
id tracking_quality_indicator
id tracking_quality_text
id vertical_format_button
layout activity_main
layout config_panel
layout custom_toast
layout dialog_tracking_help
layout export_panel
layout grid_panel
layout image_panel
mipmap ic_launcher
mipmap ic_launcher_round
string about_text
string app_name
string app_restarting
string app_settings
string ar_session_not_ready
string ar_session_reset
string ar_settings
string arcore_not_supported
string baking_title
string camera_permission_required_message
string cancel
string choose_color
string choose_folder
string config_tab
string confirm_reinit_app
string deace_tab
string default_format
string default_grid_format
string default_opacity
string exit
string export_ar_view
string export_error
string export_instructions
string export_settings
string export_success
string export_tab
string failed_to_capture_image
string failed_to_load_image
string failed_to_save_image
string file_format
string filename_pattern
string fix_grid
string fix_image
string general
string grant_permission
string grid_color
string grid_format
string grid_opacity
string grid_options
string grid_tab
string help
string help_about
string horizontal
string image_load_error
string image_opacity
string image_quality
string image_saved_to
string image_size_format
string image_tab
string initializing_ar
string installing_arcore
string jpg
string jpg_format
string line_thickness
string load_image
string max_quality
string maximum
string minimize_menu
string no_image_loaded
string normal
string normal_quality
string ok
string opacity_format
string opacity_format_50
string opacity_value
string permission_required
string png
string png_format
string preserve_aspect_ratio
string projected_image_options
string prompt_placeholder
string release_grid
string release_image
string requesting_permissions
string reset_ar_session
string reset_session
string reset_settings
string results_placeholder
string save_directory
string scanning_environment
string settings
string settings_reset
string show_planes
string square
string storage_permission_denied
string storage_permission_explanation
string storage_permission_required
string tracking
string tracking_found_surfaces
string tracking_good
string tracking_help_good
string tracking_help_limited
string tracking_help_lost
string tracking_initializing
string tracking_limited
string tracking_looking_for_surfaces
string tracking_lost
string tracking_move_device
string tracking_ready
string vertical
string visualize_detected_planes
style DialogButtonStyle
style Theme.DeaceAR
style Theme.DeaceAR.Dialog
style Theme.SplashScreen
style WhiteSeekBar
style WhiteSwitch
xml backup_rules
xml data_extraction_rules
