package com.deace.deacear.ar

/**
 * Interface to define ARSessionManager methods to fix compiler errors
 */
interface ARSessionManagerInterface {
    fun fixImage()
    fun isSessionPaused(): Boolean
    fun onSessionPaused()
    fun onSessionResumed()
}

/**
 * Extension functions for ARSessionManager to ensure methods are accessible
 */
fun ARSessionManager.fixImageExt() {
    this.fixImage()
}

fun ARSessionManager.isSessionPausedExt(): Boolean {
    return this.isSessionPaused()
}

fun ARSessionManager.onSessionPausedExt() {
    this.onSessionPaused()
}

fun ARSessionManager.onSessionResumedExt() {
    this.onSessionResumed()
}