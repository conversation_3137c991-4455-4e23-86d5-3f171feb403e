<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.deace.deacear"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="29"
        android:targetSdkVersion="35" />

    <!-- AR Core requires camera permission -->
    <uses-permission android:name="android.permission.CAMERA" />
    <!-- Required for saving/loading images -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <!-- AR features require OpenGL ES -->
    <uses-feature
        android:glEsVersion="0x00030000"
        android:required="true" />
    <!-- Indicates AR required -->
    <uses-feature
        android:name="android.hardware.camera.ar"
        android:required="true" />
    <!-- Camera hardware feature -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <queries>
        <package android:name="com.google.ar.core" />
        <package android:name="com.android.vending" />

        <intent>
            <action android:name="com.google.android.play.core.install.BIND_INSTALL_SERVICE" />
        </intent>
    </queries>

    <uses-permission android:name="android.permission.INTERNET" />

    <permission
        android:name="com.deace.deacear.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.deace.deacear.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.DeaceAR" >

        <!-- Required for ARCore -->
        <meta-data
            android:name="com.google.ar.core"
            android:value="required" />

        <activity
            android:name="com.deace.deacear.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.SplashScreen" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.deace.deacear.MainActivity"
            android:configChanges="orientation|screenSize"
            android:exported="false"
            android:theme="@style/Theme.DeaceAR" >
        </activity>
        <activity
            android:name="androidx.compose.ui.tooling.PreviewActivity"
            android:exported="true" />
        <activity
            android:name="androidx.activity.ComponentActivity"
            android:exported="true" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.deace.deacear.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver> <!-- The minimal version code of ARCore APK required for an app using this SDK. -->
        <meta-data
            android:name="com.google.ar.core.min_apk_version"
            android:value="250210000" /> <!-- This activity is critical for installing ARCore when it is not already present. -->
        <activity
            android:name="com.google.ar.core.InstallActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:excludeFromRecents="true"
            android:exported="false"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.Material.Light.Dialog.Alert" />
    </application>

</manifest>