Here's the complete implementation of `FileUtils.kt` that works with the AR Graffiti Projection Assistant project:

```kotlin
package com.example.graffitiar.utils

import android.content.ContentResolver
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Environment
import android.provider.OpenableColumns
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream

class FileUtils(private val context: Context) {

    companion object {
        private const val MAX_IMAGE_DIMENSION = 2048
        private const val COMPRESSION_QUALITY = 90
    }

    fun uriToBitmap(uri: Uri): Bitmap? {
        return try {
            context.contentResolver.openInputStream(uri)?.use { inputStream ->
                decodeSampledBitmapFromStream(inputStream)
            }
        } catch (e: IOException) {
            null
        }
    }

    private fun decodeSampledBitmapFromStream(inputStream: InputStream): Bitmap {
        // First decode with inJustDecodeBounds=true to check dimensions
        val options = BitmapFactory.Options().apply {
            inJustDecodeBounds = true
        }
        BitmapFactory.decodeStream(inputStream, null, options)
        
        // Calculate inSampleSize
        options.inSampleSize = calculateInSampleSize(
            options.outWidth,
            options.outHeight
        )
        
        // Reset the input stream
        inputStream.reset()
        
        // Decode bitmap with inSampleSize set
        options.inJustDecodeBounds = false
        return BitmapFactory.decodeStream(inputStream, null, options)
            ?: throw IOException("Failed to decode bitmap")
    }

    private fun calculateInSampleSize(width: Int, height: Int): Int {
        var inSampleSize = 1
        
        if (height > MAX_IMAGE_DIMENSION || width > MAX_IMAGE_DIMENSION) {
            val halfHeight = height / 2
            val halfWidth = width / 2
            
            // Calculate the largest inSampleSize value that keeps both
            // height and width larger than MAX_IMAGE_DIMENSION
            while (halfHeight / inSampleSize >= MAX_IMAGE_DIMENSION &&
                   halfWidth / inSampleSize >= MAX_IMAGE_DIMENSION) {
                inSampleSize *= 2
            }
        }
        
        return inSampleSize
    }

    fun saveBitmap(bitmap: Bitmap, file: File): Boolean {
        return try {
            FileOutputStream(file).use { out ->
                bitmap.compress(
                    getCompressFormat(file.name),
                    COMPRESSION_QUALITY,
                    out
                )
                true
            }
        } catch (e: IOException) {
            false
        }
    }

    private fun getCompressFormat(filename: String): Bitmap.CompressFormat {
        return when {
            filename.endsWith(".png", ignoreCase = true) -> 
                Bitmap.CompressFormat.PNG
            filename.endsWith(".webp", ignoreCase = true) -> 
                Bitmap.CompressFormat.WEBP
            else -> Bitmap.CompressFormat.JPEG
        }
    }

    fun getFileName(uri: Uri): String? {
        return context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                cursor.getString(
                    cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                )
            } else {
                null
            }
        }
    }

    fun createImageFile(): File {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return File.createTempFile(
            "JPEG_${timeStamp}_",
            ".jpg",
            storageDir
        )
    }

    fun getPublicPicturesDirectory(subfolder: String): File {
        val dir = File(
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES),
            subfolder
        )
        if (!dir.exists()) {
            dir.mkdirs()
        }
        return dir
    }
}
```

### Key Features:

1. **Image Loading**:
   - `uriToBitmap()` safely loads images from content URIs
   - Automatic downsampling of large images to prevent OOM errors
   - Stream-based processing for memory efficiency

2. **Image Saving**:
   - `saveBitmap()` handles different formats (JPEG, PNG, WEBP)
   - Proper file compression with quality control
   - Error handling for storage operations

3. **File Management**:
   - `getFileName()` extracts display names from URIs
   - `createImageFile()` generates unique temporary files
   - `getPublicPicturesDirectory()` manages export locations

4. **Performance Optimizations**:
   - Bitmap sampling calculates optimal dimensions
   - Proper resource cleanup with `use` blocks
   - Configurable maximum dimensions

5. **Safety Features**:
   - Null-safe operations
   - IOException handling
   - Memory-conscious bitmap loading

### Usage Examples:

1. **Loading an image**:
```kotlin
val bitmap = fileUtils.uriToBitmap(selectedImageUri)
```

2. **Saving an image**:
```kotlin
val success = fileUtils.saveBitmap(bitmap, outputFile)
```

3. **Creating export directory**:
```kotlin
val exportDir = fileUtils.getPublicPicturesDirectory("Graffiti_AR")
```

4. **Getting file information**:
```kotlin
val fileName = fileUtils.getFileName(contentUri)
```

### Integration Notes:

1. Add these permissions to AndroidManifest.xml:
```xml
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

2. For Android 10+ (API 29+), add this to the application tag:
```xml
android:requestLegacyExternalStorage="true"
```

3. Recommended to initialize in Application class:
```kotlin
// In your Application class
val fileUtils by lazy { FileUtils(applicationContext) }
```

This implementation provides robust file handling capabilities while maintaining memory efficiency and safety - crucial for an AR application that handles potentially large image files. The class is designed to work seamlessly with the other components in your Graffiti AR Projection Assistant.