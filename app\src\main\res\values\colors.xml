<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Material Design Color Palette -->
    <color name="colorPrimary">#6200EE</color>  <!-- Purple 500 -->
    <color name="colorPrimaryDark">#3700B3</color>  <!-- Purple 700 -->
    <color name="colorAccent">#03DAC6</color>  <!-- Teal 200 -->
    
    <!-- Status/Navigation Bar Colors -->
    <color name="statusBarBackground">@color/colorPrimaryDark</color>
    <color name="navigationBarBackground">@color/black</color>
    
    <!-- Background Colors -->
    <color name="background">#121212</color>  <!-- Dark background for AR view -->
    <color name="surface">#1E1E1E</color>  <!-- Cards/dialogs -->
    <color name="error">#CF6679</color>  <!-- Error color -->
    
    <!-- Text Colors -->
    <color name="textPrimary">#FFFFFF</color>
    <color name="textSecondary">#B3B3B3</color>
    <color name="textHint">#666666</color>
    
    <!-- UI Element Colors -->
    <color name="buttonNormal">@color/colorPrimary</color>
    <color name="buttonPressed">#7F3FD4</color>  <!-- Lighter purple -->
    <color name="buttonDisabled">#424242</color>
    
    <!-- AR Specific Colors -->
    <color name="ar_grid_color">#4DFFFFFF</color>  <!-- White grid with 30% opacity -->
    <color name="ar_plane_color">#4D00E5FF</color>  <!-- Cyan plane with 30% opacity -->
    <color name="ar_reticle_color">#FF4081</color>  <!-- Pink reticle -->
    <color name="ar_anchor_color">#FFD600</color>  <!-- Yellow anchor -->
    
    <!-- Standard Colors -->
    <color name="white">#FFFFFFFF</color>
    <color name="black">#FF000000</color>
    <color name="transparent">#00000000</color>
    
    <!-- Notification Colors -->
    <color name="notification_success">#4CAF50</color>  <!-- Green -->
    <color name="notification_warning">#FFC107</color>  <!-- Amber -->
    <color name="notification_error">#F44336</color>  <!-- Red -->
    
    <!-- Gradient Colors (for possible future use) -->
    <color name="gradient_start">@color/colorPrimary</color>
    <color name="gradient_end">@color/colorAccent</color>
    
    <!-- Panel Backgrounds -->
    <color name="panel_background">#CC1E1E1E</color>  <!-- Semi-transparent dark -->
    <color name="panel_divider">#33FFFFFF</color>  <!-- Very light divider -->
    
    <!-- Selection States -->
    <color name="item_selected">#33FFFFFF</color>  <!-- White with 20% opacity -->
    <color name="item_pressed">#1AFFFFFF</color>  <!-- White with 10% opacity -->
</resources>