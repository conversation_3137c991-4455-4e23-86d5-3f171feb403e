package com.deace.deacear.utils

import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import android.widget.Toast
import com.deace.deacear.R

/**
 * Utility class for displaying messages to the user with improved visibility
 */
object MessageUtils {

    /**
     * Shows a custom toast message with improved visibility
     *
     * @param context The context to use for creating the toast
     * @param message The message to display
     * @param duration The duration to show the toast (Toast.LENGTH_SHORT or Toast.LENGTH_LONG)
     */
    fun showToast(context: Context, message: String, duration: Int = Toast.LENGTH_LONG) {
        try {
            // Create a custom toast layout
            val inflater = LayoutInflater.from(context)
            val layout = inflater.inflate(R.layout.custom_toast, null)

            // Get the text view from the layout
            val textView = layout.findViewById<TextView>(R.id.toast_text)

            // Set the message
            textView.text = message

            // Create and show the toast
            val toast = Toast(context)
            toast.setGravity(Gravity.CENTER_HORIZONTAL or Gravity.BOTTOM, 0, 150)
            toast.duration = duration
            toast.view = layout
            toast.show()
        } catch (e: Exception) {
            // Fallback to standard toast if custom implementation fails
            Toast.makeText(context, message, duration).show()
        }
    }

    /**
     * Shows a status message in a TextView with improved visibility
     *
     * @param textView The TextView to display the message in
     * @param message The message to display
     */
    fun showStatusMessage(textView: TextView, message: String) {
        textView.text = message

        // Ensure the text view is visible
        textView.visibility = View.VISIBLE

        // Increase text size for better visibility
        textView.textSize = 14f
    }
}
