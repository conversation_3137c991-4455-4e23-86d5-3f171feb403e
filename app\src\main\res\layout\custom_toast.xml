<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/toast_background"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:id="@+id/toast_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:maxWidth="300dp"
        android:textColor="#FFFFFF"
        android:textSize="16sp" />

</LinearLayout>
