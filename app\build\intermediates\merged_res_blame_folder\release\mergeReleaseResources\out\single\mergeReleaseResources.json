[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xhdpi_baseline_grid_on_black_20.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xhdpi\\baseline_grid_on_black_20.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxxhdpi_baseline_grid_on_black_36.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxxhdpi\\baseline_grid_on_black_36.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xhdpi_baseline_image_black_20.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xhdpi\\baseline_image_black_20.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-mdpi_baseline_image_black_20.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-mdpi\\baseline_image_black_20.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\layout_export_panel.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\layout\\export_panel.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_save_alt.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\save_alt.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xhdpi_baseline_image_black_36.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xhdpi\\baseline_image_black_36.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxhdpi_baseline_image_black_36.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxhdpi\\baseline_image_black_36.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-hdpi_baseline_grid_on_black_20.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-hdpi\\baseline_grid_on_black_20.png"}, {"merged": "com.deace.deacear.app-release-58:/layout_image_panel.xml.flat", "source": "com.deace.deacear.app-main-59:/layout/image_panel.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_selected_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\selected_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-hdpi_baseline_grid_on_black_18.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-hdpi\\baseline_grid_on_black_18.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-hdpi_baseline_image_black_24.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-hdpi\\baseline_image_black_24.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\mipmap-anydpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\mipmap-anydpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_ic_grid_vertical.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\ic_grid_vertical.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_rounded_panel_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\rounded_panel_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xhdpi_baseline_grid_on_black_36.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xhdpi\\baseline_grid_on_black_36.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-mdpi_baseline_image_black_36.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-mdpi\\baseline_image_black_36.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-mdpi_baseline_grid_on_black_24.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-mdpi\\baseline_grid_on_black_24.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxhdpi_baseline_grid_on_black_36.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxhdpi\\baseline_grid_on_black_36.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-mdpi_baseline_grid_on_black_18.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-mdpi\\baseline_grid_on_black_18.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\layout_config_panel.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\layout\\config_panel.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxxhdpi_baseline_image_black_20.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxxhdpi\\baseline_image_black_20.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\layout_dialog_tracking_help.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\layout\\dialog_tracking_help.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\layout_custom_toast.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\layout\\custom_toast.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xhdpi_baseline_grid_on_black_48.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xhdpi\\baseline_grid_on_black_48.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-hdpi_baseline_image_black_36.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-hdpi\\baseline_image_black_36.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-mdpi_baseline_image_black_48.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-mdpi\\baseline_image_black_48.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_ic_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\ic_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xhdpi_baseline_grid_on_black_18.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xhdpi\\baseline_grid_on_black_18.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_ic_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\ic_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\anim_pulse.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\anim\\pulse.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxxhdpi_baseline_grid_on_black_20.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxxhdpi\\baseline_grid_on_black_20.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-hdpi_baseline_grid_on_black_48.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-hdpi\\baseline_grid_on_black_48.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_toast_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\toast_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-mdpi_baseline_grid_on_black_48.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-mdpi\\baseline_grid_on_black_48.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-mdpi_baseline_image_black_18.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-mdpi\\baseline_image_black_18.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxhdpi_baseline_image_black_18.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxhdpi\\baseline_image_black_18.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_ic_export.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\ic_export.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxhdpi_baseline_image_black_20.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxhdpi\\baseline_image_black_20.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_ic_maximize.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\ic_maximize.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxxhdpi_baseline_image_black_36.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxxhdpi\\baseline_image_black_36.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xhdpi_baseline_image_black_48.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xhdpi\\baseline_image_black_48.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\layout_grid_panel.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\layout\\grid_panel.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_baseline_image_20.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\baseline_image_20.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxhdpi_baseline_grid_on_black_18.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxhdpi\\baseline_grid_on_black_18.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-mdpi_baseline_grid_on_black_36.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-mdpi\\baseline_grid_on_black_36.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxxhdpi_baseline_image_black_48.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxxhdpi\\baseline_image_black_48.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_ic_grid.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\ic_grid.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xhdpi_baseline_grid_on_black_24.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xhdpi\\baseline_grid_on_black_24.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxxhdpi_baseline_image_black_18.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxxhdpi\\baseline_image_black_18.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxhdpi_baseline_grid_on_black_48.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxhdpi\\baseline_grid_on_black_48.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-hdpi_baseline_image_black_20.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-hdpi\\baseline_image_black_20.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_ic_minimize.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\ic_minimize.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxhdpi_baseline_image_black_48.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxhdpi\\baseline_image_black_48.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\mipmap-anydpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\mipmap-anydpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-hdpi_baseline_grid_on_black_24.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-hdpi\\baseline_grid_on_black_24.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_ic_reticle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\ic_reticle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxhdpi_baseline_grid_on_black_20.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxhdpi\\baseline_grid_on_black_20.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-hdpi_baseline_image_black_18.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-hdpi\\baseline_image_black_18.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_rounded_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\rounded_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxxhdpi_baseline_grid_on_black_24.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxxhdpi\\baseline_grid_on_black_24.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xhdpi_baseline_image_black_18.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xhdpi\\baseline_image_black_18.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxxhdpi_baseline_grid_on_black_18.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxxhdpi\\baseline_grid_on_black_18.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xhdpi_baseline_image_black_24.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xhdpi\\baseline_image_black_24.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_baseline_grid_on_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\baseline_grid_on_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_tracking_progress_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\tracking_progress_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-hdpi_baseline_grid_on_black_36.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-hdpi\\baseline_grid_on_black_36.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\layout_image_panel.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\layout\\image_panel.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_ic_help.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\ic_help.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxxhdpi_baseline_image_black_24.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxxhdpi\\baseline_image_black_24.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-hdpi_baseline_image_black_48.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-hdpi\\baseline_image_black_48.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_baseline_image_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\baseline_image_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_splash_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\splash_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-mdpi_baseline_grid_on_black_20.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-mdpi\\baseline_grid_on_black_20.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxhdpi_baseline_grid_on_black_24.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxhdpi\\baseline_grid_on_black_24.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_ic_grid_square.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\ic_grid_square.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-mdpi_baseline_image_black_24.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-mdpi\\baseline_image_black_24.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_deace_logo_sticker.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\deace_logo_sticker.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_ic_grid_horizontal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\ic_grid_horizontal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxxhdpi_baseline_grid_on_black_48.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxxhdpi\\baseline_grid_on_black_48.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable_circle_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable\\circle_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-release-58:\\drawable-xxhdpi_baseline_image_black_24.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.deace.deacear.app-main-59:\\drawable-xxhdpi\\baseline_image_black_24.png"}]