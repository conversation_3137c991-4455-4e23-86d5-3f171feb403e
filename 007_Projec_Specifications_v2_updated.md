# **DeaceAR - Refined Specification**

**Version:** 1.1
**Date:** May 19, 2025
**Project:** AR Graffiti Projection Assistant

---

## 🎯 **1. Core Functionality & Vision**

- **Objective:** An Android application that uses ARCore to accurately project user-selected images onto real-world vertical surfaces (walls), assisting artists in visualizing their designs before painting.

- **Workflow:**
  1. **Setup Phase:** User scans a wall with the camera, selects an image, positions it on the detected wall surface
  2. **Painting Phase:** User approaches the wall with phone in one hand, using the AR projection as a guide while painting with the other hand

- **Target Users:** Graffiti artists, muralists, and other visual artists working on large vertical surfaces

---

## 📱 **2. Technical Requirements**

- **Development Environment:** Android Studio
- **Core Technology:** ARCore SDK with Kotlin
- **Rendering:** Filament or OpenGL for modern approach
- **Minimum Android Version:** Android 8.0+ (API level 26) with ARCore support
- **Camera Permission:** Required at launch
- **Storage Permission:** Required for image selection and export

---

## 🔄 **3. User Experience Flow**

### **3.1 App Launch & Initialization**

1. Launch directly into AR camera view (no intermediate menus)
2. Request necessary permissions (camera, storage) if not granted
3. Display brief onboarding overlay with instructions: "Scan the wall by moving your phone slowly from side to side"
4. Begin ARCore initialization and plane detection

### **3.2 Setup Phase (Wall Detection & Image Placement)**

1. **Wall Detection:**
   - Continuously scan environment focusing on vertical plane detection
   - Provide subtle visual feedback on detected vertical planes using semi-transparent overlay
   - Display status messages guiding the user to move the device if detection is slow
   - Clearly differentiate between horizontal and vertical surface detection in messages

2. **Image Selection:**
   - Enable "Select Image" button once a suitable vertical plane is detected
   - Open device's image gallery through standard Android Storage Access Framework (SAF)
   - Support common image formats: JPG, PNG, WEBP
   - Provide clear feedback when an image is successfully loaded

3. **Image Placement:**
   - Initially place selected image centered on the detected wall plane
   - Apply default transparency (70% opaque) for better wall visibility
   - Scale image appropriately to fit within reasonable dimensions while maintaining aspect ratio
   - Implement improved tap-to-place mechanism with synthetic hit generation when direct hit tests fail
   - Show clear instructions for the user to tap on a highlighted vertical surface
   - Provide immediate visual feedback when image is placed

4. **Image Manipulation:**
   - **Move:** One-finger drag to reposition image across wall surface
   - **Scale:** Two-finger pinch to resize (with option to lock/unlock aspect ratio)
   - **Rotate:** Two-finger rotation gesture to adjust orientation
   - **Adjust:** Controls for transparency, brightness, and contrast
   - Ensure consistent and reliable response to gestures

5. **Position Locking:**
   - Prominent "Lock Image" button creates an ARCore Anchor
   - Anchor firmly attaches the image to its real-world position
   - Transition to Painting Phase UI
   - Provide clear feedback when image position is locked

### **3.3 Painting Phase (Active Use)**

1. **Minimalist Interface:**
   - Maximum view of the wall and projected image
   - Auto-hiding controls to avoid obstruction
   - Stable tracking of anchored image regardless of device movement

2. **Essential Controls:**
   - Transparency slider for real-time opacity adjustment
   - Hide/Show toggle to quickly check painting progress
   - Tracking quality indicator
   - Exit/Back button

3. **Tracking Management:**
   - Visual indication of tracking quality (green/yellow/red)
   - Recovery guidance if tracking is lost
   - Option to reset tracking if necessary
   - Consistent messaging aligned with visual indicators

4. **Session End:**
   - Option to save the current view as an image
   - Return to setup phase or exit the application

---

## 📲 **4. User Interface Details**

### **4.1 Main AR View (Common to Both Phases)**

- Full-screen AR camera view showing the real world
- Status bar with:
  - Tracking indicator (colored dot: green/yellow/red)
  - Battery level (optional but recommended for long sessions)
  - Current time (optional)
- System back gesture/button returns to previous phase or exits app

### **4.2 Setup Phase UI Elements**

- **Scanning Guidance:**
  - Initial overlay with animation showing correct scanning motion
  - Text instruction: "Point your device at a wall and move slowly"
  - Progress indicator showing scanning status

- **Plane Visualization:**
  - Semi-transparent colored overlay on detected vertical planes
  - Visual distinction for the currently selected/active plane

- **Action Button:**
  - Floating Action Button (FAB) at bottom-center
  - Initially shows "scanning" state (animated)
  - Changes to "select image" icon once vertical plane detection is stable
  - Tapping opens image picker

- **Image Manipulation Controls:**
  - Bounding box or handles around selected image during manipulation
  - Small tooltips explaining available gestures on first use
  - Image adjustment panel (accessible via icon):
    - Opacity slider (0-100%)
    - Brightness slider (0-200%, with 100% as default)
    - Contrast slider (0-200%, with 100% as default)
    - "Reset" button to restore default values
    - Option to lock/unlock aspect ratio

- **Transition Button:**
  - Prominent "Lock Image" button appears once image is placed
  - Clear visual feedback when pressed (animation/haptic)

### **4.3 Tracking Quality Indicator**

- **Enhanced Visual Feedback:**
  - Color-coded indicator (green/yellow/red)
  - Only shows green when vertical surfaces are actually detected
  - Yellow indicates tracking but no vertical surfaces
  - Red indicates tracking loss or initialization
  - Contextual messages that match the visual state

- **Detailed Messages:**
  - "Vertical wall detected - tap on it to place your image" (green)
  - "Surface detected - keep scanning to find vertical walls" (yellow)
  - "Continue scanning to find more surfaces" (initialization/limited tracking)
  - "Move your device slowly to scan walls and floors" (tracking lost)

- **Progress Indicator:**
  - Shows scanning progress during initialization
  - Clear indication when vertical surfaces are detected

### **4.4 Contextual Help & Message Display System**

- Subtle "?" icon in a corner to access quick tips
- First-time-use tooltips explaining key interactions
- Error messages with recovery suggestions
- **Enhanced Message Display System:**
  - Custom message display boxes with increased visibility
  - Larger text size (16sp) for better readability
  - Semi-transparent dark background with rounded corners
  - Maximum width constraint to ensure full message visibility
  - Centered positioning for optimal viewing
  - Consistent styling across all application messages
  - Replaces standard Android Toast messages for improved user experience

---

## 🎚️ **5. Technical Implementation Details**

### **5.1 ARCore Integration**

- **Camera Usage:**
  - Use ARCore's CameraConfig for optimal frame rate and resolution
  - Implement proper camera permission handling and lifecycle management

- **Plane Detection:**
  - Focus specifically on vertical plane detection (`Plane.Type.VERTICAL`)
  - Implement filtering to prioritize wall-sized planes over small surfaces
  - Set minimum plane size requirements (e.g., at least 1m × 1m)
  - Clearly differentiate between horizontal and vertical surfaces in feedback

- **Anchor Creation:**
  - Create robust anchors once image position is locked
  - Handle anchor updates and maintenance during tracking
  - Implement proper anchor lifecycle management
  - Use enhanced hit testing with fallback mechanisms for reliable image placement
  - Support synthetic hit generation when direct hits aren't found but planes exist
  - Automatic placement using closest vertical plane when direct hit test fails

- **Tracking Management:**
  - Monitor tracking state continuously
  - Implement recovery strategies for lost tracking
  - Provide clear user feedback about tracking quality
  - Stabilized tracking indicator with debouncing to prevent flickering
  - Optimized animation handling for smooth visual feedback
  - State tracking with appropriate transition timing
  - Ensure tracking messages are consistent with visual indicators

### **5.2 Image Handling**

- **Loading:**
  - Implement efficient image loading with proper size constraints
  - Support downsampling for large images
  - Handle memory management for image textures
  - Provide clear feedback when image loading completes

- **Rendering:**
  - Apply proper texture filtering for image quality
  - Support transparency and blending
  - Implement shader effects for brightness/contrast adjustments
  - Ensure proper synchronization between component states

- **Transformation:**
  - Maintain proper aspect ratio during scaling (when locked)
  - Apply smooth transitions during manipulation
  - Ensure image stays properly anchored to the detected plane

### **5.3 User Interaction**

- **Touch Handling:**
  - Implement multi-touch gesture recognition
  - Differentiate between UI interaction and AR content manipulation
  - Apply appropriate sensitivity and thresholds
  - Enhanced hit testing with multi-point sampling for improved accuracy
  - Prioritized hit result processing (vertical planes → horizontal planes → any hit)
  - Fallback mechanisms to ensure successful image placement in challenging environments
  - Synthetic hit generation for more reliable tap-to-place functionality

- **UI Responsiveness:**
  - Ensure smooth slider operation and immediate visual feedback
  - Implement debouncing for rapid adjustments
  - Use animations for UI transitions (appear/disappear)

- **Message Display System:**
  - Custom MessageUtils class for consistent message presentation
  - Enhanced visibility with larger text size and contrasting background
  - Proper handling of longer messages without truncation
  - Centralized message management for consistent user experience
  - Graceful fallback to standard Toast messages if custom implementation fails

### **5.4 Performance Optimization**

- **Rendering Efficiency:**
  - Optimize draw calls and texture resolution
  - Use appropriate level of detail based on device capabilities
  - Implement frame rate management strategies

- **Battery Considerations:**
  - Monitor and optimize CPU/GPU usage
  - Implement frame rate throttling when appropriate
  - Add battery level warnings for extended sessions

### **5.5 Error Handling**

- **Tracking Issues:**
  - Detect and communicate tracking problems
  - Implement recovery guidance with clear instructions
  - Preserve image position data even during temporary tracking loss
  - Provide specific guidance based on tracking state

- **Resource Management:**
  - Handle out-of-memory situations gracefully
  - Implement proper cleanup when switching between app phases
  - Release resources when app is paused or stopped

### **5.6 Debug Logging**

- **Enhanced Logging System:**
  - Detailed logging of tracking state and plane detection
  - Comprehensive hit test analysis
  - Image loading and placement diagnostics
  - Component synchronization verification
  - Categorized log messages for easier troubleshooting

---

## 📸 **6. Export Functionality**

- **Capture Mechanism:**
  - Implement screenshot capability that captures AR view without UI elements
  - Maintain original camera resolution when possible

- **Export Options:**
  - Format: JPG (smaller) or PNG (higher quality)
  - Resolution: Device screen resolution or higher if supported
  - Naming: "DeaceAR_YYYYMMDD_HHMMSS" format with auto-increment

- **Storage:**
  - Save to dedicated app folder within Pictures directory
  - Implement proper media scanning to make exports visible in gallery apps
  - Follow modern Android storage practices (Scoped Storage)

---

## 🔧 **7. Configuration Options**

- **Image Defaults:**
  - Initial opacity: 70%
  - Aspect ratio locking: On by default
  - Default scale relative to detected plane

- **Export Settings:**
  - Image format (JPG/PNG)
  - Resolution quality
  - Default save location

- **Appearance:**
  - Plane visualization style and color
  - UI theme (light/dark following system)
  - Optional grid overlay (can be enabled in settings if desired)

- **Performance:**
  - Quality vs performance balance options
  - Frame rate targets

---

## 🚀 **8. Implementation Priorities**

### **8.1 Core MVP Features (Phase 1)**
1. Basic ARCore vertical plane detection
2. Image selection and projection onto wall
3. Fundamental manipulation (move, scale, rotate)
4. Position locking with anchors
5. Basic opacity control
6. Minimal but functional UI

### **8.2 Enhanced Features (Phase 2)**
1. Improved visual feedback during scanning
2. Image adjustments (brightness, contrast)
3. Auto-hiding UI elements
4. Export functionality
5. Tracking quality indicators
6. Enhanced error handling

### **8.3 Future Enhancements (Post-Launch)**
1. Save/restore AR sessions
2. Multiple image support
3. Edge detection/tracing assist
4. Video recording option
5. Cloud anchors for multi-device collaboration
6. Reference grid with customization options

---

## 📝 **9. Development Notes**

- **Architecture:**
  - Follow Model-View-ViewModel (MVVM) pattern
  - Separate AR functionality from UI logic
  - Use Android Architecture Components (LiveData, ViewModel)
  - Implement proper listener pattern for communication between components

- **Testing:**
  - Test on various device form factors and Android versions
  - Verify performance on lower-end ARCore compatible devices
  - Validate tracking quality in different lighting conditions

- **Documentation:**
  - Include in-app help and first-use guidance
  - Provide clear attribution: "DeaceAR v1.0 ©DEACE: Auteur membre de l'ADAGP. Une autorisation est nécessaire pour toute utilisation des œuvres (www.adagp.fr). More on www.deace.com"

---

## 👤 **10. User Permissions and Privacy**

- **Required Permissions:**
  - Camera: For AR functionality
  - Storage: For image selection and saving exports

- **Optional Permissions:**
  - Location: Only if implementing location-based features in future

- **Privacy Considerations:**
  - No data collection beyond app functionality
  - No network connectivity required for core features
  - Clear privacy policy accessible from settings

---

This specification includes the latest technical improvements to ensure reliable image placement and tracking. The focus on clear user feedback and enhanced error recovery makes the application more robust in real-world usage scenarios.