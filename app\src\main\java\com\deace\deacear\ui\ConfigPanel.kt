package com.deace.deacear.ui

import android.app.AlertDialog
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.widget.SwitchCompat
import com.deace.deacear.R
import com.deace.deacear.ar.ARSessionManager
import com.deace.deacear.utils.MessageUtils
import com.deace.deacear.utils.SettingsManager

class ConfigPanel(
    private val panelLayout: LinearLayout,
    private val context: Context,
    private val settingsManager: SettingsManager
) {
    // Reference to the parent ScrollView that contains the panel layout
    private val scrollView: ScrollView = (panelLayout.parent as? ScrollView)
        ?: throw IllegalArgumentException("ConfigPanel's LinearLayout must be inside a ScrollView")

    // Auto-hide timer constants and components
    private val AUTO_HIDE_DELAY_MS = 5000L // 5 seconds
    private val handler = Handler(Looper.getMainLooper())
    private val hideRunnable = Runnable {
        hide()
        Log.d("ConfigPanel", "Auto-hiding config panel after inactivity")
    }

    // --- Views ---
    private lateinit var resetSessionButton: Button
    private lateinit var defaultGridFormatSpinner: Spinner
    private lateinit var defaultGridOpacitySeekBar: SeekBar
    private lateinit var imageQualityRadioGroup: RadioGroup
    private lateinit var fileFormatRadioGroup: RadioGroup
    private lateinit var resetSettingsButton: Button
    private lateinit var helpAboutButton: Button

    // --- Dependencies ---
    private var arSessionManager: ARSessionManager? = null

    init {
        Log.d("ConfigPanel", "Initializing...")

        try {
            // Initialize views directly without any delay
            if (initializeViews()) {
                Log.d("ConfigPanel", "Views initialized, setting up listeners...")
                setupListeners()
                Log.d("ConfigPanel", "Configuration panel initialized successfully")
            } else {
                Log.e("ConfigPanel", "View initialization failed.")
            }
        } catch (e: Exception) {
            Log.e("ConfigPanel", "Error during initialization", e)
        }
    }

    private fun initializeViews(): Boolean {
        try {
            resetSessionButton = panelLayout.findViewById(R.id.reset_session_button)
            defaultGridFormatSpinner = panelLayout.findViewById(R.id.default_grid_format_spinner)
            defaultGridOpacitySeekBar = panelLayout.findViewById(R.id.default_grid_opacity_seekbar)
            imageQualityRadioGroup = panelLayout.findViewById(R.id.image_quality_radio_group)
            fileFormatRadioGroup = panelLayout.findViewById(R.id.file_format_radio_group)
            resetSettingsButton = panelLayout.findViewById(R.id.reset_settings_button)
            helpAboutButton = panelLayout.findViewById(R.id.help_about_button)

            // Set up spinner adapter for grid format
            val gridFormatAdapter = ArrayAdapter.createFromResource(
                context,
                R.array.grid_formats_array,
                android.R.layout.simple_spinner_item
            )
            gridFormatAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            defaultGridFormatSpinner.adapter = gridFormatAdapter

            return true
        } catch (e: Exception) {
            Log.e("ConfigPanel", "Error finding views in ConfigPanel layout.", e)
            return false
        }
    }


    private fun setupListeners() {
        // Check if views are initialized before setting up listeners
        if (!::resetSessionButton.isInitialized ||
            !::defaultGridFormatSpinner.isInitialized ||
            !::defaultGridOpacitySeekBar.isInitialized ||
            !::imageQualityRadioGroup.isInitialized ||
            !::fileFormatRadioGroup.isInitialized ||
            !::resetSettingsButton.isInitialized ||
            !::helpAboutButton.isInitialized) {
            Log.e("ConfigPanel", "setupListeners called before views were initialized")
            return
        }

        resetSessionButton.setOnClickListener {
            resetAutoHideTimer() // Reset auto-hide timer on user interaction
            arSessionManager?.resetARSession()
            MessageUtils.showToast(context, context.getString(R.string.ar_session_reset), Toast.LENGTH_SHORT)
        }

        defaultGridFormatSpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                resetAutoHideTimer() // Reset auto-hide timer on user interaction
                settingsManager.defaultGridFormat = position
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        defaultGridOpacitySeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    resetAutoHideTimer() // Reset auto-hide timer on user interaction
                    settingsManager.defaultGridOpacity = progress / 100f
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                resetAutoHideTimer() // Reset auto-hide timer on user interaction
            }
            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                resetAutoHideTimer() // Reset auto-hide timer on user interaction
            }
        })

        imageQualityRadioGroup.setOnCheckedChangeListener { _, checkedId ->
            resetAutoHideTimer() // Reset auto-hide timer on user interaction
            settingsManager.imageQuality = when (checkedId) {
                R.id.normal_quality_radio -> SettingsManager.IMAGE_QUALITY_NORMAL
                else -> SettingsManager.IMAGE_QUALITY_MAX
            }
        }

        fileFormatRadioGroup.setOnCheckedChangeListener { _, checkedId ->
            resetAutoHideTimer() // Reset auto-hide timer on user interaction
            settingsManager.fileFormat = when (checkedId) {
                R.id.png_format_radio -> SettingsManager.FILE_FORMAT_PNG
                else -> SettingsManager.FILE_FORMAT_JPG
            }
        }

        resetSettingsButton.setOnClickListener {
            resetAutoHideTimer() // Reset auto-hide timer on user interaction
            resetToDefaultSettings()
        }

        helpAboutButton.setOnClickListener {
            resetAutoHideTimer() // Reset auto-hide timer on user interaction
            showAboutDialog()
        }
    }


    /** Loads settings from SettingsManager and updates the UI controls */
    private fun loadCurrentSettings() {
        Log.d("ConfigPanel", "Loading current settings into UI.")

        // Check if all required views are initialized before proceeding
        if (!::defaultGridFormatSpinner.isInitialized ||
            !::defaultGridOpacitySeekBar.isInitialized ||
            !::imageQualityRadioGroup.isInitialized ||
            !::fileFormatRadioGroup.isInitialized) {

            Log.e("ConfigPanel", "loadCurrentSettings called before views were initialized")
            return
        }

        try {
            // Grid Options
            defaultGridFormatSpinner.setSelection(settingsManager.defaultGridFormat.coerceIn(0, defaultGridFormatSpinner.adapter.count - 1))
            defaultGridOpacitySeekBar.progress = (settingsManager.defaultGridOpacity * 100).toInt()

            // Export Settings
            imageQualityRadioGroup.check(
                when (settingsManager.imageQuality) {
                    SettingsManager.IMAGE_QUALITY_NORMAL -> R.id.normal_quality_radio
                    else -> R.id.max_quality_radio
                }
            )

            fileFormatRadioGroup.check(
                when (settingsManager.fileFormat) {
                    SettingsManager.FILE_FORMAT_PNG -> R.id.png_format_radio
                    else -> R.id.jpg_format_radio
                }
            )

        } catch (e: Exception) {
            Log.e("ConfigPanel", "Error loading settings into UI", e)
        }
    }

    /** Resets SettingsManager and reloads UI */
    private fun resetToDefaultSettings() {
        settingsManager.resetToDefaults()
        loadCurrentSettings() // Update UI to reflect defaults
        MessageUtils.showToast(context, context.getString(R.string.settings_reset), Toast.LENGTH_SHORT)
    }

    /** Shows a simple About dialog */
    private fun showAboutDialog() {
        AlertDialog.Builder(context)
            .setTitle(R.string.help_about)
            .setMessage(R.string.about_text)
            .setPositiveButton(R.string.ok, null)
            .show()
    }

    /** Sets the ARSessionManager dependency */
    fun setARSessionManager(manager: ARSessionManager) {
        this.arSessionManager = manager

        // Load current settings into UI
        loadCurrentSettings()
    }

    /** Shows the panel */
    fun show() {
        // Optional: Reload settings when shown in case they changed elsewhere
        // loadCurrentSettings()
        scrollView.visibility = View.VISIBLE
        resetAutoHideTimer()
        Log.d("ConfigPanel", "ConfigPanel shown.")
    }

    /** Hides the panel */
    fun hide() {
        scrollView.visibility = View.GONE
        cancelAutoHideTimer()
        Log.d("ConfigPanel", "ConfigPanel hidden.")
    }

    /**
     * Resets the auto-hide timer
     */
    fun resetAutoHideTimer() {
        // Cancel any existing timer
        cancelAutoHideTimer()
        // Start a new timer
        handler.postDelayed(hideRunnable, AUTO_HIDE_DELAY_MS)
        Log.d("ConfigPanel", "Auto-hide timer reset. Panel will hide in ${AUTO_HIDE_DELAY_MS/1000} seconds if no interaction")
    }

    /**
     * Cancels the auto-hide timer
     */
    private fun cancelAutoHideTimer() {
        handler.removeCallbacks(hideRunnable)
    }

    /**
     * Returns whether this panel is currently active (visible or should be visible)
     */
    fun isActive(): Boolean {
        return scrollView.visibility == View.VISIBLE
    }
}