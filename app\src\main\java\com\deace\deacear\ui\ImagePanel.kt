package com.deace.deacear.ui

import android.content.Context // Use regular Context for resources, etc.
import android.content.Intent
import android.graphics.Bitmap // Import Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import android.provider.OpenableColumns
import android.util.Log // Import Log
import android.view.View
import android.widget.*
import androidx.appcompat.widget.SwitchCompat
import com.deace.deacear.R
import com.deace.deacear.ar.ARSessionManager
// Assuming ARImage exists and has setOpacity, getOpacity, getPreserveAspectRatio
import com.deace.deacear.ar.objects.ARImage // Example import
import com.deace.deacear.utils.FileUtils // Ensure this utility exists and is imported
import com.deace.deacear.utils.MessageUtils
import com.deace.deacear.utils.PermissionHelper
import com.deace.deacear.utils.SettingsManager
import com.deace.deacear.MainActivity

// Define a typealias for the function that will launch the intent
typealias IntentLauncherCallback = (Intent) -> Unit

class ImagePanel(
    private val panelLayout: LinearLayout,
    private val context: Context, // Now just Context
    private val settingsManager: SettingsManager,
    private val requestImageLauncher: IntentLauncherCallback // Callback to MainActivity
) {
    // --- Views ---
    private lateinit var loadImageButton: Button
    private lateinit var fixImageButton: Button
    private lateinit var releaseImageButton: Button
    private lateinit var testImagePlacementButton: Button
    private lateinit var opacitySeekBar: SeekBar
    private lateinit var opacityValueText: TextView
    private lateinit var preserveAspectSwitch: SwitchCompat
    private lateinit var imageSizeText: TextView
    private lateinit var imageSelectedStatus: TextView
    private lateinit var imageThumbnail: ImageView

    // Rotation buttons - Z-axis (original)
    private lateinit var rotateLeft90Button: Button
    private lateinit var rotateRight90Button: Button
    private lateinit var rotate180Button: Button
    private lateinit var rotateLeft15Button: Button
    private lateinit var rotateRight15Button: Button
    private lateinit var flipHorizontalButton: Button

    // Rotation buttons - X-axis (pitch)
    private lateinit var rotateXPlus15Button: Button
    private lateinit var rotateXMinus15Button: Button
    private lateinit var rotateXPlus90Button: Button
    private lateinit var rotateXMinus90Button: Button

    // Rotation buttons - Y-axis (yaw)
    private lateinit var rotateYPlus15Button: Button
    private lateinit var rotateYMinus15Button: Button
    private lateinit var rotateYPlus90Button: Button
    private lateinit var rotateYMinus90Button: Button

    // Rotation buttons - Z-axis (roll) - additional controls
    private lateinit var rotateZPlus15Button: Button
    private lateinit var rotateZMinus15Button: Button
    private lateinit var rotateZPlus90Button: Button
    private lateinit var rotateZMinus90Button: Button

    // --- State & Dependencies ---
    private var arSessionManager: ARSessionManager? = null

    // Enhanced rotation tracking for 3D rotations
    private var totalRotationX = 0f
    private var totalRotationY = 0f
    private var totalRotationZ = 0f
    private var rotationHistory = mutableListOf<String>()

    // Handler for UI operations
    private val handler = Handler(Looper.getMainLooper())

    // Default state values
    private val defaultOpacity = 0.8f // Example default

    init {
        Log.d("ImagePanel", "Initializing...")

        try {
            // Initialize views on the main thread
            panelLayout.post {
                if (initializeViews()) {
                    // Double-check all views are initialized before proceeding
                    if (::loadImageButton.isInitialized &&
                        ::fixImageButton.isInitialized &&
                        ::releaseImageButton.isInitialized &&
                        ::opacitySeekBar.isInitialized &&
                        ::opacityValueText.isInitialized &&
                        ::preserveAspectSwitch.isInitialized &&
                        ::imageSizeText.isInitialized) {

                        Log.d("ImagePanel", "All views confirmed initialized, setting up listeners...")
                        setupListeners()
                        reset()
                        Log.d("ImagePanel", "Successfully initialized")
                    } else {
                        Log.e("ImagePanel", "initializeViews() returned true but some views are not initialized")
                        panelLayout.visibility = View.GONE
                    }
                } else {
                    Log.e("ImagePanel", "View initialization failed.")
                    panelLayout.visibility = View.GONE
                }
            }
        } catch (e: Exception) {
            Log.e("ImagePanel", "Error during initialization", e)
            panelLayout.visibility = View.GONE
        }
    }

    private fun initializeViews(): Boolean {
        try {
            // Find views in the panel layout
            loadImageButton = panelLayout.findViewById(R.id.load_image_button)
            fixImageButton = panelLayout.findViewById(R.id.fix_image_button)
            releaseImageButton = panelLayout.findViewById(R.id.release_image_button)
            testImagePlacementButton = panelLayout.findViewById(R.id.test_image_placement_button)
            opacitySeekBar = panelLayout.findViewById(R.id.image_opacity_seekbar)
            opacityValueText = panelLayout.findViewById(R.id.image_opacity_value)
            preserveAspectSwitch = panelLayout.findViewById(R.id.preserve_aspect_switch)
            imageSizeText = panelLayout.findViewById(R.id.image_size_text)

            // Find rotation buttons - Z-axis (original)
            rotateLeft90Button = panelLayout.findViewById(R.id.rotate_left_90_button)
            rotateRight90Button = panelLayout.findViewById(R.id.rotate_right_90_button)
            rotate180Button = panelLayout.findViewById(R.id.rotate_180_button)
            rotateLeft15Button = panelLayout.findViewById(R.id.rotate_left_15_button)
            rotateRight15Button = panelLayout.findViewById(R.id.rotate_right_15_button)
            flipHorizontalButton = panelLayout.findViewById(R.id.flip_horizontal_button)

            // Find rotation buttons - X-axis (pitch)
            rotateXPlus15Button = panelLayout.findViewById(R.id.rotate_x_plus_15_button)
            rotateXMinus15Button = panelLayout.findViewById(R.id.rotate_x_minus_15_button)
            rotateXPlus90Button = panelLayout.findViewById(R.id.rotate_x_plus_90_button)
            rotateXMinus90Button = panelLayout.findViewById(R.id.rotate_x_minus_90_button)

            // Find rotation buttons - Y-axis (yaw)
            rotateYPlus15Button = panelLayout.findViewById(R.id.rotate_y_plus_15_button)
            rotateYMinus15Button = panelLayout.findViewById(R.id.rotate_y_minus_15_button)
            rotateYPlus90Button = panelLayout.findViewById(R.id.rotate_y_plus_90_button)
            rotateYMinus90Button = panelLayout.findViewById(R.id.rotate_y_minus_90_button)

            // Find rotation buttons - Z-axis (roll) - additional controls
            rotateZPlus15Button = panelLayout.findViewById(R.id.rotate_z_plus_15_button)
            rotateZMinus15Button = panelLayout.findViewById(R.id.rotate_z_minus_15_button)
            rotateZPlus90Button = panelLayout.findViewById(R.id.rotate_z_plus_90_button)
            rotateZMinus90Button = panelLayout.findViewById(R.id.rotate_z_minus_90_button)

            // The imageSelectedStatus TextView is in the main activity layout, not in the panel layout
            // Find it from the parent view of the panel layout
            val rootView = panelLayout.rootView
            imageSelectedStatus = rootView.findViewById(R.id.image_selected_status)
            imageThumbnail = rootView.findViewById(R.id.image_thumbnail)

            if (::imageSelectedStatus.isInitialized) {
                // Set initial status
                imageSelectedStatus.text = "No image selected"
                imageSelectedStatus.visibility = View.VISIBLE
                Log.d("ImagePanel", "Successfully found and initialized imageSelectedStatus TextView")
            } else {
                Log.e("ImagePanel", "Could not find imageSelectedStatus TextView in the root view")
            }

            if (::imageThumbnail.isInitialized) {
                // Initially hide the thumbnail
                imageThumbnail.visibility = View.GONE
                Log.d("ImagePanel", "Successfully found and initialized imageThumbnail ImageView")
            } else {
                Log.e("ImagePanel", "Could not find imageThumbnail ImageView in the root view")
            }

            return true
        } catch (e: Exception) {
            Log.e("ImagePanel", "Error finding views in ImagePanel layout.", e)
            return false
        }
    }

    private fun setupListeners() {
        // Check if views are initialized before setting up listeners
        if (!::loadImageButton.isInitialized || !::fixImageButton.isInitialized ||
            !::releaseImageButton.isInitialized || !::testImagePlacementButton.isInitialized ||
            !::rotateLeft90Button.isInitialized || !::rotateRight90Button.isInitialized ||
            !::rotate180Button.isInitialized || !::rotateLeft15Button.isInitialized ||
            !::rotateRight15Button.isInitialized || !::flipHorizontalButton.isInitialized ||
            !::rotateXPlus15Button.isInitialized || !::rotateXMinus15Button.isInitialized ||
            !::rotateXPlus90Button.isInitialized || !::rotateXMinus90Button.isInitialized ||
            !::rotateYPlus15Button.isInitialized || !::rotateYMinus15Button.isInitialized ||
            !::rotateYPlus90Button.isInitialized || !::rotateYMinus90Button.isInitialized ||
            !::rotateZPlus15Button.isInitialized || !::rotateZMinus15Button.isInitialized ||
            !::rotateZPlus90Button.isInitialized || !::rotateZMinus90Button.isInitialized) {
            Log.e("ImagePanel", "setupListeners called before views were initialized")
            return
        }

        loadImageButton.setOnClickListener { view ->
            Log.d("ImagePanel", "Load image button clicked")
            view.isEnabled = false // Temporarily disable to prevent double-clicks

            // User interaction

            // Show visual feedback
            MessageUtils.showToast(context, "Opening gallery...", Toast.LENGTH_SHORT)

            // Open the image picker
            openImagePicker()

            // Re-enable the button after a short delay
            view.postDelayed({ view.isEnabled = true }, 1000)
        }

        fixImageButton.setOnClickListener {
            // User interaction

            arSessionManager?.fixImage()
            updateFixButtonState(true)
        }

        releaseImageButton.setOnClickListener {
            // User interaction

            arSessionManager?.releaseImage()
            updateFixButtonState(false)
        }

        testImagePlacementButton.setOnClickListener { view ->
            Log.d("ImagePanel", "Test image placement button clicked")
            view.isEnabled = false // Temporarily disable to prevent double-clicks

            // User interaction

            // Show visual feedback
            MessageUtils.showToast(context, "Testing image placement on closest wall...", Toast.LENGTH_SHORT)

            // Call the test function in ARSessionManager
            arSessionManager?.testImagePlacement()

            // Re-enable the button after a short delay
            view.postDelayed({ view.isEnabled = true }, 1000)
        }

        // Rotation button listeners
        rotateLeft90Button.setOnClickListener {
            Log.d("ImagePanel", "Rotate left 90° button clicked")
            logRotationAction("Rotate Left 90°", -90f)
            arSessionManager?.rotateImage(-90f)
        }

        rotateRight90Button.setOnClickListener {
            Log.d("ImagePanel", "Rotate right 90° button clicked")
            logRotationAction("Rotate Right 90°", 90f)
            arSessionManager?.rotateImage(90f)
        }

        rotate180Button.setOnClickListener {
            Log.d("ImagePanel", "Rotate 180° button clicked")
            logRotationAction("Rotate 180°", 180f)
            arSessionManager?.rotateImage(180f)
        }

        rotateLeft15Button.setOnClickListener {
            Log.d("ImagePanel", "Rotate left 15° button clicked")
            logRotationAction("Rotate Left 15°", -15f)
            arSessionManager?.rotateImage(-15f)
        }

        rotateRight15Button.setOnClickListener {
            Log.d("ImagePanel", "Rotate right 15° button clicked")
            logRotationAction("Rotate Right 15°", 15f)
            arSessionManager?.rotateImage(15f)
        }

        flipHorizontalButton.setOnClickListener {
            Log.d("ImagePanel", "Flip horizontal button clicked")
            logRotationAction("Flip Horizontal", 0f, flipHorizontal = true)
            arSessionManager?.flipImageHorizontal()
        }

        // X-axis rotation button listeners
        rotateXPlus15Button.setOnClickListener {
            Log.d("ImagePanel", "Rotate X+15° button clicked")
            logRotationAction("Rotate X+15°", 15f, axis = "X")
            arSessionManager?.rotateImageX(15f)
        }

        rotateXMinus15Button.setOnClickListener {
            Log.d("ImagePanel", "Rotate X-15° button clicked")
            logRotationAction("Rotate X-15°", -15f, axis = "X")
            arSessionManager?.rotateImageX(-15f)
        }

        rotateXPlus90Button.setOnClickListener {
            Log.d("ImagePanel", "Rotate X+90° button clicked")
            logRotationAction("Rotate X+90°", 90f, axis = "X")
            arSessionManager?.rotateImageX(90f)
        }

        rotateXMinus90Button.setOnClickListener {
            Log.d("ImagePanel", "Rotate X-90° button clicked")
            logRotationAction("Rotate X-90°", -90f, axis = "X")
            arSessionManager?.rotateImageX(-90f)
        }

        // Y-axis rotation button listeners
        rotateYPlus15Button.setOnClickListener {
            Log.d("ImagePanel", "Rotate Y+15° button clicked")
            logRotationAction("Rotate Y+15°", 15f, axis = "Y")
            arSessionManager?.rotateImageY(15f)
        }

        rotateYMinus15Button.setOnClickListener {
            Log.d("ImagePanel", "Rotate Y-15° button clicked")
            logRotationAction("Rotate Y-15°", -15f, axis = "Y")
            arSessionManager?.rotateImageY(-15f)
        }

        rotateYPlus90Button.setOnClickListener {
            Log.d("ImagePanel", "Rotate Y+90° button clicked")
            logRotationAction("Rotate Y+90°", 90f, axis = "Y")
            arSessionManager?.rotateImageY(90f)
        }

        rotateYMinus90Button.setOnClickListener {
            Log.d("ImagePanel", "Rotate Y-90° button clicked")
            logRotationAction("Rotate Y-90°", -90f, axis = "Y")
            arSessionManager?.rotateImageY(-90f)
        }

        // Z-axis rotation button listeners (additional controls)
        rotateZPlus15Button.setOnClickListener {
            Log.d("ImagePanel", "Rotate Z+15° button clicked")
            logRotationAction("Rotate Z+15°", 15f, axis = "Z")
            arSessionManager?.rotateImageZ(15f)
        }

        rotateZMinus15Button.setOnClickListener {
            Log.d("ImagePanel", "Rotate Z-15° button clicked")
            logRotationAction("Rotate Z-15°", -15f, axis = "Z")
            arSessionManager?.rotateImageZ(-15f)
        }

        rotateZPlus90Button.setOnClickListener {
            Log.d("ImagePanel", "Rotate Z+90° button clicked")
            logRotationAction("Rotate Z+90°", 90f, axis = "Z")
            arSessionManager?.rotateImageZ(90f)
        }

        rotateZMinus90Button.setOnClickListener {
            Log.d("ImagePanel", "Rotate Z-90° button clicked")
            logRotationAction("Rotate Z-90°", -90f, axis = "Z")
            arSessionManager?.rotateImageZ(-90f)
        }

        opacitySeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                // User interaction

                opacityValueText.text = context.getString(R.string.opacity_format, progress)
                if (fromUser) {
                    val opacity = progress / 100f
                    // Use the property directly instead of a setter method
                    arSessionManager?.getCurrentImage()?.let { image ->
                        image.opacity = opacity
                    }
                }
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                // User interaction
            }
            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                // User interaction
            }
        })

        preserveAspectSwitch.setOnCheckedChangeListener { _, isChecked ->
            // User interaction

            // Assuming ARSessionManager.setPreserveAspectRatio() exists
            arSessionManager?.setPreserveAspectRatio(isChecked)
        }
    }

    private fun updateFixButtonState(isFixed: Boolean) {
        // Check if views are initialized before attempting to access them
        if (!::fixImageButton.isInitialized || !::releaseImageButton.isInitialized ||
            !::loadImageButton.isInitialized || !::testImagePlacementButton.isInitialized) {
            Log.w("ImagePanel", "updateFixButtonState called before views were initialized")
            return
        }

        if (isFixed) {
            fixImageButton.visibility = View.GONE
            releaseImageButton.visibility = View.VISIBLE
            testImagePlacementButton.visibility = View.GONE
            loadImageButton.isEnabled = false
            // opacitySeekBar.isEnabled = false // Optional
            // preserveAspectSwitch.isEnabled = false // Optional
        } else {
            fixImageButton.visibility = View.VISIBLE
            releaseImageButton.visibility = View.GONE
            testImagePlacementButton.visibility = View.VISIBLE
            loadImageButton.isEnabled = true
            // opacitySeekBar.isEnabled = true // Optional
            // preserveAspectSwitch.isEnabled = true // Optional
        }
        Log.d("ImagePanel", "Updated fix button state: isFixed=$isFixed")
    }

    private fun openImagePicker() {
        Log.d("ImagePanel", "Requesting image picker launch...")

        // Check if we have storage permissions
        if (context is MainActivity && !PermissionHelper.hasStoragePermissions(context)) {
            Log.d("ImagePanel", "Storage permissions not granted, requesting...")
            (context as MainActivity).requestStoragePermission()
            MessageUtils.showToast(context, "Please grant storage permissions to load images", Toast.LENGTH_SHORT)
            return
        }

        // Try multiple intent types in sequence to maximize compatibility
        val intents = mutableListOf<Intent>()

        // Option 1: Simple ACTION_GET_CONTENT (most compatible)
        intents.add(Intent(Intent.ACTION_GET_CONTENT).apply {
            type = "image/*"
            addCategory(Intent.CATEGORY_OPENABLE)
        })

        // Option 2: ACTION_PICK from media store (works on most devices)
        intents.add(Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI))

        // Option 3: ACTION_OPEN_DOCUMENT (newer API)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            intents.add(Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
                type = "image/*"
                addCategory(Intent.CATEGORY_OPENABLE)
                putExtra(Intent.EXTRA_MIME_TYPES, arrayOf("image/jpeg", "image/png", "image/webp"))
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                addFlags(Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION)
            })
        }

        // Try each intent in sequence
        var success = false
        for (intent in intents) {
            try {
                Log.d("ImagePanel", "Trying to launch gallery with intent: ${intent.action}")
                requestImageLauncher(intent)
                success = true
                Log.d("ImagePanel", "Successfully launched gallery picker")
                break
            } catch (e: Exception) {
                Log.e("ImagePanel", "Failed to launch with intent ${intent.action}: ${e.message}")
                // Continue to next intent
            }
        }

        if (success) {
            MessageUtils.showToast(context, "Select an image from your gallery", Toast.LENGTH_SHORT)
        } else {
            // All intents failed
            Log.e("ImagePanel", "All gallery picker intents failed")
            MessageUtils.showToast(context, "Could not open gallery. Please check app permissions.", Toast.LENGTH_LONG)

            // Last resort: Try to create a chooser with all available options
            try {
                val chooserIntent = Intent.createChooser(
                    Intent(Intent.ACTION_GET_CONTENT).apply { type = "image/*" },
                    "Select Image"
                )
                requestImageLauncher(chooserIntent)
            } catch (e: Exception) {
                Log.e("ImagePanel", "Even chooser intent failed: ${e.message}")
                MessageUtils.showToast(context, "Gallery access completely failed. Please restart the app.", Toast.LENGTH_LONG)
            }
        }
    }

    fun onImageSelected(uri: Uri) {
        Log.d("ImageDebug", "===== IMAGE PANEL: onImageSelected CALLED =====")
        Log.d("ImageDebug", "Image URI: $uri")
        Log.d("ImageDebug", "URI scheme: ${uri.scheme}, authority: ${uri.authority}, path: ${uri.path}")

        // Consider running this on a background thread
        // CoroutineScope(Dispatchers.IO).launch { ... }
        try {
            // Check if imageSizeText is initialized before continuing
            if (!::imageSizeText.isInitialized) {
                Log.e("ImageDebug", "onImageSelected called before views were initialized")
                Log.e("ImageDebug", "imageSizeText initialized: ${::imageSizeText.isInitialized}")
                Log.e("ImageDebug", "fixImageButton initialized: ${::fixImageButton.isInitialized}")
                Log.e("ImageDebug", "opacitySeekBar initialized: ${::opacitySeekBar.isInitialized}")
                MessageUtils.showToast(context, "Please try again in a moment", Toast.LENGTH_SHORT)
                return
            }

            Log.d("ImageDebug", "Attempting to convert URI to bitmap using FileUtils.uriToBitmap")

            // Check if the URI is readable
            try {
                val inputStream = context.contentResolver.openInputStream(uri)
                val canRead = inputStream != null
                inputStream?.close()
                Log.d("ImageDebug", "Can read from URI directly: $canRead")
            } catch (e: Exception) {
                Log.e("ImageDebug", "Cannot read from URI directly", e)
            }

            // Try multiple methods to load the bitmap
            var bitmap: Bitmap? = null

            // Method 1: Use FileUtils.uriToBitmap
            bitmap = FileUtils.uriToBitmap(context, uri)

            if (bitmap == null) {
                Log.e("ImageDebug", "FileUtils.uriToBitmap returned null, trying alternative method")

                // Method 2: Try using MediaStore for content URIs
                try {
                    if (uri.toString().startsWith("content://")) {
                        bitmap = MediaStore.Images.Media.getBitmap(context.contentResolver, uri)
                        Log.d("ImageDebug", "MediaStore.Images.Media.getBitmap succeeded")
                    }
                } catch (e: Exception) {
                    Log.e("ImageDebug", "MediaStore.Images.Media.getBitmap failed", e)
                }

                // Method 3: Try using BitmapFactory directly
                if (bitmap == null) {
                    try {
                        val inputStream = context.contentResolver.openInputStream(uri)
                        bitmap = BitmapFactory.decodeStream(inputStream)
                        inputStream?.close()
                        Log.d("ImageDebug", "BitmapFactory.decodeStream direct attempt")
                    } catch (e: Exception) {
                        Log.e("ImageDebug", "BitmapFactory.decodeStream failed", e)
                    }
                }
            }

            if (bitmap == null) {
                Log.e("ImageDebug", "All bitmap loading methods failed")
            } else {
                Log.d("ImageDebug", "Bitmap loaded successfully, size: ${bitmap.width}x${bitmap.height}")
            }

            // Switch back to main thread to update UI and interact with ARSessionManager
            // withContext(Dispatchers.Main) { ... }
            bitmap?.let { loadedBitmap ->
                Log.d("ImageDebug", "Bitmap loaded successfully: ${loadedBitmap.width}x${loadedBitmap.height}")

                // Check if ARSessionManager is initialized
                if (arSessionManager == null) {
                    Log.e("ImageDebug", "arSessionManager is null, cannot load image")
                    MessageUtils.showToast(context, "Error: AR Session not initialized", Toast.LENGTH_SHORT)

                    // Try to get the ARSessionManager from MainActivity as a fallback
                    if (context is MainActivity) {
                        val mainActivity = context as MainActivity
                        if (mainActivity.isARSessionManagerInitialized()) {
                            Log.d("ImageDebug", "Attempting to get ARSessionManager from MainActivity as fallback")
                            val sessionManager = mainActivity.getARSessionManager()
                            if (sessionManager != null) {
                                Log.d("ImageDebug", "Successfully got ARSessionManager from MainActivity")
                                arSessionManager = sessionManager
                            } else {
                                Log.e("ImageDebug", "Failed to get ARSessionManager from MainActivity")
                                return@let
                            }
                        } else {
                            Log.e("ImageDebug", "ARSessionManager not initialized in MainActivity")
                            return@let
                        }
                    } else {
                        Log.e("ImageDebug", "Context is not MainActivity, cannot get ARSessionManager")
                        return@let
                    }
                }

                Log.d("ImageDebug", "Calling arSessionManager.loadImage() with bitmap size: ${loadedBitmap.width}x${loadedBitmap.height}")
                // Assuming ARSessionManager.loadImage(Bitmap) exists
                arSessionManager?.loadImage(loadedBitmap)

                // Verify the image was loaded successfully
                var imageLoaded = arSessionManager?.getCurrentImage() != null
                Log.d("ImageDebug", "arSessionManager.loadImage() completed. Image loaded successfully: $imageLoaded")

                // If the image wasn't loaded successfully, try again after a short delay
                if (!imageLoaded) {
                    Log.d("ImageDebug", "Image not loaded successfully, trying again after delay")
                    try {
                        // Wait a moment before trying again
                        Thread.sleep(500)

                        // Try loading the image again
                        Log.d("ImageDebug", "Retrying image load after delay")
                        arSessionManager?.loadImage(loadedBitmap)

                        // Check if it worked this time
                        imageLoaded = arSessionManager?.getCurrentImage() != null
                        Log.d("ImageDebug", "Retry image load completed. Image loaded successfully: $imageLoaded")
                    } catch (e: Exception) {
                        Log.e("ImageDebug", "Error during retry of image loading", e)
                    }
                }

                // Update the image size text
                imageSizeText.text = context.getString(
                    R.string.image_size_format,
                    loadedBitmap.width,
                    loadedBitmap.height
                )

                // Update the image selected status message
                if (::imageSelectedStatus.isInitialized) {
                    // Extract filename from URI if possible
                    val fileName = try {
                        val cursor = context.contentResolver.query(uri, null, null, null, null)
                        val nameIndex = cursor?.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                        cursor?.moveToFirst()
                        val name = nameIndex?.let { cursor.getString(it) } ?: "Unknown"
                        cursor?.close()
                        name
                    } catch (e: Exception) {
                        Log.e("ImageDebug", "Error extracting filename from URI", e)
                        "Image selected"
                    }

                    // Update the status text with just the filename
                    try {
                        Log.d("ImageDebug", "Setting imageSelectedStatus text to: $fileName")
                        imageSelectedStatus.text = fileName
                        Log.d("ImageDebug", "Successfully updated image selected status to filename: $fileName")

                        // Double-check that the text was actually set
                        val currentText = imageSelectedStatus.text.toString()
                        Log.d("ImageDebug", "Current text in imageSelectedStatus after update: $currentText")
                    } catch (e: Exception) {
                        Log.e("ImageDebug", "Error updating imageSelectedStatus text", e)
                    }
                } else {
                    Log.e("ImageDebug", "imageSelectedStatus is not initialized, cannot update filename")

                    // Try to find it again as a fallback
                    try {
                        val rootView = panelLayout.rootView
                        val statusView = rootView.findViewById<TextView>(R.id.image_selected_status)
                        if (statusView != null) {
                            val fileName = try {
                                val cursor = context.contentResolver.query(uri, null, null, null, null)
                                val nameIndex = cursor?.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                                cursor?.moveToFirst()
                                val name = nameIndex?.let { cursor.getString(it) } ?: "Unknown"
                                cursor?.close()
                                name
                            } catch (e: Exception) {
                                "Image selected"
                            }

                            statusView.text = fileName
                            Log.d("ImageDebug", "Updated imageSelectedStatus via fallback method: $fileName")
                        } else {
                            Log.e("ImageDebug", "Could not find imageSelectedStatus even with fallback method")
                        }
                    } catch (e: Exception) {
                        Log.e("ImageDebug", "Error in fallback method for updating imageSelectedStatus", e)
                    }
                }

                syncImageSettingsUi() // Update sliders/switches based on new image/defaults

                // Create and display a thumbnail from the loaded bitmap
                if (::imageThumbnail.isInitialized) {
                    try {
                        // Create a scaled-down version of the bitmap for the thumbnail
                        val thumbnailSize = 32 // Match the size in the layout
                        val scaledBitmap = Bitmap.createScaledBitmap(loadedBitmap, thumbnailSize, thumbnailSize, true)

                        // Set the thumbnail image and make it visible
                        imageThumbnail.setImageBitmap(scaledBitmap)
                        imageThumbnail.visibility = View.VISIBLE

                        // Hide the filename text since we're showing the thumbnail
                        if (::imageSelectedStatus.isInitialized) {
                            imageSelectedStatus.visibility = View.GONE
                            Log.d("ImageDebug", "Hiding filename text since thumbnail is displayed")
                        }

                        // Auto-close timer removed as requested
                        Log.d("ImageDebug", "Thumbnail created and displayed successfully")
                    } catch (e: Exception) {
                        Log.e("ImageDebug", "Error creating thumbnail", e)

                        // Make sure filename is visible if thumbnail creation fails
                        if (::imageSelectedStatus.isInitialized) {
                            imageSelectedStatus.visibility = View.VISIBLE
                        }

                        // Auto-close timer removed as requested
                        Log.d("ImageDebug", "Thumbnail creation failed but panel will remain open")
                    }
                } else {
                    Log.e("ImageDebug", "imageThumbnail is not initialized, cannot display thumbnail")

                    // Make sure filename is visible if thumbnail can't be displayed
                    if (::imageSelectedStatus.isInitialized) {
                        imageSelectedStatus.visibility = View.VISIBLE
                    }

                    // Auto-close timer removed as requested
                    Log.d("ImageDebug", "Thumbnail view not initialized but panel will remain open")
                }

                Log.d("ImageDebug", "UI updated with new image information")

                // Show detailed instructions to the user
                MessageUtils.showToast(
                    context,
                    "Image loaded successfully! To place it:\n1. Scan walls until they highlight green\n2. Tap directly on a highlighted wall\n3. Press 'Lock Image' when positioned",
                    Toast.LENGTH_LONG
                )

                // Image loading complete

                Log.d("ImageDebug", "Image loading process completed successfully.")

            } ?: run {
                Log.e("ImageDebug", "Failed to load bitmap from URI")
                MessageUtils.showToast(context, context.getString(R.string.failed_to_load_image), Toast.LENGTH_SHORT)
                imageSizeText.text = context.getString(R.string.no_image_loaded)
            }
        } catch (e: Exception) {
            // withContext(Dispatchers.Main) { ... }
            Log.e("ImageDebug", "Error processing selected image URI", e)
            Log.e("ImageDebug", "Exception type: ${e.javaClass.name}, message: ${e.message}")
            Log.e("ImageDebug", "Stack trace: ${e.stackTraceToString()}")

            MessageUtils.showToast(
                context,
                context.getString(R.string.image_load_error, e.localizedMessage),
                Toast.LENGTH_LONG
            )
            if (::imageSizeText.isInitialized) {
                imageSizeText.text = context.getString(R.string.no_image_loaded)
            }
        }

        Log.d("ImageDebug", "===== IMAGE PANEL: onImageSelected COMPLETED =====")
    }


    fun setARSessionManager(manager: ARSessionManager) {
        this.arSessionManager = manager
        Log.d("ImagePanel", "ARSessionManager set for ImagePanel.")
        // Ensure we only sync UI after views are fully initialized
        panelLayout.post {
            syncUiStateFromManager()
        }
    }

    private fun syncUiStateFromManager() {
        // Check if UI has been initialized before proceeding
        if (!::fixImageButton.isInitialized || !::releaseImageButton.isInitialized ||
            !::opacitySeekBar.isInitialized || !::opacityValueText.isInitialized ||
            !::preserveAspectSwitch.isInitialized || !::imageSizeText.isInitialized) {
            Log.w("ImagePanel", "syncUiStateFromManager called before views were initialized")
            return
        }

        arSessionManager?.let { manager ->
            // Sync fix state - Access property directly
            val isActuallyFixed = manager.isImageFixed // CORRECTED ACCESS
            updateFixButtonState(isActuallyFixed)
            syncImageSettingsUi()

        } ?: run {
            updateFixButtonState(false)
            resetImageSettingsUi()
            imageSizeText.text = context.getString(R.string.no_image_loaded)
        }
    }

    private fun syncImageSettingsUi() {
        // Check if UI components are initialized before proceeding
        if (!::opacitySeekBar.isInitialized || !::opacityValueText.isInitialized ||
            !::preserveAspectSwitch.isInitialized) {
            Log.w("ImagePanel", "syncImageSettingsUi called before views were initialized")
            return
        }

        arSessionManager?.let { manager ->
            // Get the current image from the session manager
            val currentImage: ARImage? = manager.getCurrentImage()
            // Access the properties directly rather than using getter methods
            val currentOpacity = currentImage?.opacity ?: settingsManager.defaultImageOpacity
            val currentPreserveAspect = currentImage?.preserveAspectRatio ?: settingsManager.preserveAspectRatio

            opacitySeekBar.progress = (currentOpacity * 100f).toInt()
            opacityValueText.text = context.getString(R.string.opacity_format, opacitySeekBar.progress)
            // Only update if the switch state is different to avoid triggering listener unnecessarily
            if (preserveAspectSwitch.isChecked != currentPreserveAspect) {
                preserveAspectSwitch.isChecked = currentPreserveAspect
            }
            Log.d("ImagePanel", "Synced image settings: opacity=${currentOpacity}, preserveAspect=${currentPreserveAspect}")

        } ?: resetImageSettingsUi()
    }

    private fun resetImageSettingsUi() {
        // Check if UI components are initialized before proceeding
        if (!::opacitySeekBar.isInitialized || !::opacityValueText.isInitialized ||
            !::preserveAspectSwitch.isInitialized) {
            Log.w("ImagePanel", "resetImageSettingsUi called before views were initialized")
            return
        }

        val defaultOpacityProgress = (settingsManager.defaultImageOpacity * 100f).toInt()
        opacitySeekBar.progress = defaultOpacityProgress
        opacityValueText.text = context.getString(R.string.opacity_format, defaultOpacityProgress)
        if (preserveAspectSwitch.isChecked != settingsManager.preserveAspectRatio) {
            preserveAspectSwitch.isChecked = settingsManager.preserveAspectRatio
        }
        Log.d("ImagePanel", "Reset image settings UI to defaults.")
    }


    fun show() {
        // Ensure UI is synced in the UI thread after views are initialized
        panelLayout.post {
            syncUiStateFromManager()
            panelLayout.visibility = View.VISIBLE

            // Move the Fix Image button to be more prominent
            if (::fixImageButton.isInitialized) {
                // Make sure it's visible and styled prominently
                fixImageButton.visibility = View.VISIBLE
            }

            // Panel is now visible

            Log.d("ImagePanel", "ImagePanel shown.")
        }
    }

    fun hide() {
        panelLayout.visibility = View.GONE
        Log.d("ImagePanel", "ImagePanel hidden.")
    }

    // Auto-hide timer removed as requested

    /**
     * Returns whether this panel is currently active (visible or should be visible)
     */
    fun isActive(): Boolean {
        return panelLayout.visibility == View.VISIBLE
    }

    fun reset() {
        Log.d("ImagePanel", "Resetting ImagePanel state.")
        resetImageSettingsUi()
        if (::imageSizeText.isInitialized) {
            imageSizeText.text = context.getString(R.string.no_image_loaded)
        }
        // Hide the thumbnail when resetting
        if (::imageThumbnail.isInitialized) {
            imageThumbnail.visibility = View.GONE
            imageThumbnail.setImageBitmap(null) // Clear the bitmap to free memory
        }

        // Show the filename text again and reset it
        if (::imageSelectedStatus.isInitialized) {
            imageSelectedStatus.visibility = View.VISIBLE
            imageSelectedStatus.text = "No image selected"
        }
        // Correctly access isImageFixed property
        arSessionManager?.let { updateFixButtonState(it.isImageFixed) } ?: updateFixButtonState(false)

        // Reset rotation tracking when panel is reset
        resetRotationTracking()
    }

    /**
     * Enhanced logging for 3D rotation actions - logs rotation around specific axes
     */
    private fun logRotationAction(actionName: String, angle: Float, flipHorizontal: Boolean = false, axis: String = "Z") {
        val timestamp = System.currentTimeMillis()
        val action = if (flipHorizontal) {
            "FLIP_HORIZONTAL"
        } else {
            "ROTATE_${axis}_${angle}°"
        }

        // Update total rotation per axis (normalize to 0-360 range)
        if (!flipHorizontal) {
            when (axis) {
                "X" -> {
                    totalRotationX = (totalRotationX + angle) % 360f
                    if (totalRotationX < 0) totalRotationX += 360f
                }
                "Y" -> {
                    totalRotationY = (totalRotationY + angle) % 360f
                    if (totalRotationY < 0) totalRotationY += 360f
                }
                "Z" -> {
                    totalRotationZ = (totalRotationZ + angle) % 360f
                    if (totalRotationZ < 0) totalRotationZ += 360f
                }
            }
        }

        // Create detailed log entry with 3D rotation information
        val logEntry = "[$timestamp] $actionName: $action | X: ${totalRotationX}° Y: ${totalRotationY}° Z: ${totalRotationZ}°"
        rotationHistory.add(logEntry)

        // Enhanced logging to Android Log with 3D rotation details
        Log.i("ROTATION_3D_LOG", "=== 3D IMAGE ROTATION ACTION ===")
        Log.i("ROTATION_3D_LOG", "Action: $actionName")
        Log.i("ROTATION_3D_LOG", "Axis: $axis")
        Log.i("ROTATION_3D_LOG", "Angle: ${if (flipHorizontal) "FLIP_H" else "${angle}°"}")
        Log.i("ROTATION_3D_LOG", "Total X-axis rotation: ${totalRotationX}°")
        Log.i("ROTATION_3D_LOG", "Total Y-axis rotation: ${totalRotationY}°")
        Log.i("ROTATION_3D_LOG", "Total Z-axis rotation: ${totalRotationZ}°")
        Log.i("ROTATION_3D_LOG", "Timestamp: $timestamp")
        Log.i("ROTATION_3D_LOG", "History Count: ${rotationHistory.size}")
        Log.i("ROTATION_3D_LOG", "=================================")

        // Log complete 3D rotation summary every 5 actions
        if (rotationHistory.size % 5 == 0) {
            Log.i("ROTATION_3D_SUMMARY", "=== COMPLETE 3D ROTATION SUMMARY ===")
            Log.i("ROTATION_3D_SUMMARY", "FINAL ROTATIONS FOR AUGMENT CODE:")
            Log.i("ROTATION_3D_SUMMARY", "X-axis (Pitch): ${totalRotationX}°")
            Log.i("ROTATION_3D_SUMMARY", "Y-axis (Yaw): ${totalRotationY}°")
            Log.i("ROTATION_3D_SUMMARY", "Z-axis (Roll): ${totalRotationZ}°")
            Log.i("ROTATION_3D_SUMMARY", "")
            Log.i("ROTATION_3D_SUMMARY", "COMPLETE ACTION HISTORY:")
            rotationHistory.forEachIndexed { index, entry ->
                Log.i("ROTATION_3D_SUMMARY", "${index + 1}: $entry")
            }
            Log.i("ROTATION_3D_SUMMARY", "====================================")
        }

        // Show enhanced toast with 3D rotation state
        val toastMessage = if (flipHorizontal) {
            "$actionName applied"
        } else {
            "$actionName applied. X:${totalRotationX}° Y:${totalRotationY}° Z:${totalRotationZ}°"
        }
        MessageUtils.showToast(context, toastMessage, Toast.LENGTH_SHORT)
    }

    /**
     * Resets 3D rotation tracking (call when image is reloaded or reset)
     */
    fun resetRotationTracking() {
        totalRotationX = 0f
        totalRotationY = 0f
        totalRotationZ = 0f
        rotationHistory.clear()
        Log.i("ROTATION_3D_LOG", "=== 3D ROTATION TRACKING RESET ===")
        Log.i("ROTATION_3D_LOG", "All axis rotations reset to 0°")
    }

    /**
     * Gets the complete rotation history for external analysis
     */
    fun getRotationHistory(): List<String> {
        return rotationHistory.toList()
    }

    /**
     * Gets the current total rotation for X-axis (pitch)
     */
    fun getCurrentTotalRotationX(): Float {
        return totalRotationX
    }

    /**
     * Gets the current total rotation for Y-axis (yaw)
     */
    fun getCurrentTotalRotationY(): Float {
        return totalRotationY
    }

    /**
     * Gets the current total rotation for Z-axis (roll)
     */
    fun getCurrentTotalRotationZ(): Float {
        return totalRotationZ
    }

    /**
     * Gets all current 3D rotations as a formatted string for Augment Code
     */
    fun getFinalRotationsForAugmentCode(): String {
        return "FINAL 3D ROTATIONS FOR IMPLEMENTATION:\n" +
                "X-axis (Pitch): ${totalRotationX}°\n" +
                "Y-axis (Yaw): ${totalRotationY}°\n" +
                "Z-axis (Roll): ${totalRotationZ}°\n" +
                "Total actions performed: ${rotationHistory.size}"
    }
}