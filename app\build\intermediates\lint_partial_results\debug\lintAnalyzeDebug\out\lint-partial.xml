<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.9.2" type="partial_results">
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="android.settings.APPLICATION_DETAILS_SETTINGS (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/MainActivity.kt"
                            line="1395"
                            column="30"
                            startOffset="68807"
                            endLine="1395"
                            endColumn="82"
                            endOffset="68859"/>
                        <location id="1"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/MainActivity.kt"
                            line="1509"
                            column="38"
                            startOffset="75170"
                            endLine="1509"
                            endColumn="90"
                            endOffset="75222"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="incidents">
                    <map id="0">
                        <entry
                            name="incidentClass"
                            string="com.deace.deacear.ui.DeacePanel"/>
                        <location id="location"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/DeacePanel.kt"
                            line="76"
                            column="30"
                            startOffset="2307"
                            endLine="76"
                            endColumn="44"
                            endOffset="2321"/>
                        <location id="secondaryLocation"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/DeacePanel.kt"
                            line="78"
                            column="17"
                            startOffset="2371"
                            endLine="78"
                            endColumn="46"
                            endOffset="2400"
                            message="The unsafe intent is launched here."/>
                    </map>
            </map>
            <map id="unprotected">
                <entry
                    name="com.deace.deacear.SplashActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.array.grid_formats"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/arrays.xml"
            line="3"
            column="19"
            startOffset="71"
            endLine="3"
            endColumn="38"
            endOffset="90"/>
        <location id="R.array.line_thickness_options"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/arrays.xml"
            line="9"
            column="19"
            startOffset="247"
            endLine="9"
            endColumn="48"
            endOffset="276"/>
        <location id="R.color.ar_anchor_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="31"
            column="12"
            startOffset="1439"
            endLine="31"
            endColumn="34"
            endOffset="1461"/>
        <location id="R.color.ar_grid_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="28"
            column="12"
            startOffset="1185"
            endLine="28"
            endColumn="32"
            endOffset="1205"/>
        <location id="R.color.ar_plane_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="29"
            column="12"
            startOffset="1274"
            endLine="29"
            endColumn="33"
            endOffset="1295"/>
        <location id="R.color.ar_reticle_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="30"
            column="12"
            startOffset="1364"
            endLine="30"
            endColumn="35"
            endOffset="1387"/>
        <location id="R.color.background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="13"
            column="12"
            startOffset="538"
            endLine="13"
            endColumn="29"
            endOffset="555"/>
        <location id="R.color.buttonDisabled"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="25"
            column="12"
            startOffset="1096"
            endLine="25"
            endColumn="33"
            endOffset="1117"/>
        <location id="R.color.buttonNormal"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="23"
            column="12"
            startOffset="962"
            endLine="23"
            endColumn="31"
            endOffset="981"/>
        <location id="R.color.buttonPressed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="24"
            column="12"
            startOffset="1022"
            endLine="24"
            endColumn="32"
            endOffset="1042"/>
        <location id="R.color.colorAccent"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="6"
            column="12"
            startOffset="250"
            endLine="6"
            endColumn="30"
            endOffset="268"/>
        <location id="R.color.colorPrimary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="4"
            column="12"
            startOffset="108"
            endLine="4"
            endColumn="31"
            endOffset="127"/>
        <location id="R.color.colorPrimaryDark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="5"
            column="12"
            startOffset="177"
            endLine="5"
            endColumn="35"
            endOffset="200"/>
        <location id="R.color.error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="15"
            column="12"
            startOffset="689"
            endLine="15"
            endColumn="24"
            endOffset="701"/>
        <location id="R.color.gradient_end"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="45"
            column="12"
            startOffset="2061"
            endLine="45"
            endColumn="31"
            endOffset="2080"/>
        <location id="R.color.gradient_start"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="44"
            column="12"
            startOffset="1999"
            endLine="44"
            endColumn="33"
            endOffset="2020"/>
        <location id="R.color.item_pressed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="53"
            column="12"
            startOffset="2445"
            endLine="53"
            endColumn="31"
            endOffset="2464"/>
        <location id="R.color.item_selected"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="52"
            column="12"
            startOffset="2361"
            endLine="52"
            endColumn="32"
            endOffset="2381"/>
        <location id="R.color.navigationBarBackground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="10"
            column="12"
            startOffset="436"
            endLine="10"
            endColumn="42"
            endOffset="466"/>
        <location id="R.color.notification_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="41"
            column="12"
            startOffset="1869"
            endLine="41"
            endColumn="37"
            endOffset="1894"/>
        <location id="R.color.notification_success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="39"
            column="12"
            startOffset="1725"
            endLine="39"
            endColumn="39"
            endOffset="1752"/>
        <location id="R.color.notification_warning"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="40"
            column="12"
            startOffset="1797"
            endLine="40"
            endColumn="39"
            endOffset="1824"/>
        <location id="R.color.panel_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="48"
            column="12"
            startOffset="2158"
            endLine="48"
            endColumn="35"
            endOffset="2181"/>
        <location id="R.color.panel_divider"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="49"
            column="12"
            startOffset="2244"
            endLine="49"
            endColumn="32"
            endOffset="2264"/>
        <location id="R.color.statusBarBackground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="9"
            column="12"
            startOffset="365"
            endLine="9"
            endColumn="38"
            endOffset="391"/>
        <location id="R.color.surface"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="14"
            column="12"
            startOffset="622"
            endLine="14"
            endColumn="26"
            endOffset="636"/>
        <location id="R.color.textHint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="20"
            column="12"
            startOffset="880"
            endLine="20"
            endColumn="27"
            endOffset="895"/>
        <location id="R.color.textPrimary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="18"
            column="12"
            startOffset="784"
            endLine="18"
            endColumn="30"
            endOffset="802"/>
        <location id="R.color.textSecondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="19"
            column="12"
            startOffset="831"
            endLine="19"
            endColumn="32"
            endOffset="851"/>
        <location id="R.color.transparent"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="36"
            column="12"
            startOffset="1636"
            endLine="36"
            endColumn="30"
            endOffset="1654"/>
        <location id="R.dimen.main_menu_minimized_width"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="6"
            column="12"
            startOffset="170"
            endLine="6"
            endColumn="44"
            endOffset="202"/>
        <location id="R.drawable.baseline_grid_on_24"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_grid_on_24.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="570"/>
        <location id="R.drawable.baseline_grid_on_black_18"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_grid_on_black_18.png"/>
        <location id="R.drawable.baseline_grid_on_black_20"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_grid_on_black_20.png"/>
        <location id="R.drawable.baseline_grid_on_black_24"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_grid_on_black_24.png"/>
        <location id="R.drawable.baseline_grid_on_black_36"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_grid_on_black_36.png"/>
        <location id="R.drawable.baseline_grid_on_black_48"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_grid_on_black_48.png"/>
        <location id="R.drawable.baseline_image_20"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_image_20.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="441"/>
        <location id="R.drawable.baseline_image_24"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_image_24.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="453"/>
        <location id="R.drawable.baseline_image_black_18"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_image_black_18.png"/>
        <location id="R.drawable.baseline_image_black_20"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_image_black_20.png"/>
        <location id="R.drawable.baseline_image_black_24"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_image_black_24.png"/>
        <location id="R.drawable.baseline_image_black_36"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_image_black_36.png"/>
        <location id="R.drawable.baseline_image_black_48"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_image_black_48.png"/>
        <location id="R.drawable.ic_maximize"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_maximize.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="376"/>
        <location id="R.drawable.ic_minimize"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_minimize.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="314"/>
        <location id="R.drawable.save_alt"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/save_alt.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="1"
            endColumn="253"
            endOffset="252"/>
        <location id="R.layout.config_panel"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/config_panel.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="371"
            endColumn="14"
            endOffset="14855"/>
        <location id="R.layout.export_panel"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/export_panel.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="21"
            endColumn="16"
            endOffset="822"/>
        <location id="R.layout.grid_panel"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/grid_panel.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="123"
            endColumn="16"
            endOffset="4884"/>
        <location id="R.layout.image_panel"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/image_panel.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="212"
            endColumn="16"
            endOffset="8432"/>
        <location id="R.string.baking_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="30"
            column="13"
            startOffset="1461"
            endLine="30"
            endColumn="32"
            endOffset="1480"/>
        <location id="R.string.choose_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="42"
            column="13"
            startOffset="2062"
            endLine="42"
            endColumn="32"
            endOffset="2081"/>
        <location id="R.string.choose_folder"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="101"
            column="13"
            startOffset="5533"
            endLine="101"
            endColumn="33"
            endOffset="5553"/>
        <location id="R.string.deace_tab"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="22"
            column="13"
            startOffset="1190"
            endLine="22"
            endColumn="29"
            endOffset="1206"/>
        <location id="R.string.default_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="82"
            column="13"
            startOffset="4484"
            endLine="82"
            endColumn="34"
            endOffset="4505"/>
        <location id="R.string.export_instructions"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="59"
            column="13"
            startOffset="2858"
            endLine="59"
            endColumn="39"
            endOffset="2884"/>
        <location id="R.string.file_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="94"
            column="13"
            startOffset="5182"
            endLine="94"
            endColumn="31"
            endOffset="5200"/>
        <location id="R.string.filename_pattern"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="99"
            column="13"
            startOffset="5411"
            endLine="99"
            endColumn="36"
            endOffset="5434"/>
        <location id="R.string.general"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="102"
            column="13"
            startOffset="5590"
            endLine="102"
            endColumn="27"
            endOffset="5604"/>
        <location id="R.string.grid_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="85"
            column="13"
            startOffset="4673"
            endLine="85"
            endColumn="30"
            endOffset="4690"/>
        <location id="R.string.image_opacity"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="49"
            column="13"
            startOffset="2304"
            endLine="49"
            endColumn="33"
            endOffset="2324"/>
        <location id="R.string.image_quality"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="89"
            column="13"
            startOffset="4921"
            endLine="89"
            endColumn="33"
            endOffset="4941"/>
        <location id="R.string.jpg"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="97"
            column="13"
            startOffset="5323"
            endLine="97"
            endColumn="23"
            endOffset="5333"/>
        <location id="R.string.line_thickness"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="86"
            column="13"
            startOffset="4724"
            endLine="86"
            endColumn="34"
            endOffset="4745"/>
        <location id="R.string.maximum"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="92"
            column="13"
            startOffset="5080"
            endLine="92"
            endColumn="27"
            endOffset="5094"/>
        <location id="R.string.minimize_menu"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="23"
            column="13"
            startOffset="1235"
            endLine="23"
            endColumn="33"
            endOffset="1255"/>
        <location id="R.string.normal"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="90"
            column="13"
            startOffset="4978"
            endLine="90"
            endColumn="26"
            endOffset="4991"/>
        <location id="R.string.opacity_value"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="15"
            column="13"
            startOffset="934"
            endLine="15"
            endColumn="33"
            endOffset="954"/>
        <location id="R.string.png"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="95"
            column="13"
            startOffset="5235"
            endLine="95"
            endColumn="23"
            endOffset="5245"/>
        <location id="R.string.preserve_aspect_ratio"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="53"
            column="13"
            startOffset="2556"
            endLine="53"
            endColumn="41"
            endOffset="2584"/>
        <location id="R.string.projected_image_options"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="87"
            column="13"
            startOffset="4783"
            endLine="87"
            endColumn="43"
            endOffset="4813"/>
        <location id="R.string.prompt_placeholder"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="31"
            column="13"
            startOffset="1520"
            endLine="31"
            endColumn="38"
            endOffset="1545"/>
        <location id="R.string.release_grid"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="40"
            column="13"
            startOffset="1952"
            endLine="40"
            endColumn="32"
            endOffset="1971"/>
        <location id="R.string.reset_ar_session"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="77"
            column="13"
            startOffset="4175"
            endLine="77"
            endColumn="36"
            endOffset="4198"/>
        <location id="R.string.results_placeholder"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="32"
            column="13"
            startOffset="1599"
            endLine="32"
            endColumn="39"
            endOffset="1625"/>
        <location id="R.string.save_directory"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="100"
            column="13"
            startOffset="5474"
            endLine="100"
            endColumn="34"
            endOffset="5495"/>
        <location id="R.string.tracking"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="115"
            column="13"
            startOffset="6226"
            endLine="115"
            endColumn="28"
            endOffset="6241"/>
        <location id="R.string.tracking_good"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="116"
            column="13"
            startOffset="6273"
            endLine="116"
            endColumn="33"
            endOffset="6293"/>
        <location id="R.string.tracking_move_device"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="120"
            column="13"
            startOffset="6497"
            endLine="120"
            endColumn="40"
            endOffset="6524"/>
        <location id="R.string.tracking_ready"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="123"
            column="13"
            startOffset="6737"
            endLine="123"
            endColumn="34"
            endOffset="6758"/>
        <location id="R.string.visualize_detected_planes"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="80"
            column="13"
            startOffset="4348"
            endLine="80"
            endColumn="45"
            endOffset="4380"/>
        <location id="R.style.WhiteSwitch"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="11"
            column="12"
            startOffset="489"
            endLine="11"
            endColumn="30"
            endOffset="507"/>
        <entry
            name="model"
            string="anim[pulse(U)],array[grid_formats(D),line_thickness_options(D),grid_formats_array(U)],attr[colorControlNormal(R),selectableItemBackgroundBorderless(R)],color[white(U),colorPrimary(D),colorPrimaryDark(D),colorAccent(D),statusBarBackground(D),navigationBarBackground(D),black(U),background(D),surface(D),error(D),textPrimary(D),textSecondary(D),textHint(D),buttonNormal(D),buttonPressed(D),buttonDisabled(D),ar_grid_color(D),ar_plane_color(D),ar_reticle_color(D),ar_anchor_color(D),transparent(D),notification_success(D),notification_warning(D),notification_error(D),gradient_start(D),gradient_end(D),panel_background(D),panel_divider(D),item_selected(D),item_pressed(D)],dimen[main_menu_minimized_width(D)],drawable[baseline_grid_on_24(D),baseline_image_20(D),baseline_image_24(D),circle_shape(U),deace_logo_sticker(U),ic_export(U),ic_grid(U),ic_grid_horizontal(U),ic_grid_square(U),ic_grid_vertical(U),ic_help(U),ic_image(U),ic_launcher_background(U),ic_launcher_foreground(U),ic_launcher_foreground_1(R),ic_maximize(D),ic_minimize(D),ic_reticle(U),ic_settings(U),rounded_button_background(U),rounded_panel_background(U),save_alt(D),selected_button_background(U),splash_background(U),toast_background(U),tracking_progress_background(U),baseline_grid_on_black_18(D),baseline_grid_on_black_20(D),baseline_grid_on_black_24(D),baseline_grid_on_black_36(D),baseline_grid_on_black_48(D),baseline_image_black_18(D),baseline_image_black_20(D),baseline_image_black_24(D),baseline_image_black_36(D),baseline_image_black_48(D)],id[ar_surface_view(D),loading_layout(D),loading_text(D),main_menu_layout(U),deace_logo(U),menu_icons_container(D),grid_tab_button(U),image_tab_button(U),export_tab_button(U),config_tab_button(U),grid_panel_layout(D),vertical_format_button(U),square_format_button(U),horizontal_format_button(U),fix_grid_button(U),grid_opacity_seekbar(U),grid_opacity_value(U),image_panel_layout(D),load_image_button(U),image_thumbnail(U),image_selected_status(U),fix_image_button(U),test_image_placement_button(U),release_image_button(U),image_opacity_seekbar(U),image_opacity_value(U),preserve_aspect_switch(U),image_size_text(U),export_panel_layout(D),export_button(U),deace_panel_layout(D),confirm_reinit_button(U),config_panel_scroll(D),config_panel_layout(D),show_planes_switch(D),reset_session_button(U),default_grid_format_spinner(U),default_grid_opacity_seekbar(U),image_quality_radio_group(U),normal_quality_radio(U),max_quality_radio(U),file_format_radio_group(U),jpg_format_radio(U),png_format_radio(U),reset_settings_button(U),help_about_button(U),reticle(D),tracking_indicator_container(U),tracking_quality_indicator(U),tracking_quality_text(U),tracking_init_text(U),tracking_progress_bar(U),help_button(U),quick_lock_image_button(D),grid_color_button(D),grid_line_thickness_spinner(D),default_image_opacity_seekbar(D),filename_pattern_edittext(D),save_directory_text(D),save_directory_button(D),toast_text(U),btn_close_tracking_help(U),release_grid_button(U),rotate_left_90_button(U),rotate_right_90_button(U),rotate_180_button(U),rotate_left_15_button(U),rotate_right_15_button(U),flip_horizontal_button(U)],layout[activity_main(U),config_panel(D),custom_toast(U),dialog_tracking_help(U),export_panel(D),grid_panel(D),image_panel(D)],mipmap[ic_launcher(U),ic_launcher_round(U)],string[app_name(U),scanning_environment(U),grid_tab(U),image_tab(U),export_tab(U),config_tab(U),grid_format(U),vertical(U),square(U),horizontal(U),fix_grid(U),grid_opacity(U),load_image(U),fix_image(U),release_image(U),opacity_format_50(U),no_image_loaded(U),export_ar_view(U),confirm_reinit_app(U),ar_settings(U),show_planes(U),reset_session(U),grid_options(U),default_grid_format(U),default_opacity(U),export_settings(U),normal_quality(U),max_quality(U),jpg_format(U),png_format(U),app_settings(U),reset_settings(U),help_about(U),tracking_lost(U),help(U),reset_ar_session(D),default_format(D),grid_color(D),choose_color(D),line_thickness(D),projected_image_options(D),preserve_aspect_ratio(D),image_quality(D),normal(D),maximum(D),file_format(D),png(D),jpg(D),filename_pattern(D),save_directory(D),choose_folder(D),general(D),export_instructions(D),release_grid(D),image_opacity(D),installing_arcore(U),initializing_ar(U),requesting_permissions(U),arcore_not_supported(U),permission_required(U),camera_permission_required_message(U),settings(U),exit(U),opacity_format(U),opacity_value(D),deace_tab(D),minimize_menu(D),app_restarting(U),baking_title(D),prompt_placeholder(D),results_placeholder(D),image_size_format(U),failed_to_load_image(U),image_load_error(U),storage_permission_required(U),storage_permission_explanation(U),storage_permission_denied(U),grant_permission(U),cancel(U),failed_to_capture_image(U),ar_session_not_ready(U),failed_to_save_image(U),export_success(U),image_saved_to(U),export_error(U),ar_session_reset(U),settings_reset(U),ok(U),visualize_detected_planes(D),about_text(U),tracking(D),tracking_good(D),tracking_limited(U),tracking_initializing(U),tracking_move_device(D),tracking_looking_for_surfaces(U),tracking_found_surfaces(U),tracking_ready(D),tracking_help_lost(U),tracking_help_limited(U),tracking_help_good(U)],style[Theme_DeaceAR(U),Theme_SplashScreen(U),WhiteSeekBar(U),TextAppearance_AppCompat_Medium(E),TextAppearance_AppCompat_Small(E),Widget_AppCompat_SeekBar(R),WhiteSwitch(D),Widget_AppCompat_CompoundButton_Switch(E),Theme_AppCompat_Light_NoActionBar(R),Theme_DeaceAR_Dialog(U),Theme_AppCompat_Dialog_Alert(R),DialogButtonStyle(U),Widget_AppCompat_Button_ButtonBar_AlertDialog(R)],xml[data_extraction_rules(U),backup_rules(U)];a^8,b^c,13^7,1e^7,1f^9,25^4,26^4,27^4,32^33,36^4,3c^29,8e^98^6^39^97^29^5^99^2b^9a^30^9b^2a^9c^37^4c^9d^9e^2e^9f^2d^a0^2c^38^a1^a2^fe^a3^a4^a5^a6^a7^a8^a9^aa^ab^ac^ad^ae^af^b0^b1^b2^b3^b4^b5^b6^b7^36^28^b8^3e^b9^2f^7d,8f^aa^ff^ba^ad^bb^1^af^bc^bd^be^2^bf^c0^b0^c1^c2^c3^c4^c5^c6^c7^c8^c9^ca^b6^b7,90^3d,91^39^6^28^38,92^a8^cb^100,93^9d^ff^9e^9f^a0^a1^cc^a2^a6^bc^bd^be^2,94^a3^a7^100^a4^38^a5^cd^ff^a6^c0,95^31^32,96^31^32,fc^104^c,fd^fc^3c,fe^101,102^103,105^106^c^6^39^107,107^108^6;;;"/>
    </map>

</incidents>
