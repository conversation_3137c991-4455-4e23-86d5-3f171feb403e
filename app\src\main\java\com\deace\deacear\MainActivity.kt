package com.deace.deacear

import android.Manifest
import android.app.Activity // Keep this import for Activity.RESULT_OK
import android.content.Intent
import android.graphics.PixelFormat
import android.net.Uri
import android.opengl.GLES30
import android.opengl.GLSurfaceView
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import android.provider.Settings
import android.util.Log
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.widget.Button
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher // Import needed
import com.deace.deacear.utils.MessageUtils
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.deace.deacear.ar.ARRenderer
import com.deace.deacear.ar.ARSessionManager
import com.deace.deacear.ar.ARSessionListener
import com.deace.deacear.ar.fixImageExt
import com.deace.deacear.ar.isSessionPausedExt
import com.deace.deacear.ar.onSessionPausedExt
import com.deace.deacear.ar.onSessionResumedExt
import com.deace.deacear.databinding.ActivityMainBinding
import com.deace.deacear.ui.ConfigPanel
import com.deace.deacear.ui.DeacePanel
import com.deace.deacear.ui.ExportPanel
import com.deace.deacear.ui.GridPanel
import com.deace.deacear.ui.ImagePanel
import com.deace.deacear.ui.MainMenu
import com.deace.deacear.utils.PermissionHelper
import com.deace.deacear.utils.RotationGestureDetector
import com.deace.deacear.utils.SettingsManager
import com.google.ar.core.ArCoreApk
import com.google.ar.core.Config
import com.google.ar.core.Frame
import com.google.ar.core.Plane
import com.google.ar.core.Session
import com.google.ar.core.TrackingState
import com.google.ar.core.exceptions.SessionPausedException
import com.google.ar.core.exceptions.UnavailableException
import javax.microedition.khronos.egl.EGLConfig
import javax.microedition.khronos.opengles.GL10

class MainActivity : AppCompatActivity(), ARSessionListener {
    private lateinit var binding: ActivityMainBinding

    // --- Constants ---
    private val REQUEST_IMAGE_PICK = 1001 // Request code for image picker

    // --- AR Components ---
    private lateinit var arSession: Session
    private lateinit var arRenderer: ARRenderer
    private lateinit var arSessionManager: ARSessionManager

    // --- UI Panels ---
    private lateinit var mainMenu: MainMenu
    private lateinit var gridPanel: GridPanel
    private lateinit var imagePanel: ImagePanel // Declare here
    private lateinit var exportPanel: ExportPanel
    private lateinit var configPanel: ConfigPanel
    private lateinit var deacePanel: DeacePanel

    // --- Utils & Detectors ---
    private lateinit var gestureDetector: GestureDetector
    private lateinit var scaleGestureDetector: ScaleGestureDetector
    private lateinit var rotationGestureDetector: RotationGestureDetector
    private lateinit var settingsManager: SettingsManager

    // --- Tracking Quality Indicator (spec 4.3) - Enhanced version ---
    private lateinit var trackingIndicatorContainer: LinearLayout
    private lateinit var trackingQualityIndicator: View
    private lateinit var trackingQualityText: TextView
    private lateinit var trackingInitText: TextView
    private lateinit var trackingProgressBar: ProgressBar
    private var trackingInitialized = false
    private var trackingProgress = 0
    private var lastPlaneDetectionTimeMs = 0L
    private var lastTrackingState: TrackingState? = null
    private var lastVerticalPlaneCount = 0
    private var trackingStateChangeTime = 0L
    private var isAnimating = false

    // --- Activity Result Launchers ---
    // Multiple Permissions Launcher
    private val requestPermissionsLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        Log.d("MainActivity", "Permission request result: $permissions")

        // Check specifically for camera permission
        val cameraGranted = permissions[Manifest.permission.CAMERA] ?: false

        if (allGranted) {
            Log.d("MainActivity", "All permissions granted. Proceeding with AR initialization.")
            // Add a short delay to let the system update its permission cache
            Thread.sleep(500)
            initializeAR()
        } else if (cameraGranted) {
            // Special case: If camera is granted but other permissions aren't, we can still try AR
            Log.d("MainActivity", "Camera permission granted but some other permissions denied. Attempting to continue.")
            // Add a short delay to let the system update its permission cache
            Thread.sleep(500)
            initializeAR()
        } else {
            Log.d("MainActivity", "Critical camera permission denied. Showing error dialog.")
            showPermissionDeniedDialog()
        }
    }

    // Storage Permissions Launcher
    private val storagePermissionsLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        Log.d("ImageDebug", "===== STORAGE PERMISSION RESULT RECEIVED =====")
        Log.d("ImageDebug", "Permission results: $permissions")
        Log.d("ImageDebug", "All permissions granted: $allGranted")

        // Check each permission individually
        permissions.forEach { (permission, granted) ->
            Log.d("ImageDebug", "Permission $permission: ${if (granted) "GRANTED" else "DENIED"}")
        }

        // Verify the current permission state using the helper
        val actualPermissionState = PermissionHelper.hasStoragePermissions(this)
        Log.d("ImageDebug", "Current permission state according to PermissionHelper: $actualPermissionState")

        // Check which panel is active
        val isExportPanelActive = exportPanel.isActive()
        val isImagePanelActive = imagePanel.isActive()
        Log.d("ImageDebug", "Export panel active: $isExportPanelActive, Image panel active: $isImagePanelActive")

        if (allGranted) {
            Log.d("ImageDebug", "All storage permissions granted, proceeding with actions")

            // If export panel is active, try to export again
            if (isExportPanelActive) {
                Log.d("ImageDebug", "Export panel is active, calling retryExport()")
                exportPanel.retryExport()
            }

            // If image panel is active, try to open the gallery
            if (isImagePanelActive) {
                Log.d("ImageDebug", "Image panel is active and permissions granted, opening gallery")
                // Open the gallery directly using our helper method
                openGalleryPicker()
            }
        } else {
            Log.d("ImageDebug", "Some storage permissions were denied")

            // Check if any permissions are permanently denied
            val permanentlyDenied = PermissionHelper.arePermissionsPermanentlyDenied(
                this,
                PermissionHelper.STORAGE_PERMISSIONS
            )
            Log.d("ImageDebug", "Permissions permanently denied: $permanentlyDenied")

            // Show appropriate message and action
            if (permanentlyDenied) {
                Log.d("ImageDebug", "Showing dialog for permanently denied permissions")

                // Show a dialog that offers to take the user to app settings
                AlertDialog.Builder(this)
                    .setTitle("Permission Required")
                    .setMessage("Storage permission is permanently denied. You need to enable it in the app settings to save images.")
                    .setPositiveButton("Open Settings") { _, _ ->
                        // Open app settings
                        val intent = Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                        intent.data = Uri.fromParts("package", packageName, null)
                        startActivity(intent)
                    }
                    .setNegativeButton("Cancel", null)
                    .show()
            } else {
                Log.d("ImageDebug", "Showing standard permission denied message")
                MessageUtils.showToast(
                    this,
                    getString(R.string.storage_permission_denied),
                    Toast.LENGTH_LONG
                )
            }
        }

        Log.d("ImageDebug", "===== STORAGE PERMISSION PROCESSING COMPLETED =====")
    }

    // Image Picker Launcher - Declared and Initialized at Class Level
    private val imagePickerLauncher: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        Log.d("MainActivity", "Image Picker Result received. Code: ${result.resultCode}")
        if (result.resultCode == RESULT_OK) {
            result.data?.data?.let { uri ->
                Log.d("MainActivity", "Image URI selected: $uri")

                // Take persistent permission for the URI
                try {
                    val takeFlags = Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_WRITE_URI_PERMISSION
                    contentResolver.takePersistableUriPermission(uri, takeFlags)
                    Log.d("MainActivity", "Successfully took persistable URI permission")
                } catch (e: Exception) {
                    Log.e("MainActivity", "Failed to take persistable URI permission", e)
                    // Continue anyway as this might not be critical
                }

                // Check if imagePanel is initialized before calling its method
                if (::imagePanel.isInitialized) {
                    // Show a toast to indicate the image is being loaded
                    MessageUtils.showToast(this, "Loading selected image...", Toast.LENGTH_SHORT)

                    // Extract filename from URI if possible for direct update
                    try {
                        val cursor = contentResolver.query(uri, null, null, null, null)
                        val nameIndex = cursor?.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
                        cursor?.moveToFirst()
                        val fileName = nameIndex?.let { cursor.getString(it) } ?: "Unknown"
                        cursor?.close()

                        // Update the status text directly in addition to passing to imagePanel
                        binding.imageSelectedStatus.text = fileName
                        Log.d("MainActivity", "Directly updated imageSelectedStatus to: $fileName")
                    } catch (e: Exception) {
                        Log.e("MainActivity", "Error extracting filename from URI", e)
                    }

                    // Pass the result URI back to the panel
                    imagePanel.onImageSelected(uri)
                } else {
                    Log.e("MainActivity", "Image selected but imagePanel is not initialized!")
                    MessageUtils.showToast(this, "Error: Image panel not initialized", Toast.LENGTH_SHORT)
                }
            } ?: run {
                Log.w("MainActivity", "Image Picker result OK but no data/URI found.")
                MessageUtils.showToast(this, "No image was selected", Toast.LENGTH_SHORT)
            }
        } else {
            Log.d("MainActivity", "Image selection cancelled or failed (Result Code: ${result.resultCode}).")
            if (result.resultCode != RESULT_CANCELED) {
                MessageUtils.showToast(this, "Image selection failed", Toast.LENGTH_SHORT)
            }
        }
    }

    // --- Activity Lifecycle ---
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        Log.d("MainActivity", "onCreate started.")
        settingsManager = SettingsManager(this)

        // Initialize UI components (including panels)
        initializeUI() // imagePickerLauncher is already initialized before this call

        // Initialize the Quick Lock Image button
        setupQuickLockImageButton()

        // Check and request permissions
        checkPermissionsAndInitAR()
        Log.d("MainActivity", "onCreate finished.")
    }

    private fun setupQuickLockImageButton() {
        binding.quickLockImageButton.setOnClickListener {
            if (::arSessionManager.isInitialized && !arSessionManager.isImageFixed) {
                arSessionManager.fixImageExt()
                MessageUtils.showToast(this, "Image locked in place", Toast.LENGTH_SHORT)
                binding.quickLockImageButton.visibility = View.GONE
            }
        }
    }

    override fun onResume() {
        super.onResume()
        Log.d("MainActivity", "onResume.")

        // Reset resume attempt counter
        resumeAttemptCount = 0

        // Attempt to resume AR session if initialized
        if (::arSession.isInitialized) {
            resumeARSession()
        } else {
            // Check if ARCore needs installation again (e.g., if INSTALL_REQUESTED happened)
            checkArCoreInstallation()
        }
    }

    private fun resumeARSession() {
        try {
            Log.d("MainActivity", "Starting resumeARSession - attempt $resumeAttemptCount")

            // Before resuming, explicitly make sure ARSessionManager knows we're in paused state
            // This ensures ARSessionManager doesn't try to get frames while we're resuming
            if (::arSessionManager.isInitialized) {
                arSessionManager.onSessionPausedExt()
            }

            // CRITICAL: Make sure we resume in the correct order

            // 1. First resume the GLSurfaceView to ensure GL context is available
            // This must happen before resuming the ARCore session to ensure the
            // EGL context is ready when ARCore tries to use it
            binding.arSurfaceView.onResume()
            Log.d("MainActivity", "GLSurfaceView resumed")

            // Add much longer delay between GLSurfaceView resume and ARCore session resume
            // This is critical to make sure the OpenGL context is fully established
            // Using a very long delay as requested to ensure GL context is completely ready
            Thread.sleep(1000) // Increased to 1000ms (1 second) for GL context to be fully ready

            // 2. Next resume the ARCore session
            // This is where the camera is activated and AR processing begins
            arSession.resume()
            Log.d("MainActivity", "AR Session resumed")

            // Add very long delay after ARCore session resume to allow internal state changes to complete
            // This helps avoid race conditions between resume and first frame update
            // The camera system needs substantial time to become fully active after resume call
            Thread.sleep(1500) // Increased to 1500ms (1.5 seconds) for camera pipeline to fully initialize

            // 3. Test if we can actually get a frame before notifying ARSessionManager
            // This is a critical verification step that ensures the camera is truly ready
            var frameTestSuccessful = false
            try {
                val testFrame = arSession.update()
                // ARCore's update() method never returns null, but we'll keep the check for robustness
                frameTestSuccessful = true
                Log.d("MainActivity", "Pre-notification frame test successful")
            } catch (e: Exception) {
                Log.w("MainActivity", "Pre-notification frame test failed: ${e.message}")
            }

            // 4. Finally notify ARSessionManager about session resumption AFTER
            // the session has been successfully resumed, so that state is correct
            if (::arSessionManager.isInitialized) {
                Log.d("MainActivity", "Notifying ARSessionManager about resumed session (frame test: $frameTestSuccessful)")
                arSessionManager.onSessionResumedExt()
                Log.d("MainActivity", "ARSessionManager notified of resume")

                // Verify the session state by checking if ARSessionManager still thinks it's paused
                // Allow a longer period for verification because ARSessionManager now has its own
                // retry mechanism with additional delays
                Handler(Looper.getMainLooper()).postDelayed({
                    if (!isFinishing && !isDestroyed) {
                        // Double-check if session is still considered paused
                        // Also verify if we can get an actual frame
                        var frameAcquirementSuccessful = false
                        try {
                            // This is a crucial test - attempt to actually get a frame
                            val testFrame = arSession.update()
                            // ARCore's update() method never returns null, but we'll keep the check for robustness
                            Log.d("MainActivity", "Successfully acquired a test frame during verification")
                            frameAcquirementSuccessful = true
                        } catch (e: Exception) {
                            Log.w("MainActivity", "Test frame acquisition failed: ${e.message}")
                        }

                        if ((arSessionManager.isSessionPausedExt() || !frameAcquirementSuccessful)
                                && resumeAttemptCount < MAX_RESUME_ATTEMPTS) {
                            // The session is still considered paused or can't get frames after our resume attempt
                            resumeAttemptCount++
                            Log.w("MainActivity", "ARCore session still not fully operational after resume. " +
                                    "Retry attempt $resumeAttemptCount of $MAX_RESUME_ATTEMPTS. " +
                                    "Session paused: ${arSessionManager.isSessionPausedExt()}, " +
                                    "Frame acquisition successful: $frameAcquirementSuccessful")

                            // Try again with an increasing delay - use longer delays
                            val increasingDelay = RESUME_RETRY_DELAY_MS * (resumeAttemptCount + 2)
                            Log.d("MainActivity", "Will retry resume in ${increasingDelay}ms")

                            Handler(Looper.getMainLooper()).postDelayed({
                                if (!isFinishing && !isDestroyed) {
                                    Log.d("MainActivity", "Attempting session resume retry")
                                    resumeARSession()
                                }
                            }, increasingDelay)
                        } else {
                            Log.d("MainActivity", "Session resume verification passed")
                            // Reset error flags when resuming was successful
                            hadDrawFrameError = false
                            hadSessionPausedError = false
                        }
                    }
                }, 200) // Longer delay for verification to allow ARSessionManager's verification to complete
            }

        } catch (e: Exception) {
            Log.e("MainActivity", "Failed to resume AR session", e)

            // If resume fails but we haven't exceeded retry attempts, try again
            if (resumeAttemptCount < MAX_RESUME_ATTEMPTS) {
                resumeAttemptCount++
                // Calculate increasing delay based on retry count - use longer delays for failures
                val increasingDelay = RESUME_RETRY_DELAY_MS * (resumeAttemptCount + 2)
                Log.w("MainActivity", "Resume attempt $resumeAttemptCount failed with exception: ${e.message}. " +
                        "Will retry in ${increasingDelay}ms")

                // Log more details about the exception to help debugging
                Log.e("MainActivity", "Exception during resume:", e)

                // Try again after an increasing delay
                Handler(Looper.getMainLooper()).postDelayed({
                    if (!isFinishing && !isDestroyed) {
                        resumeARSession()
                    }
                }, increasingDelay)
                return
            }

            // We've exceeded retry attempts or encountered a fatal error
            Log.e("MainActivity", "Maximum resume attempts exceeded", e)

            // If resume fails, mark the session as still paused in ARSessionManager
            if (::arSessionManager.isInitialized) {
                arSessionManager.onSessionPausedExt()
            }

            // Make sure GLSurfaceView state is consistent
            try {
                binding.arSurfaceView.onPause()
            } catch (innerEx: Exception) {
                Log.e("MainActivity", "Error pausing GLSurfaceView after resume failure", innerEx)
            }

            // Show a user-friendly error message
            Toast.makeText(this, "Failed to resume AR after multiple attempts: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onPause() {
        super.onPause()
        Log.d("MainActivity", "onPause.")
        if (::arSession.isInitialized) {
            try {
                // CRITICAL: Make sure we pause in the correct order (reverse of resume)

                // 1. First notify ARSessionManager that we're about to pause
                // This ensures the ARSessionManager won't try to update frames during pause transition
                if (::arSessionManager.isInitialized) {
                    arSessionManager.onSessionPausedExt()
                    Log.d("MainActivity", "ARSessionManager notified of pause")
                }

                // 2. Pause ARCore session to stop camera access and processing
                // This must happen before pausing the GLSurfaceView to prevent
                // attempts to use the camera after the app is backgrounded
                arSession.pause()
                Log.d("MainActivity", "AR Session paused")

                // 3. Finally pause the GLSurfaceView (stops rendering loop)
                // This must be last since the renderer might still be in the middle of
                // a draw call using ARCore resources
                binding.arSurfaceView.onPause()
                Log.d("MainActivity", "GLSurfaceView paused")
            } catch (e: Exception) {
                Log.e("MainActivity", "Error pausing AR session", e)

                // Even if there's an exception, we must ensure everything gets paused
                // to avoid resource leaks or camera issues

                // Ensure ARSessionManager knows about the pause state
                if (::arSessionManager.isInitialized) {
                    try {
                        arSessionManager.onSessionPausedExt()
                    } catch (innerEx: Exception) {
                        Log.e("MainActivity", "Error ensuring ARSessionManager pause state", innerEx)
                    }
                }

                // Try to pause ARCore session if the error was elsewhere
                try {
                    arSession.pause()
                } catch (innerEx: Exception) {
                    Log.e("MainActivity", "Error ensuring ARCore session pause", innerEx)
                }

                // Finally ensure GLSurfaceView is paused
                try {
                    binding.arSurfaceView.onPause()
                } catch (innerEx: Exception) {
                    Log.e("MainActivity", "Error ensuring GLSurfaceView pause", innerEx)
                }
            }
        }
    }

    /**
     * Check if ARSessionManager is initialized
     * @return true if ARSessionManager is initialized, false otherwise
     */
    fun isARSessionManagerInitialized(): Boolean {
        return ::arSessionManager.isInitialized
    }

    /**
     * Get the ARSessionManager instance
     * @return The ARSessionManager instance or null if not initialized
     */
    fun getARSessionManager(): ARSessionManager? {
        return if (::arSessionManager.isInitialized) arSessionManager else null
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d("MainActivity", "onDestroy.")

        // Release the ARRenderer resources first
        if (::arRenderer.isInitialized) {
            arRenderer.release()
            Log.d("MainActivity", "ARRenderer resources released.")
        }

        // Reset the ARSessionManager
        if (::arSessionManager.isInitialized) {
            arSessionManager.resetARSession()
            Log.d("MainActivity", "ARSessionManager reset.")
        }

        // Finally close the ARCore session
        if (::arSession.isInitialized) {
            arSession.close()
            Log.d("MainActivity", "AR Session closed.")
        }
    }

    // Handle image selection result from gallery
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        Log.d("ImageDebug", "===== ACTIVITY RESULT RECEIVED =====")
        Log.d("ImageDebug", "onActivityResult: requestCode=$requestCode, resultCode=$resultCode")
        Log.d("ImageDebug", "Has data: ${data != null}, Has URI: ${data?.data != null}")

        if (data != null) {
            Log.d("ImageDebug", "Intent action: ${data.action}, type: ${data.type}")
            Log.d("ImageDebug", "Intent extras: ${data.extras?.keySet()?.joinToString() ?: "none"}")
        }

        if (requestCode == REQUEST_IMAGE_PICK) {
            if (resultCode == RESULT_OK) {
                // Get the selected image URI
                val imageUri = data?.data

                if (imageUri != null) {
                    Log.d("ImageDebug", "Image selected from gallery: $imageUri")
                    Log.d("ImageDebug", "URI scheme: ${imageUri.scheme}, authority: ${imageUri.authority}")
                    Log.d("ImageDebug", "URI path: ${imageUri.path}")

                    // Check if the URI is readable
                    try {
                        val inputStream = contentResolver.openInputStream(imageUri)
                        val canRead = inputStream != null
                        inputStream?.close()
                        Log.d("ImageDebug", "Can read from URI: $canRead")
                    } catch (e: Exception) {
                        Log.e("ImageDebug", "Cannot read from URI", e)
                    }

                    // Take persistent permission for the URI
                    try {
                        val takeFlags = Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_WRITE_URI_PERMISSION
                        contentResolver.takePersistableUriPermission(imageUri, takeFlags)
                        Log.d("ImageDebug", "Successfully took persistable URI permission")
                    } catch (e: Exception) {
                        Log.e("ImageDebug", "Failed to take persistable URI permission", e)
                        Log.e("ImageDebug", "Exception type: ${e.javaClass.name}, message: ${e.message}")
                        // Continue anyway as this might not be critical
                    }

                    // Pass the URI to the image panel
                    if (::imagePanel.isInitialized) {
                        Log.d("ImageDebug", "ImagePanel is initialized, passing URI to onImageSelected")
                        // Show a toast to indicate the image is being loaded
                        MessageUtils.showToast(this, "Loading selected image...", Toast.LENGTH_SHORT)

                        // Extract filename from URI if possible for direct update
                        try {
                            val cursor = contentResolver.query(imageUri, null, null, null, null)
                            val nameIndex = cursor?.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
                            cursor?.moveToFirst()
                            val fileName = nameIndex?.let { cursor.getString(it) } ?: "Unknown"
                            cursor?.close()

                            // Update the status text directly in addition to passing to imagePanel
                            binding.imageSelectedStatus.text = fileName
                            Log.d("ImageDebug", "Directly updated imageSelectedStatus to: $fileName in onActivityResult")
                        } catch (e: Exception) {
                            Log.e("ImageDebug", "Error extracting filename from URI in onActivityResult", e)
                        }

                        // Pass the result URI to the panel
                        imagePanel.onImageSelected(imageUri)
                    } else {
                        Log.e("ImageDebug", "Image selected but imagePanel is not initialized!")
                        MessageUtils.showToast(this, "Error: Image panel not initialized", Toast.LENGTH_SHORT)
                    }
                } else {
                    Log.w("ImageDebug", "No image URI returned from gallery despite RESULT_OK")
                    MessageUtils.showToast(this, "No image was selected", Toast.LENGTH_SHORT)
                }
            } else {
                Log.w("ImageDebug", "Image selection cancelled or failed (Result Code: $resultCode)")
                if (resultCode != RESULT_CANCELED) {
                    MessageUtils.showToast(this, "Image selection failed with code: $resultCode", Toast.LENGTH_SHORT)
                }
            }
        } else {
            Log.d("ImageDebug", "Received activity result for unknown request code: $requestCode")
        }

        Log.d("ImageDebug", "===== ACTIVITY RESULT PROCESSING COMPLETED =====")
    }

    // --- Initialization ---
    private fun checkPermissionsAndInitAR() {
        Log.d("MainActivity", "Checking required permissions...")

        // Check camera permission first - it's the most critical for AR
        val cameraGranted = PermissionHelper.hasCameraPermission(this)
        Log.d("MainActivity", "Camera permission check: $cameraGranted")

        // Check all permissions
        if (PermissionHelper.hasAllPermissions(this)) {
            Log.d("MainActivity", "All permissions already granted. Initializing AR...")
            // Clear any pending permission error dialogs that might be showing
            dismissPermissionDialogs()
            // Check ARCore installation status before proceeding
            checkArCoreInstallation()
        } else if (cameraGranted) {
            // If camera is granted but not all permissions, we can still try to proceed
            Log.d("MainActivity", "Camera permission granted but not all permissions. Trying to initialize AR anyway...")
            dismissPermissionDialogs()
            checkArCoreInstallation()
        } else {
            Log.d("MainActivity", "Permissions needed. Requesting...")
            // Force a UI update to clear any stale permission messages
            binding.loadingLayout.visibility = View.VISIBLE
            binding.loadingText.text = getString(com.deace.deacear.R.string.requesting_permissions)
            // Launch permission request
            requestPermissionsLauncher.launch(PermissionHelper.REQUIRED_PERMISSIONS)
        }
    }

    private var permissionDialog: AlertDialog? = null

    private fun dismissPermissionDialogs() {
        // Dismiss any pending permission dialogs
        permissionDialog?.dismiss()
        permissionDialog = null
    }

    private fun checkArCoreInstallation() {
        Log.d("MainActivity", "Checking ARCore installation status...")
        // Check if ARCore is installed and ready. If not, request installation.
        // This handles cases where installation was requested but not completed,
        // or if it wasn't checked previously.
        val availability = ArCoreApk.getInstance().checkAvailability(this)
        if (availability.isTransient) {
            // Re-query availability later.
            Log.d("MainActivity", "ARCore availability is transient. Retrying later...")
            // Consider using a Handler().postDelayed here for robustness
            Handler(Looper.getMainLooper()).postDelayed({ checkArCoreInstallation() }, 200)
            return
        }

        if (availability.isSupported) {
            Log.d("MainActivity", "ARCore is supported.")
            // If AR isn't initialized yet, do it now.
            if (!::arSession.isInitialized) {
                initializeAR()
            } else {
                Log.d("MainActivity", "AR already initialized.")
                binding.loadingLayout.visibility = View.GONE // Ensure loading is hidden
            }
        } else { // Unsupported or requires update/installation
            Log.d("MainActivity", "ARCore not supported or needs install/update. Status: $availability")
            // Handle non-recoverable error (UNSUPPORTED)
            if (availability == ArCoreApk.Availability.UNSUPPORTED_DEVICE_NOT_CAPABLE) {
                Log.e("MainActivity", "ARCore is not supported on this device.")
                showArCoreNotSupportedDialog()
                return
            }
            // Request installation or update if needed.
            try {
                val installStatus = ArCoreApk.getInstance().requestInstall(this, true)
                when(installStatus) {
                    ArCoreApk.InstallStatus.INSTALL_REQUESTED -> {
                        Log.d("MainActivity", "ARCore installation requested. Waiting for onResume.")
                        binding.loadingLayout.visibility = View.VISIBLE
                        binding.loadingText.text = getString(com.deace.deacear.R.string.installing_arcore) // Ensure this string exists
                        // Installation result will be handled by ARCore framework, onResume check helps.
                    }
                    ArCoreApk.InstallStatus.INSTALLED -> {
                        Log.d("MainActivity", "ARCore installed successfully during check. Initializing AR.")
                        // Should theoretically not happen if availability check was accurate, but handle anyway.
                        if (!::arSession.isInitialized) initializeAR()
                    }
                }
            } catch (e: UnavailableException) {
                Log.e("MainActivity", "ARCore UnavailableException during requestInstall", e)
                showArCoreNotSupportedDialog() // Treat as unsupported
            }
        }
    }

    private fun initializeUI() {
        Log.d("MainActivity", "Initializing UI...")
        // Setup AR surface view
        binding.arSurfaceView.apply {
            preserveEGLContextOnPause = true // Important for ARCore stability
            setEGLContextClientVersion(3)
            setEGLConfigChooser(8, 8, 8, 8, 16, 0) // Alpha for transparency? Check ARCore examples

            // Create EGL context before setting the renderer
            setRenderer(object: GLSurfaceView.Renderer {
                override fun onSurfaceCreated(gl: GL10?, config: EGLConfig?) {
                    // Check initialization as renderer might be created before AR is fully ready
                    try {
                        if (::arRenderer.isInitialized) {
                            arRenderer.onSurfaceCreated(gl, config)
                        } else {
                            Log.w("MainActivity", "onSurfaceCreated called but ARRenderer not initialized.")
                        }
                    } catch (e: Exception) {
                        Log.e("MainActivity", "Error in onSurfaceCreated", e)
                    }
                }

                override fun onSurfaceChanged(gl: GL10?, width: Int, height: Int) {
                    try {
                        if (::arRenderer.isInitialized) {
                            arRenderer.onSurfaceChanged(gl, width, height)
                        } else {
                            Log.w("MainActivity", "onSurfaceChanged called but ARRenderer not initialized.")
                        }
                    } catch (e: Exception) {
                        Log.e("MainActivity", "Error in onSurfaceChanged", e)
                    }
                }

                override fun onDrawFrame(gl: GL10?) {
                    // Critical path - avoid logging here unless debugging performance
                    try {
                        // Make sure both renderer and session manager are initialized
                        if (!::arRenderer.isInitialized || !::arSessionManager.isInitialized) {
                            // Still initializing, just draw a blank frame
                            GLES30.glClear(GLES30.GL_COLOR_BUFFER_BIT or GLES30.GL_DEPTH_BUFFER_BIT)
                            return
                        }

                        // Let arRenderer handle the drawing, but we need to check session state
                        // first to potentially avoid repeated SessionPausedException errors
                        if (arSessionManager.isSessionPausedExt()) {
                            // Session is known to be paused - don't try to get a frame
                            if (!hadSessionPausedError) {
                                Log.d("MainActivity", "Session is paused during render cycle")
                                hadSessionPausedError = true
                            }

                            // Still call onDrawFrame - ARRenderer will handle paused state gracefully
                            arRenderer.onDrawFrame(gl)
                            return
                        }

                        // Session appears to be active - try to get a frame
                        var frame: Frame? = null
                        try {
                            // Get the current frame - this is where SessionPausedException might occur
                            frame = arSessionManager.getCurrentFrame()

                            // If we get a valid frame, update visuals
                            if (frame != null) {
                                // Update reticle hit test with the valid frame
                                arSessionManager.updateReticleHitTest(frame, binding.arSurfaceView.width, binding.arSurfaceView.height)

                                // Update tracking quality indicator (spec 4.3)
                                updateTrackingQualityIndicator(frame)

                                // Reset error flags if we got a successful frame
                                if (hadDrawFrameError || hadSessionPausedError) {
                                    Log.d("MainActivity", "Successfully recovered frame updates after errors")
                                    hadDrawFrameError = false
                                    hadSessionPausedError = false
                                }
                            } else {
                                // Failed to get frame despite session not being marked paused
                                // This could indicate a temporary glitch or state inconsistency
                                if (!hadDrawFrameError) {
                                    Log.d("MainActivity", "Failed to get frame despite session not marked as paused")
                                    hadDrawFrameError = true
                                }
                            }

                            // Always call arRenderer.onDrawFrame which now handles null frames gracefully
                            arRenderer.onDrawFrame(gl)

                        } catch (e: com.google.ar.core.exceptions.SessionPausedException) {
                            // Session was paused during frame retrieval
                            if (!hadSessionPausedError) {
                                Log.d("MainActivity", "Session paused during render")
                                hadSessionPausedError = true

                                // Update our state tracking
                                arSessionManager.onSessionPausedExt()
                            }

                            // Still try to render a frame - ARRenderer will handle paused state gracefully
                            arRenderer.onDrawFrame(gl)

                        } catch (e: Exception) {
                            // Only log once per session or use a throttling mechanism to avoid log spam
                            if (!hadDrawFrameError) {
                                Log.e("MainActivity", "Error in onDrawFrame", e)
                                hadDrawFrameError = true
                            }

                            // Still try to render something - let the renderer decide what's appropriate
                            try {
                                arRenderer.onDrawFrame(gl)
                            } catch (renderEx: Exception) {
                                // Last resort - just clear the screen if even renderer fails
                                GLES30.glClear(GLES30.GL_COLOR_BUFFER_BIT or GLES30.GL_DEPTH_BUFFER_BIT)
                            }
                        }
                    } catch (e: Exception) {
                        // Outermost exception handler - catches any other unexpected errors
                        if (!hadDrawFrameError) {
                            Log.e("MainActivity", "Unexpected error in onDrawFrame", e)
                            hadDrawFrameError = true
                        }

                        // Last resort - just clear the screen
                        try {
                            GLES30.glClear(GLES30.GL_COLOR_BUFFER_BIT or GLES30.GL_DEPTH_BUFFER_BIT)
                        } catch (glEx: Exception) {
                            // Nothing more we can do
                        }
                    }
                }
            })
            renderMode = GLSurfaceView.RENDERMODE_CONTINUOUSLY

            // Make sure we're creating a translucent surface
            holder.setFormat(PixelFormat.TRANSLUCENT)

            Log.d("MainActivity", "GLSurfaceView setup complete.")
        }

        try {
            // Initialize all panels in parallel without delays
            Log.d("MainActivity", "Initializing all UI panels...")

            // Initialize all panels at once
            mainMenu = MainMenu(binding.mainMenuLayout, this)
            gridPanel = GridPanel(binding.gridPanelLayout, this)
            exportPanel = ExportPanel(binding.exportPanelLayout, this, settingsManager)
            configPanel = ConfigPanel(binding.configPanelLayout, this, settingsManager)
            // Make sure the ScrollView is properly referenced
            binding.configPanelScroll.visibility = View.GONE
            deacePanel = DeacePanel(binding.deacePanelLayout, this)

            // Initialize ImagePanel - Pass the class-level launcher callback
            imagePanel = ImagePanel(
                binding.imagePanelLayout,
                this, // Context
                settingsManager,
                requestImageLauncher = { intent -> // Pass the launcher callback
                    try {
                        Log.d("MainActivity", "Launching image picker via callback.")
                        imagePickerLauncher.launch(intent) // Use the class-level launcher
                    } catch (e: Exception) {
                        Log.e("MainActivity", "Failed to launch image picker", e)
                        MessageUtils.showToast(this, "Cannot open image picker", Toast.LENGTH_SHORT)
                    }
                }
            )

            // Setup tab selection listeners for main menu
            mainMenu.setOnGridTabSelected {
                Log.d("MainActivity", "Grid tab selected")
                showGridPanel()
            }

            mainMenu.setOnImageTabSelected {
                Log.d("MainActivity", "Image tab selected")
                showImagePanel()
            }

            mainMenu.setOnExportTabSelected {
                Log.d("MainActivity", "Export tab selected")
                showExportPanel()
            }

            mainMenu.setOnConfigTabSelected {
                Log.d("MainActivity", "Config tab selected")
                showConfigPanel()
            }

            mainMenu.setOnDeaceLogoSelected {
                Log.d("MainActivity", "Deace logo selected")
                showDeacePanel()
            }

            // Add direct click listeners to the tab buttons as a backup
            // This ensures they work even if the MainMenu callbacks fail
            val imageTabButton = binding.mainMenuLayout.findViewById<ImageButton>(R.id.image_tab_button)
            imageTabButton?.setOnClickListener {
                Log.d("MainActivity", "Image tab button clicked directly")
                // Add visual feedback
                it.alpha = 0.5f
                it.animate().alpha(1.0f).setDuration(300).start()
                // Show the image panel
                showImagePanel()
            }

            // Initialize tracking quality indicator (spec 4.3) - Enhanced version
            trackingIndicatorContainer = findViewById(R.id.tracking_indicator_container)
            trackingQualityIndicator = findViewById(R.id.tracking_quality_indicator)
            trackingQualityText = findViewById(R.id.tracking_quality_text)
            trackingInitText = findViewById(R.id.tracking_init_text)
            trackingProgressBar = findViewById(R.id.tracking_progress_bar)

            // Set initial tracking state
            trackingInitText.text = getString(R.string.tracking_initializing)
            trackingProgressBar.progress = 0

            // Apply pulsing animation to the tracking indicator during initialization
            val pulseAnimation = AnimationUtils.loadAnimation(this, R.anim.pulse)
            trackingQualityIndicator.startAnimation(pulseAnimation)
            isAnimating = true

            // Tracking help button has been removed from the layout
            // Help functionality is now only available through the bottom right help button

            // Initialize help button (spec 4.4)
            val helpButton = findViewById<View>(R.id.help_button)
            helpButton.setOnClickListener {
                showHelpDialog()
            }

            // Hide the main menu by default to align with specs (3.1)
            // Only show minimal UI on startup
            mainMenu.minimize()

            // Start with the image panel as the primary functionality
            showImagePanel()

            // Set a flag to show first-time user tooltips
            if (!settingsManager.hasSeenTutorial) {
                showFirstTimeTooltips()
            }

            Log.d("MainActivity", "All UI Panels successfully initialized with minimal UI.")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error during panel initialization", e)
            Toast.makeText(this, "UI initialization error: ${e.message}", Toast.LENGTH_LONG).show()
        }

        // Setup gesture detector for AR interactions
        setupGestureDetector()
        Log.d("MainActivity", "Gesture Detector setup complete.")
    }

    private fun setupGestureDetector() {
        // Basic tap and scroll gesture detector
        gestureDetector = GestureDetector(this, object : GestureDetector.SimpleOnGestureListener() {
            override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
                Log.d("GestureDetector", "onSingleTapConfirmed at (${e.x}, ${e.y})")
                if (::arSessionManager.isInitialized) {
                    // Make sure the session is not paused
                    if (!arSessionManager.isSessionPausedExt()) {
                        // Check if we have an image loaded
                        val arImage = arSessionManager.getCurrentImage()
                        val verticalPlanes = countVerticalPlanes()

                        // Log detailed tap information for debugging
                        Log.d("GestureDetector", "Tap debug - Image loaded: ${arImage != null}, Image fixed: ${arSessionManager.isImageFixed}, Vertical planes: $verticalPlanes")

                        // Get the latest vertical plane count before sending tap
                        val currentVerticalPlanes = countVerticalPlanes()
                        Log.d("GestureDetector", "Current vertical planes count before tap: $currentVerticalPlanes")

                        // Forward tap to ARSessionManager with additional logging
                        try {
                            Log.d("GestureDetector", "Sending tap to ARSessionManager at (${e.x}, ${e.y})")
                            arSessionManager.handleTap(e)
                            Log.d("GestureDetector", "Tap successfully sent to ARSessionManager")
                        } catch (ex: Exception) {
                            Log.e("GestureDetector", "Error sending tap to ARSessionManager", ex)
                            MessageUtils.showToast(
                                this@MainActivity,
                                "Error processing tap: ${ex.message}",
                                Toast.LENGTH_SHORT
                            )
                        }

                        // Show appropriate feedback based on system state
                        if (arImage != null && !arSessionManager.isImageFixed) {
                            // Get the latest vertical plane count directly from ARSessionManager
                            // This ensures we have the most up-to-date information
                            val currentVerticalPlanes = countVerticalPlanes()
                            Log.d("GestureDetector", "Current vertical planes count: $currentVerticalPlanes")

                            if (currentVerticalPlanes > 0) {
                                // We have vertical planes and an image to place
                                MessageUtils.showToast(
                                    this@MainActivity,
                                    "Placing image on wall. You can adjust it with gestures.",
                                    Toast.LENGTH_SHORT
                                )
                            } else {
                                // Image loaded but no vertical surfaces yet
                                MessageUtils.showToast(
                                    this@MainActivity,
                                    "No vertical walls detected yet. Continue scanning to find walls.",
                                    Toast.LENGTH_LONG
                                )
                            }
                        } else if (arImage == null) {
                            // No image loaded yet
                            // Check if we have a filename displayed, which would indicate the user has selected an image
                            val imageFilename = binding.imageSelectedStatus.text.toString()
                            if (imageFilename != "No image selected" && imageFilename.isNotEmpty()) {
                                // User has selected an image but it's not loaded in AR
                                Log.e("MainActivity", "Image filename displayed but AR image is null: $imageFilename")
                                Toast.makeText(
                                    this@MainActivity,
                                    "Image selected but not loaded properly. Please try selecting the image again.",
                                    Toast.LENGTH_LONG
                                ).show()
                            } else {
                                // No image selected
                                Toast.makeText(
                                    this@MainActivity,
                                    "Select an image first using the Image panel.",
                                    Toast.LENGTH_SHORT
                                ).show()
                            }
                        }
                    } else {
                        Log.d("GestureDetector", "Session is paused, ignoring tap")
                    }
                }
                return true
            }

            /**
             * Helper method to count vertical planes in the current frame
             * with enhanced logging for debugging
             */
            private fun countVerticalPlanes(): Int {
                try {
                    val frame = arSession.update()
                    val planes: Collection<Plane> = frame.getUpdatedTrackables(Plane::class.java)

                    // Count all planes by type for detailed logging
                    val allPlanes = planes.size
                    val trackingPlanes = planes.count { it.trackingState == TrackingState.TRACKING }
                    val verticalPlanes = planes.count {
                        it.trackingState == TrackingState.TRACKING && it.type == Plane.Type.VERTICAL
                    }
                    val horizontalPlanes = planes.count {
                        it.trackingState == TrackingState.TRACKING &&
                        (it.type == Plane.Type.HORIZONTAL_UPWARD_FACING ||
                         it.type == Plane.Type.HORIZONTAL_DOWNWARD_FACING)
                    }

                    // Log detailed plane information
                    Log.d("PlaneDetectionDebug", "Plane detection status:")
                    Log.d("PlaneDetectionDebug", "- Total planes: $allPlanes")
                    Log.d("PlaneDetectionDebug", "- Tracking planes: $trackingPlanes")
                    Log.d("PlaneDetectionDebug", "- Vertical planes: $verticalPlanes")
                    Log.d("PlaneDetectionDebug", "- Horizontal planes: $horizontalPlanes")

                    // Log details about each vertical plane
                    if (verticalPlanes > 0) {
                        Log.d("PlaneDetectionDebug", "Vertical plane details:")
                        planes.filter { it.trackingState == TrackingState.TRACKING && it.type == Plane.Type.VERTICAL }
                            .forEachIndexed { index, plane ->
                                Log.d("PlaneDetectionDebug", "  Plane $index:")
                                Log.d("PlaneDetectionDebug", "  - Size: ${plane.extentX}m x ${plane.extentZ}m")
                                Log.d("PlaneDetectionDebug", "  - Position: (${plane.centerPose.tx()}, ${plane.centerPose.ty()}, ${plane.centerPose.tz()})")
                            }
                    }

                    return verticalPlanes
                } catch (e: Exception) {
                    Log.e("GestureDetector", "Error counting vertical planes", e)
                    return 0
                }
            }

            override fun onScroll(
                e1: MotionEvent?, // Make nullable for safety
                e2: MotionEvent,
                distanceX: Float,
                distanceY: Float
            ): Boolean {
                Log.d("GestureDetector", "onScroll: dX=$distanceX, dY=$distanceY")
                // e1 can sometimes be null on edge cases, check if needed by handleScroll
                if (e1 != null && ::arSessionManager.isInitialized) {
                    // Set isDragging to true
                    arSessionManager.startDragging()
                    arSessionManager.handleScroll(e1, e2, distanceX, distanceY)
                }
                return true // Consume event
            }

            // Add onDown to return true, needed for onScroll to work reliably
            override fun onDown(e: MotionEvent): Boolean {
                return true
            }
        })

        // Scale (pinch) gesture detector
        scaleGestureDetector = ScaleGestureDetector(this, object : ScaleGestureDetector.SimpleOnScaleGestureListener() {
            override fun onScale(detector: ScaleGestureDetector): Boolean {
                Log.d("GestureDetector", "onScale: factor=${detector.scaleFactor}")
                if (::arSessionManager.isInitialized) {
                    arSessionManager.handleScale(detector.scaleFactor)
                }
                return true
            }

            override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
                Log.d("GestureDetector", "onScaleBegin")
                if (::arSessionManager.isInitialized) {
                    arSessionManager.startScaling()
                }
                return true
            }

            override fun onScaleEnd(detector: ScaleGestureDetector) {
                Log.d("GestureDetector", "onScaleEnd")
                if (::arSessionManager.isInitialized) {
                    arSessionManager.endScaling()
                }
            }
        })

        // Rotation gesture detector
        rotationGestureDetector = RotationGestureDetector(object : RotationGestureDetector.OnRotationGestureListener {
            override fun onRotation(rotationDetector: RotationGestureDetector): Boolean {
                Log.d("GestureDetector", "onRotation: angle=${rotationDetector.rotationChangeDegrees}")
                if (::arSessionManager.isInitialized) {
                    arSessionManager.handleRotation(rotationDetector.rotationChangeDegrees)
                }
                return true
            }
        })

        // Important: Set touch listener on the SurfaceView
        binding.arSurfaceView.setOnTouchListener { _, event ->
            var consumed = false

            // Handle user interaction for auto-hiding controls (spec 4.3)
            if (event.actionMasked == MotionEvent.ACTION_DOWN) {
                // Show controls on tap
                mainMenu.onUserInteraction()

                // Also show panels if they're active but hidden
                if (::gridPanel.isInitialized && gridPanel.isActive()) {
                    gridPanel.show()
                }
                if (::imagePanel.isInitialized && imagePanel.isActive()) {
                    imagePanel.show()
                }
                if (::exportPanel.isInitialized && exportPanel.isActive()) {
                    exportPanel.show()
                }
                if (::configPanel.isInitialized && configPanel.isActive()) {
                    configPanel.show()
                }
            }

            // First, check if we have multiple pointers (for scale/rotate)
            if (event.pointerCount >= 2) {
                scaleGestureDetector.onTouchEvent(event)
                rotationGestureDetector.onTouchEvent(event)
                consumed = true
            } else {
                // Otherwise, use standard gesture detector for taps and single-finger scrolls
                consumed = gestureDetector.onTouchEvent(event)
            }

            // Handle ACTION_UP separately if needed (e.g., for ending drag/scale)
            if (event.actionMasked == MotionEvent.ACTION_UP ||
                event.actionMasked == MotionEvent.ACTION_CANCEL) {
                if (::arSessionManager.isInitialized) {
                    arSessionManager.handleTouchEnd()
                    Log.d("GestureDetector", "ACTION_UP/CANCEL sent to ARSessionManager")
                }
            }

            // Always return true to consume the event since we have multiple detectors
            true
        }
    }


    private fun initializeAR() {
        // Prevent double initialization
        if (::arSession.isInitialized) {
            Log.w("MainActivity", "initializeAR called but AR session already exists.")
            binding.loadingLayout.visibility = View.GONE // Ensure loading is hidden
            return
        }
        Log.d("MainActivity", "Initializing AR...")

        try {
            // Force dismiss any previously showing permission dialog before rechecking permissions
            dismissPermissionDialogs()

            // Ensure the loading indicator is visible during initialization
            binding.loadingLayout.visibility = View.VISIBLE
            binding.loadingText.text = getString(com.deace.deacear.R.string.initializing_ar)

            // We only need to check camera permission for AR functionality
            // Double check camera permission to avoid race conditions where permissions might have been revoked
            if (!PermissionHelper.hasCameraPermission(this)) {
                Log.e("MainActivity", "Camera permission missing at AR initialization time")
                showPermissionDeniedDialog()
                return
            }

            // Storage permissions are not required for AR initialization, only for saving exports
            // Log storage permission state for informational purposes
            val storageGranted = PermissionHelper.hasStoragePermissions(this)
            Log.d("MainActivity", "Storage permissions granted: $storageGranted (not required for AR functionality)")

            // No need for a second camera permission check, we already did it above

            // Add a delay to ensure camera system is fully ready after permission check
            Thread.sleep(500)

            // Create the session with improved error handling
            try {
                arSession = Session(this) // Requires CAMERA permission
                Log.d("MainActivity", "AR Session created successfully.")
            } catch (e: Exception) {
                // Handle specific camera permission issues
                if (e.message?.contains("camera", ignoreCase = true) == true) {
                    Log.e("MainActivity", "Camera unavailable despite permission being granted", e)
                    showArCoreNotSupportedDialog("Camera unavailable: ${e.message}")
                    return
                } else {
                    // Re-throw for general handler
                    throw e
                }
            }

            // Configure ARCore session with enhanced settings for vertical plane detection
            val config = Config(arSession)

            // Set plane finding mode to detect both horizontal and vertical planes
            config.planeFindingMode = Config.PlaneFindingMode.HORIZONTAL_AND_VERTICAL

            // Use LATEST_CAMERA_IMAGE for more responsive updates
            config.updateMode = Config.UpdateMode.LATEST_CAMERA_IMAGE

            // Enable light estimation for better rendering and plane detection
            config.lightEstimationMode = Config.LightEstimationMode.ENVIRONMENTAL_HDR

            // Set focus mode to auto focus for better tracking
            config.focusMode = Config.FocusMode.AUTO

            // Enable depth for better plane detection (if supported)
            try {
                // Check if depth is supported using exception handling
                config.depthMode = Config.DepthMode.AUTOMATIC
                Log.d("MainActivity", "Depth mode enabled for better plane detection")
            } catch (e: IllegalArgumentException) {
                // Depth mode not supported on this device
                Log.d("MainActivity", "Depth mode not supported on this device")
            }

            // Enhanced plane detection settings
            try {
                // Enable instant placement for faster initial detection
                if (config.instantPlacementMode != Config.InstantPlacementMode.DISABLED) {
                    config.instantPlacementMode = Config.InstantPlacementMode.LOCAL_Y_UP
                    Log.d("MainActivity", "Instant placement mode enabled for faster detection")
                }
            } catch (e: Exception) {
                Log.d("MainActivity", "Instant placement mode not available on this device")
            }

            // Optimize for plane detection performance
            try {
                // Use high quality mode for better plane detection if available
                config.planeFindingMode = Config.PlaneFindingMode.HORIZONTAL_AND_VERTICAL
                Log.d("MainActivity", "Enhanced plane finding mode configured")
            } catch (e: Exception) {
                Log.d("MainActivity", "Enhanced plane finding mode not available")
            }

            // Apply the configuration
            arSession.configure(config)

            Log.d("MainActivity", "AR Session configured with enhanced settings for vertical plane detection")


            // Create Renderer and Session Manager
            arRenderer = ARRenderer(this, arSession)

            // Set the GLSurfaceView reference in the renderer for immediate render requests
            arRenderer.setGLSurfaceView(binding.arSurfaceView)
            Log.d("MainActivity", "Set GLSurfaceView reference in ARRenderer for immediate render requests")

            arSessionManager = ARSessionManager(this, arSession, arRenderer, settingsManager)
            // Set this activity as the listener for AR session events
            arSessionManager.setARSessionListener(this)
            Log.d("MainActivity", "ARRenderer and ARSessionManager created.")


            // Pass ARSessionManager reference to UI panels AFTER it's created
            Log.d("MainActivity", "Setting ARSessionManager for UI panels...")
            gridPanel.setARSessionManager(arSessionManager)
            imagePanel.setARSessionManager(arSessionManager)
            exportPanel.setARSessionManager(arSessionManager)
            configPanel.setARSessionManager(arSessionManager)
            Log.d("MainActivity", "ARSessionManager set for panels.")


            // Hide loading indicator now that AR is ready
            binding.loadingLayout.visibility = View.GONE
            Log.d("MainActivity", "AR Initialization complete. Loading layout hidden.")


        } catch (e: UnavailableException) {
            Log.e("MainActivity", "ARCore UnavailableException during initialization", e)
            showArCoreNotSupportedDialog(e.message)
        } catch (e: Exception) {
            Log.e("MainActivity", "Unexpected exception during AR initialization", e)
            Toast.makeText(this, "Failed to initialize AR: ${e.localizedMessage}", Toast.LENGTH_LONG).show()
            // Consider finishing the activity or providing a retry option
            finish()
        }
    }

    // --- Dialogs ---
    private fun showPermissionDeniedDialog() {
        Log.w("MainActivity", "Camera permission denied.")

        // Force a delay before checking permissions to ensure the system has updated its state
        Thread.sleep(500)

        // Double-check if camera permission is actually granted despite the callback reporting otherwise
        if (PermissionHelper.hasCameraPermission(this)) {
            Log.d("MainActivity", "Permission check contradiction: callback says denied but camera permission is actually granted")
            // If camera permission IS granted, proceed with AR initialization
            checkArCoreInstallation()
            return
        }

        // Triple-check individual permissions and log their state for debugging
        val cameraGranted = PermissionHelper.hasCameraPermission(this)
        val storageGranted = PermissionHelper.hasStoragePermissions(this)
        Log.d("MainActivity", "Permission states - Camera: $cameraGranted, Storage: $storageGranted")

        // If camera permission is granted but storage isn't, we might still be able to proceed
        if (cameraGranted && !storageGranted) {
            Log.d("MainActivity", "Camera permission granted but storage denied. Attempting to proceed with ARCore.")
            checkArCoreInstallation()
            return
        }

        // Store dialog reference so we can dismiss it if needed
        permissionDialog = androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(com.deace.deacear.R.string.permission_required) // Ensure strings exist
            .setMessage(com.deace.deacear.R.string.camera_permission_required_message)
            .setPositiveButton(com.deace.deacear.R.string.settings) { _, _ ->
                // Open app settings
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                intent.data = Uri.fromParts("package", packageName, null)
                startActivity(intent)
                finish() // Close app after sending to settings
            }
            .setNegativeButton(com.deace.deacear.R.string.exit) { _, _ -> finish() }
            .setCancelable(false)
            .create()

        permissionDialog?.show()
    }

    private fun showArCoreNotSupportedDialog(message: String? = null) {
        Log.e("MainActivity", "ARCore not supported dialog shown. Message: $message")

        // Check if the error is related to camera not being available
        if (message?.contains("camera", ignoreCase = true) == true ||
            message?.contains("Camera", ignoreCase = true) == true) {

            // This could be a camera permission issue even if permissions appear granted
            if (PermissionHelper.hasCameraPermission(this)) {
                Log.d("MainActivity", "Camera-related ARCore error despite camera permission being granted")

                // Force a restart of the session to resolve potential permission cache issues
                try {
                    // If we have an existing session, close it
                    if (::arSession.isInitialized) {
                        try {
                            arSession.close()
                            Log.d("MainActivity", "Closed existing AR session to force a clean restart")
                        } catch (e: Exception) {
                            Log.e("MainActivity", "Error closing AR session", e)
                        }
                    }

                    // Wait a moment for camera release
                    Thread.sleep(1000)

                    // Try creating a new session with a delay after permission verification
                    if (PermissionHelper.hasCameraPermission(this)) {
                        Log.d("MainActivity", "Attempting to force-create a new AR session after permission re-verification")
                        try {
                            arSession = Session(this)
                            Log.d("MainActivity", "Successfully force-created new AR session!")
                            // If we got here, session creation worked, so continue with initialization
                            val config = Config(arSession)

                            // Set plane finding mode to detect both horizontal and vertical planes
                            config.planeFindingMode = Config.PlaneFindingMode.HORIZONTAL_AND_VERTICAL

                            // Use LATEST_CAMERA_IMAGE for more responsive updates
                            config.updateMode = Config.UpdateMode.LATEST_CAMERA_IMAGE

                            // Enable light estimation for better rendering
                            config.lightEstimationMode = Config.LightEstimationMode.ENVIRONMENTAL_HDR

                            // Set focus mode to auto focus for better tracking
                            config.focusMode = Config.FocusMode.AUTO

                            // Enable depth for better plane detection (if supported)
                            try {
                                // Check if depth is supported using exception handling
                                config.depthMode = Config.DepthMode.AUTOMATIC
                                Log.d("MainActivity", "Depth mode enabled for better plane detection (recovery path)")
                            } catch (e: IllegalArgumentException) {
                                // Depth mode not supported on this device
                                Log.d("MainActivity", "Depth mode not supported on this device (recovery path)")
                            }

                            // Apply the configuration
                            arSession.configure(config)

                            Log.d("MainActivity", "AR Session configured with enhanced settings (recovery path)")

                            // Complete initialization
                            arRenderer = ARRenderer(this, arSession)

                            // Set the GLSurfaceView reference in the renderer for immediate render requests
                            arRenderer.setGLSurfaceView(binding.arSurfaceView)
                            Log.d("MainActivity", "Set GLSurfaceView reference in ARRenderer for immediate render requests (recovery path)")

                            arSessionManager = ARSessionManager(this, arSession, arRenderer, settingsManager)

                            // Configure UI
                            gridPanel.setARSessionManager(arSessionManager)
                            imagePanel.setARSessionManager(arSessionManager)
                            exportPanel.setARSessionManager(arSessionManager)
                            configPanel.setARSessionManager(arSessionManager)

                            // Hide loading indicator
                            binding.loadingLayout.visibility = View.GONE

                            // Resume session
                            resumeARSession()

                            return
                        } catch (e: Exception) {
                            Log.e("MainActivity", "Force session creation also failed", e)
                        }
                    }
                } catch (e: Exception) {
                    Log.e("MainActivity", "Error during session recreation attempt", e)
                }

                // If we reach here, our recovery attempt failed - show the camera error dialog
                androidx.appcompat.app.AlertDialog.Builder(this)
                    .setTitle("Camera Access Error")
                    .setMessage("ARCore cannot access the camera even though permissions appear to be granted. " +
                               "This could be due to:\n\n" +
                               "1. Another app using the camera\n" +
                               "2. System restriction on background camera access\n" +
                               "3. Permission cache inconsistency\n\n" +
                               "Try restarting your device and ensuring camera access is enabled for this app in system settings.")
                    .setPositiveButton("Settings") { _, _ ->
                        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                        intent.data = Uri.fromParts("package", packageName, null)
                        startActivity(intent)
                        finish()
                    }
                    .setNegativeButton("Exit") { _, _ -> finish() }
                    .setCancelable(false)
                    .show()
                return
            }
        }

        // Standard ARCore not supported dialog
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(com.deace.deacear.R.string.arcore_not_supported) // Ensure string exists
            .setMessage(message ?: "This application requires ARCore, which is not available on this device.")
            .setPositiveButton(com.deace.deacear.R.string.ok) { _, _ -> finish() } // Exit app
            .setCancelable(false)
            .show()
    }


    // Removed GLSurfaceView.Renderer methods since we're now using an anonymous implementation

    private var hadDrawFrameError = false
    private var hadSessionPausedError = false

    // Add a retry counter to track resume attempts
    private var resumeAttemptCount = 0
    private val MAX_RESUME_ATTEMPTS = 7 // Increased from 5 to 7 for more retries
    private val RESUME_RETRY_DELAY_MS = 1000L // Increased from 500ms to 1000ms (1 second) for very long delays

    // --- Public Methods for UI Panel Interaction ---
    fun showGridPanel() {
        // Don't hide the main menu since the grid panel is now positioned to the right of it
        // We want both to be visible
        mainMenu.show()

        // Hide the grid icon when showing the grid panel
        mainMenu.hideGridIcon()

        // Show grid panel and hide other panels
        gridPanel.show()
        imagePanel.hide()
        exportPanel.hide()
        configPanel.hide()
        if (::deacePanel.isInitialized) {
            deacePanel.hide()
        }

        // Show a toast to guide the user
        MessageUtils.showToast(
            this,
            "Tap on a detected surface to place a grid",
            Toast.LENGTH_SHORT
        )
    }

    /**
     * Request storage permissions for export functionality
     */
    fun requestStoragePermission() {
        Log.d("ImageDebug", "===== REQUESTING STORAGE PERMISSIONS =====")
        Log.d("ImageDebug", "Current permission state: ${PermissionHelper.hasStoragePermissions(this)}")

        // Log which permissions we're requesting
        val permissions = PermissionHelper.STORAGE_PERMISSIONS
        Log.d("ImageDebug", "Requesting permissions: ${permissions.joinToString()}")

        // Check if we need to show rationale for any permission
        val needsRationale = permissions.any { PermissionHelper.shouldShowRationale(this, it) }
        Log.d("ImageDebug", "Should show rationale: $needsRationale")

        // Check if permissions are permanently denied
        val permanentlyDenied = PermissionHelper.arePermissionsPermanentlyDenied(this, permissions)
        Log.d("ImageDebug", "Permissions permanently denied: $permanentlyDenied")

        // Launch the permission request
        Log.d("ImageDebug", "Launching permission request via storagePermissionsLauncher")
        storagePermissionsLauncher.launch(permissions)
    }

    /**
     * Open the gallery picker directly
     */
    private fun openGalleryPicker() {
        Log.d("ImageDebug", "===== STARTING GALLERY PICKER PROCESS =====")
        Log.d("ImageDebug", "Device: ${Build.MANUFACTURER} ${Build.MODEL}, Android ${Build.VERSION.SDK_INT}")
        Log.d("ImageDebug", "Storage permissions granted: ${PermissionHelper.hasStoragePermissions(this)}")

        try {
            // Try the most reliable method first - ACTION_PICK
            Log.d("ImageDebug", "Attempting to open gallery with ACTION_PICK")
            val galleryIntent = Intent(Intent.ACTION_PICK)
            galleryIntent.type = "image/*"
            galleryIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            galleryIntent.addFlags(Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION)

            // Check if there's an app that can handle this intent
            val activities = packageManager.queryIntentActivities(galleryIntent, 0)
            Log.d("ImageDebug", "Number of apps that can handle ACTION_PICK: ${activities.size}")
            if (activities.isNotEmpty()) {
                Log.d("ImageDebug", "Apps that can handle ACTION_PICK: ${activities.joinToString { it.activityInfo.name }}")
            }

            Log.d("ImageDebug", "Launching image picker with ACTION_PICK via launcher")
            try {
                imagePickerLauncher.launch(galleryIntent)
                Log.d("ImageDebug", "imagePickerLauncher.launch called successfully with ACTION_PICK")
                MessageUtils.showToast(this, "Select an image from your gallery", Toast.LENGTH_SHORT)
                return
            } catch (e: Exception) {
                Log.e("ImageDebug", "Failed to launch image picker with ACTION_PICK via launcher", e)
                // Continue to fallback methods
            }
        } catch (e: Exception) {
            Log.e("ImageDebug", "Failed to open gallery with ACTION_PICK", e)
            Log.e("ImageDebug", "Exception type: ${e.javaClass.name}, message: ${e.message}")

            // Try alternative approach - ACTION_GET_CONTENT
            try {
                Log.d("ImageDebug", "Attempting alternative gallery approach with ACTION_GET_CONTENT")
                val intent = Intent(Intent.ACTION_GET_CONTENT)
                intent.type = "image/*"
                intent.addCategory(Intent.CATEGORY_OPENABLE)
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                intent.addFlags(Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION)

                // Check if there's an app that can handle this intent
                val activities = packageManager.queryIntentActivities(intent, 0)
                Log.d("ImageDebug", "Number of apps that can handle ACTION_GET_CONTENT: ${activities.size}")
                if (activities.isNotEmpty()) {
                    Log.d("ImageDebug", "Apps that can handle ACTION_GET_CONTENT: ${activities.joinToString { it.activityInfo.name }}")
                }

                Log.d("ImageDebug", "Launching image picker with ACTION_GET_CONTENT via launcher")
                try {
                    imagePickerLauncher.launch(intent)
                    Log.d("ImageDebug", "imagePickerLauncher.launch called successfully with ACTION_GET_CONTENT")
                    MessageUtils.showToast(this, "Select an image from your gallery", Toast.LENGTH_SHORT)
                    return
                } catch (e2: Exception) {
                    Log.e("ImageDebug", "Failed to launch image picker with ACTION_GET_CONTENT via launcher", e2)
                    // Continue to fallback methods
                }
            } catch (e2: Exception) {
                Log.e("ImageDebug", "Failed to open gallery with ACTION_GET_CONTENT", e2)
                Log.e("ImageDebug", "Exception type: ${e2.javaClass.name}, message: ${e2.message}")

                // Try one last approach - Intent.createChooser
                try {
                    Log.d("ImageDebug", "Attempting last resort with Intent.createChooser")
                    val chooserIntent = Intent.createChooser(
                        Intent(Intent.ACTION_GET_CONTENT).apply {
                            type = "image/*"
                            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                        },
                        "Select Image"
                    )
                    try {
                        imagePickerLauncher.launch(chooserIntent)
                        Log.d("ImageDebug", "imagePickerLauncher.launch called successfully with createChooser")
                        MessageUtils.showToast(this, "Select an image from your gallery", Toast.LENGTH_SHORT)
                        return
                    } catch (e4: Exception) {
                        Log.e("ImageDebug", "Failed to launch image picker with createChooser via launcher", e4)
                        // If all launcher attempts fail, try the deprecated method as a last resort
                        try {
                            startActivityForResult(chooserIntent, REQUEST_IMAGE_PICK)
                            Log.d("ImageDebug", "Fallback to deprecated startActivityForResult called successfully")
                            return
                        } catch (e5: Exception) {
                            Log.e("ImageDebug", "Even deprecated startActivityForResult failed", e5)
                            // Continue to error handling
                        }
                    }
                } catch (e3: Exception) {
                    Log.e("ImageDebug", "All gallery approaches failed completely", e3)
                    Log.e("ImageDebug", "Final exception type: ${e3.javaClass.name}, message: ${e3.message}")
                    MessageUtils.showToast(this, "Cannot open gallery: ${e.message}", Toast.LENGTH_SHORT)
                }
            }
        }

        Log.d("ImageDebug", "===== GALLERY PICKER PROCESS COMPLETED =====")
    }

    fun showImagePanel() {
        Log.d("MainActivity", "Toggle image panel")

        // Show the main menu when switching to image panel
        mainMenu.show()

        // Hide other panels
        gridPanel.hide()
        exportPanel.hide()
        configPanel.hide()
        if (::deacePanel.isInitialized) {
            deacePanel.hide()
        }

        // Toggle the image panel visibility
        if (imagePanel.isActive()) {
            // If panel is already visible, hide it
            imagePanel.hide()
            binding.imagePanelLayout.visibility = View.GONE
            Log.d("MainActivity", "Image panel was visible, now hidden")
            return
        } else {
            // Show the image panel
            imagePanel.show()
            binding.imagePanelLayout.visibility = View.VISIBLE
            Log.d("MainActivity", "Image panel was hidden, now visible")
        }

        // Check if we have storage permissions and request if needed
        if (!PermissionHelper.hasStoragePermissions(this)) {
            Log.d("MainActivity", "Storage permissions not granted, requesting...")
            requestStoragePermission()
            MessageUtils.showToast(this, "Please grant storage permissions to load images", Toast.LENGTH_SHORT)
        } else {
            Log.d("MainActivity", "Storage permissions already granted")
        }

        // Show instructions toast when image panel is shown
        MessageUtils.showToast(this, "Tap the 'Load Image' button to select an image from your gallery", Toast.LENGTH_LONG)

        // Add a direct click handler to the load image button
        val loadButton = binding.imagePanelLayout.findViewById<Button>(R.id.load_image_button)
        if (loadButton == null) {
            Log.e("MainActivity", "Could not find load image button! Panel layout: ${binding.imagePanelLayout}")
            // Try to debug the issue by listing all child views
            try {
                if (binding.imagePanelLayout is ViewGroup) {
                    val viewGroup = binding.imagePanelLayout as ViewGroup
                    Log.d("MainActivity", "Image panel has ${viewGroup.childCount} children")
                    for (i in 0 until viewGroup.childCount) {
                        val child = viewGroup.getChildAt(i)
                        Log.d("MainActivity", "Child $i: ${child.javaClass.simpleName}, id: ${resources.getResourceEntryName(child.id)}")
                    }
                }
            } catch (e: Exception) {
                Log.e("MainActivity", "Error inspecting image panel children", e)
            }
        } else {
            // Make sure the button is enabled and visible
            loadButton.isEnabled = true
            loadButton.visibility = View.VISIBLE
            Log.d("MainActivity", "Found load image button, setting click listener")

            // Add a direct click listener to the button
            loadButton.setOnClickListener {
                Log.d("ImageDebug", "===== LOAD IMAGE BUTTON CLICKED =====")
                Log.d("ImageDebug", "Button state: enabled=${loadButton.isEnabled}, visible=${loadButton.visibility == View.VISIBLE}")

                // Show visual feedback
                loadButton.alpha = 0.5f
                loadButton.animate().alpha(1.0f).setDuration(300).start()

                // Request storage permission if needed
                if (!PermissionHelper.hasStoragePermissions(this)) {
                    Log.d("ImageDebug", "Storage permissions not granted, requesting permissions")
                    // The gallery will be opened in the permission callback if granted
                    requestStoragePermission()
                    MessageUtils.showToast(this, "Please grant storage permissions to load images", Toast.LENGTH_SHORT)
                    return@setOnClickListener
                } else {
                    Log.d("ImageDebug", "Storage permissions already granted, proceeding to open gallery")
                }

                // Use our helper method to open the gallery
                openGalleryPicker()
            }

            // Highlight the button with a short delay to draw attention to it
            Handler(Looper.getMainLooper()).postDelayed({
                if (!isFinishing && !isDestroyed) {
                    loadButton.alpha = 0.5f
                    loadButton.animate()
                        .alpha(1.0f)
                        .setDuration(500)
                        .start()
                }
            }, 1000)
        }
    }

    fun showExportPanel() {
        // Don't hide the main menu since the export panel is now positioned to the right of it
        // We want both to be visible
        mainMenu.show()

        // Show export panel and hide other panels
        gridPanel.hide()
        imagePanel.hide()
        exportPanel.show()
        configPanel.hide()
        if (::deacePanel.isInitialized) {
            deacePanel.hide()
        }
    }

    fun showConfigPanel() {
        // Show the main menu since the config panel is positioned below it
        mainMenu.show()

        // Show config panel and hide other panels
        gridPanel.hide()
        imagePanel.hide()
        exportPanel.hide()
        configPanel.show()
        if (::deacePanel.isInitialized) {
            deacePanel.hide()
        }
    }

    fun showDeacePanel() {
        // Don't hide the main menu since the deace panel is now positioned to the right of it
        // We want both to be visible
        mainMenu.show()

        // Show deace panel and hide other panels
        gridPanel.hide()
        imagePanel.hide()
        exportPanel.hide()
        configPanel.hide()
        if (::deacePanel.isInitialized) {
            deacePanel.show()
        }
    }

    fun toggleMainMenu() {
        mainMenu.toggle()
    }

    // Method to show the grid icon in the main menu
    fun showGridIcon() {
        if (::mainMenu.isInitialized) {
            mainMenu.showGridIcon()
        }
    }

    // Optional: If other components need the renderer instance
    fun getArRenderer(): ARRenderer? {
        return if (::arRenderer.isInitialized) arRenderer else null
    }

    /**
     * Updates the visibility of the quick lock image button based on the image anchor state
     * and applies animation for better visibility
     */
    private fun updateLockImageButtonVisibility() {
        if (::arSessionManager.isInitialized) {
            // Show the button only when an image is placed but not fixed
            val shouldShow = arSessionManager.getCurrentImage() != null && !arSessionManager.isImageFixed

            if (shouldShow && binding.quickLockImageButton.visibility != View.VISIBLE) {
                // Apply animation when showing the button
                binding.quickLockImageButton.visibility = View.VISIBLE
                binding.quickLockImageButton.alpha = 0f
                binding.quickLockImageButton.animate()
                    .alpha(1f)
                    .setDuration(500)
                    .start()

                // Also make it pulse to draw attention
                val pulseAnimation = AnimationUtils.loadAnimation(this, R.anim.pulse)
                binding.quickLockImageButton.startAnimation(pulseAnimation)

                // Show a toast reminding user to lock the image when positioned correctly
                MessageUtils.showToast(
                    this,
                    "Image placed! Press 'Lock Image' when positioned correctly.",
                    Toast.LENGTH_LONG
                )
            } else if (!shouldShow && binding.quickLockImageButton.visibility == View.VISIBLE) {
                // Animate hiding
                binding.quickLockImageButton.animate()
                    .alpha(0f)
                    .setDuration(300)
                    .withEndAction { binding.quickLockImageButton.visibility = View.GONE }
                    .start()
            }
        }
    }

    // ARSessionListener implementation

    /**
     * Called when an image is placed on a surface
     */
    override fun onImagePlaced() {
        runOnUiThread {
            updateLockImageButtonVisibility()
        }
    }

    /**
     * Called when an image is fixed in position
     */
    override fun onImageFixed() {
        runOnUiThread {
            updateLockImageButtonVisibility()
        }
    }

    /**
     * Called when an image is released from its fixed position
     */
    override fun onImageReleased() {
        runOnUiThread {
            updateLockImageButtonVisibility()
        }
    }

    /**
     * Shows tooltips and guidance for first-time users
     * Implements the contextual help requirement from specs (4.4)
     */
    private fun showFirstTimeTooltips() {
        // Create a handler to post delayed tooltips
        val handler = Handler(Looper.getMainLooper())

        // Show initial scanning guidance
        MessageUtils.showToast(
            this,
            getString(com.deace.deacear.R.string.scanning_environment),
            Toast.LENGTH_LONG
        )

        // After 5 seconds, show a tooltip about the menu
        handler.postDelayed({
            if (!isFinishing && !isDestroyed) {
                MessageUtils.showToast(
                    this,
                    "The menu in the top-left has tabs for Grid, Image, Export, and Settings",
                    Toast.LENGTH_LONG
                )

                // Show the menu briefly to highlight it
                mainMenu.show()

                // Then after 3 seconds, show another tip
                handler.postDelayed({
                    if (!isFinishing && !isDestroyed) {
                        MessageUtils.showToast(
                            this,
                            "Tap anywhere to show controls if they're hidden. Tap the ? for help.",
                            Toast.LENGTH_LONG
                        )

                        // Then minimize the menu after showing the second tip
                        handler.postDelayed({
                            if (!isFinishing && !isDestroyed) {
                                mainMenu.minimize()
                            }
                        }, 2000)
                    }
                }, 3000)
            }
        }, 5000)

        // Mark that the user has seen the tutorial
        settingsManager.hasSeenTutorial = true
    }

    /**
     * Shows the help dialog with usage instructions
     * Implements the contextual help requirement from specs (4.4)
     */
    private fun showHelpDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Help & Tips")
            .setMessage(
                "• Scan a wall by moving your device slowly\n\n" +
                "• The top-left menu has 4 tabs:\n" +
                "  - Grid: Create and adjust grid overlays\n" +
                "  - Image: Select and adjust images (main feature)\n" +
                "  - Export: Save your AR view as an image\n" +
                "  - Config: Adjust app settings\n\n" +
                "• Tap anywhere to show controls if they're hidden\n" +
                "• Use pinch to resize the image\n" +
                "• Use two fingers to rotate\n" +
                "• Use one finger to move the image\n" +
                "• Tap 'Fix Image' when positioned correctly\n" +
                "• Adjust opacity with the slider\n" +
                "• The colored dot in the top-right shows tracking quality"
            )
            .setPositiveButton(com.deace.deacear.R.string.ok, null)
            .show()
    }

    /**
     * Updates the tracking quality indicator based on the current tracking state
     * Implements the enhanced tracking quality indicator requirement
     */
    private fun updateTrackingQualityIndicator(frame: Frame) {
        // Get the camera tracking state
        val camera = frame.camera
        val trackingState = camera.trackingState

        // Log the tracking state for debugging
        Log.d("TrackingIndicator", "Current tracking state: $trackingState")

        // Log detailed tracking information
        logTrackingDetails(frame)

        // Count planes to determine tracking progress
        val planes: Collection<Plane> = frame.getUpdatedTrackables(Plane::class.java)

        // Count all tracking planes and vertical planes specifically
        val trackingPlanes = planes.count { it.trackingState == TrackingState.TRACKING }
        val verticalPlanes = planes.count {
            it.trackingState == TrackingState.TRACKING && it.type == Plane.Type.VERTICAL
        }

        // Log plane counts for debugging
        Log.d("TrackingIndicator", "Tracking planes: $trackingPlanes, Vertical planes: $verticalPlanes")

        // Update tracking progress based on planes detected
        if (trackingState == TrackingState.TRACKING) {
            // Record the time when we first get good tracking
            if (lastPlaneDetectionTimeMs == 0L) {
                lastPlaneDetectionTimeMs = System.currentTimeMillis()
            }

            // Calculate progress based on number of planes and time elapsed
            val timeElapsedMs = System.currentTimeMillis() - lastPlaneDetectionTimeMs

            // Make time-based progress faster (15ms instead of 25ms per 1%)
            val timeBasedProgress = (timeElapsedMs / 15).toInt().coerceAtMost(100)

            // Make plane-based progress even more generous (40% per plane instead of 30%)
            // Give extra weight to vertical planes (60% per vertical plane instead of 50%)
            val planeBasedProgress = ((trackingPlanes * 40) + (verticalPlanes * 60)).coerceAtMost(100)

            // Use the maximum of time-based and plane-based progress
            trackingProgress = maxOf(trackingProgress, maxOf(timeBasedProgress, planeBasedProgress))

            // FIXED: Only show green and "good tracking" when we have vertical planes
            if (trackingProgress >= 100 || verticalPlanes > 0) {
                trackingInitialized = true
                trackingProgress = 100 // Force to 100% when we have vertical planes
            } else if (trackingPlanes > 0) {
                // We have some planes but not vertical - show intermediate state
                trackingInitialized = true
                trackingProgress = 85 // High but not 100%
            }

            // Only set to green when we have actual planes detected
            if (!trackingInitialized && trackingPlanes > 0) {
                trackingProgress = maxOf(trackingProgress, 50) // Ensure at least 50% progress when we have any planes
            }
        }

        // Update the UI on the main thread
        runOnUiThread {
            try {
                // Make sure the indicator is visible
                trackingIndicatorContainer.visibility = View.VISIBLE

                // Check if tracking state has changed
                val currentTime = System.currentTimeMillis()
                val isNewTrackingState = lastTrackingState != trackingState

                // Only update UI if there's a significant change (tracking state changed or vertical plane status changed)
                // Add debounce timer to prevent flickering
                val verticalPlaneStatusChanged = (verticalPlanes > 0) != (lastVerticalPlaneCount > 0)
                val significantTimeElapsed = currentTime - trackingStateChangeTime > 1000 // Increased from 500ms to 1000ms

                if (isNewTrackingState || verticalPlaneStatusChanged || significantTimeElapsed) {
                    // Update tracking state and record changes
                    if (isNewTrackingState || verticalPlaneStatusChanged || significantTimeElapsed) {
                        trackingStateChangeTime = currentTime
                        lastTrackingState = trackingState
                        lastVerticalPlaneCount = verticalPlanes

                        // Log the change reason
                        if (isNewTrackingState) {
                            Log.d("TrackingIndicator", "Tracking state changed to: $trackingState")
                        }
                        if (verticalPlaneStatusChanged) {
                            Log.d("TrackingIndicator", "Vertical plane status changed: $verticalPlanes vertical planes")
                        }
                    }

                    // Update tracking state indicator
                    when (trackingState) {
                        TrackingState.TRACKING -> {
                            // Set indicator color based on whether we have vertical planes
                            if (verticalPlanes > 0) {
                                // Green for vertical planes
                                Log.d("TrackingIndicator", "Setting indicator to GREEN (vertical planes detected)")
                                // Force color update with post() to ensure UI thread updates it
                                trackingQualityIndicator.post {
                                    try {
                                        trackingQualityIndicator.setBackgroundColor(resources.getColor(android.R.color.holo_green_light, theme))
                                        // Force redraw
                                        trackingQualityIndicator.invalidate()
                                        Log.d("TrackingIndicator", "GREEN color set and invalidated")
                                    } catch (e: Exception) {
                                        Log.e("TrackingIndicator", "Error setting GREEN color", e)
                                    }
                                }
                                MessageUtils.showStatusMessage(trackingQualityText, "Good Tracking - Tap on wall to place")
                            } else if (trackingPlanes > 0) {
                                // Yellow for horizontal planes only
                                Log.d("TrackingIndicator", "Setting indicator to YELLOW (only horizontal planes)")
                                // Force color update with post() to ensure UI thread updates it
                                trackingQualityIndicator.post {
                                    try {
                                        trackingQualityIndicator.setBackgroundColor(resources.getColor(android.R.color.holo_orange_light, theme))
                                        // Force redraw
                                        trackingQualityIndicator.invalidate()
                                        Log.d("TrackingIndicator", "YELLOW color set and invalidated")
                                    } catch (e: Exception) {
                                        Log.e("TrackingIndicator", "Error setting YELLOW color", e)
                                    }
                                }
                                MessageUtils.showStatusMessage(trackingQualityText, "Scanning for walls...")
                            } else {
                                // Blue for tracking but no planes
                                Log.d("TrackingIndicator", "Setting indicator to BLUE (tracking but no planes)")
                                // Force color update with post() to ensure UI thread updates it
                                trackingQualityIndicator.post {
                                    try {
                                        trackingQualityIndicator.setBackgroundColor(resources.getColor(android.R.color.holo_blue_light, theme))
                                        // Force redraw
                                        trackingQualityIndicator.invalidate()
                                        Log.d("TrackingIndicator", "BLUE color set and invalidated")
                                    } catch (e: Exception) {
                                        Log.e("TrackingIndicator", "Error setting BLUE color", e)
                                    }
                                }
                                MessageUtils.showStatusMessage(trackingQualityText, "Move around to detect surfaces")
                            }

                        // If we're tracking well, update initialization progress
                        if (!trackingInitialized) {
                            // Check for planes - prioritize vertical planes but accept any planes
                            if (verticalPlanes > 0) {
                                // We found vertical planes - this is what we want!
                                MessageUtils.showStatusMessage(trackingInitText, getString(R.string.tracking_found_surfaces) + " (vertical)")
                                trackingProgressBar.progress = 100

                                // Immediately mark as initialized when we find vertical planes
                                trackingInitialized = true

                                // Show success toast for vertical plane detection
                                val currentTime = System.currentTimeMillis()
                                if (currentTime - lastToastTimeMs > TOAST_INTERVAL_MS) {
                                    MessageUtils.showToast(
                                        this@MainActivity,
                                        "Vertical wall detected! Tap on it to place content",
                                        Toast.LENGTH_LONG
                                    )
                                    lastToastTimeMs = currentTime
                                }
                            } else if (trackingPlanes > 0) {
                                // Found some planes but not vertical ones yet - still consider this good enough
                                MessageUtils.showStatusMessage(trackingInitText, getString(R.string.tracking_found_surfaces))
                                trackingProgressBar.progress = trackingProgress

                                // MODIFIED: Mark as initialized even with just horizontal planes
                                trackingInitialized = true

                                // Show guidance toast
                                val currentTime = System.currentTimeMillis()
                                if (currentTime - lastToastTimeMs > TOAST_INTERVAL_MS) {
                                    MessageUtils.showToast(
                                        this@MainActivity,
                                        "Surfaces detected! You can place content now, or continue scanning for walls",
                                        Toast.LENGTH_LONG
                                    )
                                    lastToastTimeMs = currentTime
                                }
                            } else {
                                // No planes detected yet, but tracking is good
                                MessageUtils.showStatusMessage(trackingInitText, getString(R.string.tracking_looking_for_surfaces))
                                trackingProgressBar.progress = trackingProgress

                                // FIXED: Don't say tracking is good if no planes are detected
                                // Instead encourage the user to keep scanning for vertical surfaces
                                if (trackingProgress > 70) {
                                    // Progress is good but we still need planes
                                    trackingProgressBar.progress = 70  // Cap at 70% until we get planes

                                    // Show guidance toast
                                    val currentTime = System.currentTimeMillis()
                                    if (currentTime - lastToastTimeMs > TOAST_INTERVAL_MS) {
                                        MessageUtils.showToast(
                                            this@MainActivity,
                                            "Continue scanning to find walls for placement",
                                            Toast.LENGTH_LONG
                                        )
                                        lastToastTimeMs = currentTime
                                    }
                                }
                            }
                        } else {
                            // Once fully initialized, show ready message and stop animation
                            MessageUtils.showStatusMessage(trackingInitText, getString(R.string.tracking_help_good))
                            trackingProgressBar.progress = 100

                            // Only clear animation if it's currently running
                            if (isAnimating) {
                                trackingQualityIndicator.clearAnimation()
                                isAnimating = false
                            }

                            // Show a success toast when we first reach good tracking
                            val currentTime = System.currentTimeMillis()
                            if (currentTime - lastToastTimeMs > TOAST_INTERVAL_MS * 3) { // Less frequent reminders
                                // FIXED: Make messages clearer and more specific
                                val message = if (verticalPlanes > 0) {
                                    "Vertical wall detected - tap on the wall to place your image"
                                } else if (trackingPlanes > 0) {
                                    "Surface detected - keep scanning to find vertical walls"
                                } else {
                                    "Continue scanning to find more surfaces"
                                }

                                MessageUtils.showToast(
                                    this@MainActivity,
                                    message,
                                    Toast.LENGTH_LONG
                                )
                                lastToastTimeMs = currentTime
                            }
                        }
                    }
                    TrackingState.PAUSED -> {
                        // Limited tracking - yellow indicator
                        Log.d("TrackingIndicator", "Setting indicator to YELLOW (limited tracking)")
                        // Force color update with post() to ensure UI thread updates it
                        trackingQualityIndicator.post {
                            try {
                                trackingQualityIndicator.setBackgroundColor(resources.getColor(android.R.color.holo_orange_light, theme))
                                // Force redraw
                                trackingQualityIndicator.invalidate()
                                Log.d("TrackingIndicator", "YELLOW color set and invalidated for PAUSED state")
                            } catch (e: Exception) {
                                Log.e("TrackingIndicator", "Error setting YELLOW color for PAUSED state", e)
                            }
                        }
                        MessageUtils.showStatusMessage(trackingQualityText, getString(R.string.tracking_limited))

                        // Show detailed guidance for limited tracking
                        MessageUtils.showStatusMessage(trackingInitText, getString(R.string.tracking_help_limited))

                        // If we were initialized but lost tracking, restart the pulse animation
                        if (trackingInitialized) {
                            trackingInitialized = false
                            trackingProgress = trackingProgress.coerceAtMost(70) // Reduce progress

                            // Only start animation if it's not already running
                            if (!isAnimating) {
                                val pulseAnimation = AnimationUtils.loadAnimation(this@MainActivity, R.anim.pulse)
                                trackingQualityIndicator.startAnimation(pulseAnimation)
                                isAnimating = true
                            }
                        }

                        trackingProgressBar.progress = trackingProgress

                        // Show a toast with guidance if we haven't shown one recently
                        val currentTime = System.currentTimeMillis()
                        if (currentTime - lastToastTimeMs > TOAST_INTERVAL_MS) {
                            MessageUtils.showToast(
                                this@MainActivity,
                                "Continue moving to find more surfaces",
                                Toast.LENGTH_LONG
                            )
                            lastToastTimeMs = currentTime
                        }
                    }
                    TrackingState.STOPPED -> {
                        // Lost tracking - red indicator
                        Log.d("TrackingIndicator", "Setting indicator to RED (lost tracking)")
                        // Force color update with post() to ensure UI thread updates it
                        trackingQualityIndicator.post {
                            try {
                                trackingQualityIndicator.setBackgroundColor(resources.getColor(android.R.color.holo_red_light, theme))
                                // Force redraw
                                trackingQualityIndicator.invalidate()
                                Log.d("TrackingIndicator", "RED color set and invalidated for STOPPED state")
                            } catch (e: Exception) {
                                Log.e("TrackingIndicator", "Error setting RED color for STOPPED state", e)
                            }
                        }
                        MessageUtils.showStatusMessage(trackingQualityText, getString(R.string.tracking_lost))

                        // Show detailed guidance for lost tracking
                        MessageUtils.showStatusMessage(trackingInitText, getString(R.string.tracking_help_lost))

                        // Reset tracking progress
                        trackingInitialized = false
                        trackingProgress = 0
                        lastPlaneDetectionTimeMs = 0L
                        trackingProgressBar.progress = 0

                        // Make sure pulse animation is running, but only start it if not already animating
                        if (!isAnimating) {
                            val pulseAnimation = AnimationUtils.loadAnimation(this@MainActivity, R.anim.pulse)
                            trackingQualityIndicator.startAnimation(pulseAnimation)
                            isAnimating = true
                        }

                        // Show a toast with guidance if we haven't shown one recently
                        val currentTime = System.currentTimeMillis()
                        if (currentTime - lastToastTimeMs > TOAST_INTERVAL_MS) {
                            MessageUtils.showToast(
                                this@MainActivity,
                                "Move your device slowly to scan walls and floors",
                                Toast.LENGTH_LONG
                            )
                            lastToastTimeMs = currentTime

                            // Show tracking help dialog after a few seconds if tracking is still lost
                            if (trackingHelpShown < 1) {
                                Handler(Looper.getMainLooper()).postDelayed({
                                    if (camera.trackingState == TrackingState.STOPPED && !isFinishing && !isDestroyed) {
                                        showTrackingHelpDialog()
                                        trackingHelpShown++
                                    }
                                }, 5000) // 5 seconds
                            }
                        }
                    }
                }

                }  // Close the if block for tracking state changes

                // Force a layout update to ensure the changes are visible
                trackingIndicatorContainer.invalidate()

                // Auto-hide the detailed tracking info after 10 seconds of good tracking
                if (trackingInitialized && trackingState == TrackingState.TRACKING) {
                    Handler(Looper.getMainLooper()).postDelayed({
                        if (trackingInitialized && !isFinishing && !isDestroyed) {
                            // Keep the indicator dot and status text, hide the progress and init text
                            trackingInitText.visibility = View.GONE
                            trackingProgressBar.visibility = View.GONE
                        }
                    }, 10000) // 10 seconds
                } else {
                    // Make sure they're visible when not in stable tracking
                    trackingInitText.visibility = View.VISIBLE
                    trackingProgressBar.visibility = View.VISIBLE
                }

            } catch (e: Exception) {
                Log.e("TrackingIndicator", "Error updating tracking indicator", e)
            }
        }
    }

    /**
     * Logs detailed tracking information for debugging purposes
     * This helps diagnose issues with vertical plane detection and anchor placement
     */
    private fun logTrackingDetails(frame: Frame) {
        try {
            // Only log detailed information periodically to avoid log spam
            val currentTimeMs = System.currentTimeMillis()
            if (currentTimeMs - lastTrackingLogTimeMs < TRACKING_LOG_INTERVAL_MS) {
                return
            }
            lastTrackingLogTimeMs = currentTimeMs

            // Get camera pose
            val camera = frame.camera
            val cameraPose = camera.pose

            // Log camera position and orientation
            Log.d("TrackingDetails", "Camera position: (${cameraPose.tx()}, ${cameraPose.ty()}, ${cameraPose.tz()})")

            // Get all tracked planes
            val planes: Collection<Plane> = frame.getUpdatedTrackables(Plane::class.java)

            // Count planes by type
            var verticalPlaneCount = 0
            var horizontalPlaneCount = 0
            var otherPlaneCount = 0

            // Log information about each plane
            for (plane in planes) {
                if (plane.trackingState != TrackingState.TRACKING) continue

                when (plane.type) {
                    Plane.Type.VERTICAL -> {
                        verticalPlaneCount++
                        // Log detailed information about vertical planes
                        Log.d("TrackingDetails", "Vertical plane detected - " +
                                "Size: ${plane.extentX}m x ${plane.extentZ}m, " +
                                "Center: (${plane.centerPose.tx()}, ${plane.centerPose.ty()}, ${plane.centerPose.tz()})")
                    }
                    Plane.Type.HORIZONTAL_UPWARD_FACING -> {
                        horizontalPlaneCount++
                    }
                    Plane.Type.HORIZONTAL_DOWNWARD_FACING -> {
                        horizontalPlaneCount++
                    }
                    else -> {
                        otherPlaneCount++
                    }
                }
            }

            // Log summary of detected planes
            Log.d("TrackingDetails", "Detected planes - Vertical: $verticalPlaneCount, " +
                    "Horizontal: $horizontalPlaneCount, Other: $otherPlaneCount")

            // Log tracking failure reasons if not tracking
            if (camera.trackingState != TrackingState.TRACKING) {
                val trackingFailureReason = camera.trackingFailureReason
                Log.d("TrackingDetails", "Tracking failure reason: $trackingFailureReason")
            }
        } catch (e: Exception) {
            Log.e("TrackingDetails", "Error logging tracking details", e)
        }
    }

    // Constants and variables for tracking logging
    private val TRACKING_LOG_INTERVAL_MS = 2000L // Log every 2 seconds
    private var lastTrackingLogTimeMs = 0L

    // Constants for tracking guidance toasts
    private val TOAST_INTERVAL_MS = 8000L // Show toast every 8 seconds
    private var lastToastTimeMs = 0L
    private var trackingHelpShown = 0 // Counter for tracking help dialog

    /**
     * Shows a dialog with tracking help information
     */
    private fun showTrackingHelpDialog() {
        try {
            // Inflate the dialog layout
            val dialogView = layoutInflater.inflate(R.layout.dialog_tracking_help, null)

            // Create the dialog using androidx.appcompat.app.AlertDialog with the custom theme
            val dialog = androidx.appcompat.app.AlertDialog.Builder(this, R.style.Theme_DeaceAR_Dialog)
                .setView(dialogView)
                .setCancelable(true)
                .create()

            // Set up the close button
            dialogView.findViewById<Button>(R.id.btn_close_tracking_help).setOnClickListener {
                dialog.dismiss()
            }

            // Show the dialog
            dialog.show()

            // Auto-dismiss after 30 seconds
            Handler(Looper.getMainLooper()).postDelayed({
                if (dialog.isShowing && !isFinishing && !isDestroyed) {
                    dialog.dismiss()
                }
            }, 30000) // 30 seconds
        } catch (e: Exception) {
            Log.e("MainActivity", "Error showing tracking help dialog", e)
        }
    }
}