<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Lint Report</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
 <link rel="stylesheet" href="https://code.getmdl.io/1.2.1/material.blue-indigo.min.css" />
<link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Roboto:300,400,500,700" type="text/css">
<script defer src="https://code.getmdl.io/1.2.0/material.min.js"></script>
<style>
section.section--center {
    max-width: 860px;
}
.mdl-card__supporting-text + .mdl-card__actions {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
}
main > .mdl-layout__tab-panel {
  padding: 8px;
  padding-top: 48px;
}

.mdl-card__actions {
    margin: 0;
    padding: 4px 40px;
    color: inherit;
}
.mdl-card > * {
    height: auto;
}
.mdl-card__actions a {
    color: #00BCD4;
    margin: 0;
}
.error-icon {
    color: #bb7777;
    vertical-align: bottom;
}
.warning-icon {
    vertical-align: bottom;
}
.mdl-layout__content section:not(:last-of-type) {
  position: relative;
  margin-bottom: 48px;
}

.mdl-card .mdl-card__supporting-text {
  margin: 40px;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding: 0;
  color: inherit;
  width: calc(100% - 80px);
}
div.mdl-layout__drawer-button .material-icons {
    line-height: 48px;
}
.mdl-card .mdl-card__supporting-text {
    margin-top: 0px;
}
.chips {
    float: right;
    vertical-align: middle;
}

pre.errorlines {
    background-color: white;
    font-family: monospace;
    border: 1px solid #e0e0e0;
    line-height: 0.9rem;
    font-size: 0.9rem;    padding: 1px 0px 1px; 1px;
    overflow: scroll;
}
.prefix {
    color: #660e7a;
    font-weight: bold;
}
.attribute {
    color: #0000ff;
    font-weight: bold;
}
.value {
    color: #008000;
    font-weight: bold;
}
.tag {
    color: #000080;
    font-weight: bold;
}
.comment {
    color: #808080;
    font-style: italic;
}
.javadoc {
    color: #808080;
    font-style: italic;
}
.annotation {
    color: #808000;
}
.string {
    color: #008000;
    font-weight: bold;
}
.number {
    color: #0000ff;
}
.keyword {
    color: #000080;
    font-weight: bold;
}
.caretline {
    background-color: #fffae3;
}
.lineno {
    color: #999999;
    background-color: #f0f0f0;
}
.error {
    display: inline-block;
    position:relative;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AwCFR4T/3uLMgAAADxJREFUCNdNyLERQEAABMCjL4lQwIzcjErpguAL+C9AvgKJDbeD/PRpLdm35Hm+MU+cB+tCKaJW4L4YBy+CAiLJrFs9mgAAAABJRU5ErkJggg==) bottom repeat-x;
}
.warning {
    text-decoration: none;
    background-color: #f6ebbc;
}
.overview {
    padding: 10pt;
    width: 100%;
    overflow: auto;
    border-collapse:collapse;
}
.overview tr {
    border-bottom: solid 1px #eeeeee;
}
.categoryColumn a {
     text-decoration: none;
     color: inherit;
}
.countColumn {
    text-align: right;
    padding-right: 20px;
    width: 50px;
}
.issueColumn {
   padding-left: 16px;
}
.categoryColumn {
   position: relative;
   left: -50px;
   padding-top: 20px;
   padding-bottom: 5px;
}
.options {
   padding-left: 16px;
}
</style>
<script language="javascript" type="text/javascript">
<!--
function reveal(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'block';
document.getElementById(id+'Link').style.display = 'none';
}
}
function hideid(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'none';
}
}
//-->
</script>
</head>
<body class="mdl-color--grey-100 mdl-color-text--grey-700 mdl-base">
<div class="mdl-layout mdl-js-layout mdl-layout--fixed-header">
  <header class="mdl-layout__header">
    <div class="mdl-layout__header-row">
      <span class="mdl-layout-title">Lint Report: 192 warnings</span>
      <div class="mdl-layout-spacer"></div>
      <nav class="mdl-navigation mdl-layout--large-screen-only">Check performed at Sun May 25 22:35:48 CEST 2025 by AGP (8.9.2)</nav>
    </div>
  </header>
  <div class="mdl-layout__drawer">
    <span class="mdl-layout-title">Issue Types</span>
    <nav class="mdl-navigation">
      <a class="mdl-navigation__link" href="#overview"><i class="material-icons">dashboard</i>Overview</a>
      <a class="mdl-navigation__link" href="#CustomSplashScreen"><i class="material-icons warning-icon">warning</i>Application-defined Launch Screen (1)</a>
      <a class="mdl-navigation__link" href="#InflateParams"><i class="material-icons warning-icon">warning</i>Layout Inflation without a Parent (1)</a>
      <a class="mdl-navigation__link" href="#SelectedPhotoAccess"><i class="material-icons warning-icon">warning</i>Behavior change when requesting photo library access (1)</a>
      <a class="mdl-navigation__link" href="#VectorRaster"><i class="material-icons warning-icon">warning</i>Vector Image Generation (1)</a>
      <a class="mdl-navigation__link" href="#AndroidGradlePluginVersion"><i class="material-icons warning-icon">warning</i>Obsolete Android Gradle Plugin Version (3)</a>
      <a class="mdl-navigation__link" href="#GradleDependency"><i class="material-icons warning-icon">warning</i>Obsolete Gradle Dependency (25)</a>
      <a class="mdl-navigation__link" href="#ObsoleteSdkInt"><i class="material-icons warning-icon">warning</i>Obsolete SDK_INT Version Check (1)</a>
      <a class="mdl-navigation__link" href="#VectorPath"><i class="material-icons warning-icon">warning</i>Long vector paths (1)</a>
      <a class="mdl-navigation__link" href="#UnusedResources"><i class="material-icons warning-icon">warning</i>Unused resources (83)</a>
      <a class="mdl-navigation__link" href="#TypographyEllipsis"><i class="material-icons warning-icon">warning</i>Ellipsis string can be replaced with ellipsis character (3)</a>
      <a class="mdl-navigation__link" href="#IconXmlAndPng"><i class="material-icons warning-icon">warning</i>Icon is specified both as <code>.xml</code> file and as a bitmap (2)</a>
      <a class="mdl-navigation__link" href="#IconDuplicates"><i class="material-icons warning-icon">warning</i>Duplicated icons under different names (12)</a>
      <a class="mdl-navigation__link" href="#ButtonStyle"><i class="material-icons warning-icon">warning</i>Button should be borderless (2)</a>
      <a class="mdl-navigation__link" href="#UseKtx"><i class="material-icons warning-icon">warning</i>Use KTX extension function (13)</a>
      <a class="mdl-navigation__link" href="#UseTomlInstead"><i class="material-icons warning-icon">warning</i>Use TOML Version Catalog Instead (1)</a>
      <a class="mdl-navigation__link" href="#ClickableViewAccessibility"><i class="material-icons warning-icon">warning</i>Accessibility in Custom Views (2)</a>
      <a class="mdl-navigation__link" href="#SetTextI18n"><i class="material-icons warning-icon">warning</i>TextView Internationalization (6)</a>
      <a class="mdl-navigation__link" href="#HardcodedText"><i class="material-icons warning-icon">warning</i>Hardcoded text (21)</a>
      <a class="mdl-navigation__link" href="#RtlHardcoded"><i class="material-icons warning-icon">warning</i>Using left/right instead of start/end attributes (13)</a>
    </nav>
  </div>
  <main class="mdl-layout__content">
    <div class="mdl-layout__tab-panel is-active">
<a name="overview"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverviewCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overview</h2>
  </div>
              <div class="mdl-card__supporting-text">
<table class="overview">
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Correctness">Correctness</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#CustomSplashScreen">CustomSplashScreen</a>: Application-defined Launch Screen</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#InflateParams">InflateParams</a>: Layout Inflation without a Parent</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#SelectedPhotoAccess">SelectedPhotoAccess</a>: Behavior change when requesting photo library access</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#VectorRaster">VectorRaster</a>: Vector Image Generation</td></tr>
<tr>
<td class="countColumn">3</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#AndroidGradlePluginVersion">AndroidGradlePluginVersion</a>: Obsolete Android Gradle Plugin Version</td></tr>
<tr>
<td class="countColumn">25</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#GradleDependency">GradleDependency</a>: Obsolete Gradle Dependency</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Performance">Performance</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ObsoleteSdkInt">ObsoleteSdkInt</a>: Obsolete SDK_INT Version Check</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#VectorPath">VectorPath</a>: Long vector paths</td></tr>
<tr>
<td class="countColumn">83</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UnusedResources">UnusedResources</a>: Unused resources</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Usability:Typography">Usability:Typography</a>
</td></tr>
<tr>
<td class="countColumn">3</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#TypographyEllipsis">TypographyEllipsis</a>: Ellipsis string can be replaced with ellipsis character</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Usability:Icons">Usability:Icons</a>
</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#IconXmlAndPng">IconXmlAndPng</a>: Icon is specified both as <code>.xml</code> file and as a bitmap</td></tr>
<tr>
<td class="countColumn">12</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#IconDuplicates">IconDuplicates</a>: Duplicated icons under different names</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Usability">Usability</a>
</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ButtonStyle">ButtonStyle</a>: Button should be borderless</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Productivity">Productivity</a>
</td></tr>
<tr>
<td class="countColumn">13</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UseKtx">UseKtx</a>: Use KTX extension function</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UseTomlInstead">UseTomlInstead</a>: Use TOML Version Catalog Instead</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Accessibility">Accessibility</a>
</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ClickableViewAccessibility">ClickableViewAccessibility</a>: Accessibility in Custom Views</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Internationalization">Internationalization</a>
</td></tr>
<tr>
<td class="countColumn">6</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#SetTextI18n">SetTextI18n</a>: TextView Internationalization</td></tr>
<tr>
<td class="countColumn">21</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#HardcodedText">HardcodedText</a>: Hardcoded text</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Internationalization:Bidirectional Text">Internationalization:Bidirectional Text</a>
</td></tr>
<tr>
<td class="countColumn">13</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#RtlHardcoded">RtlHardcoded</a>: Using left/right instead of start/end attributes</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#ExtraIssues">Included Additional Checks (71)</a>
</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#MissingIssues">Disabled Checks (41)</a>
</td></tr>
</table>
<br/>              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverviewCardLink" onclick="hideid('OverviewCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Correctness"></a>
<a name="CustomSplashScreen"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="CustomSplashScreenCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Application-defined Launch Screen</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/deace/deacear/SplashActivity.kt">../../src/main/java/com/deace/deacear/SplashActivity.kt</a>:9</span>: <span class="message">The application should not provide its own launch screen</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>import android.os.Looper
<span class="lineno">  7 </span>import androidx.appcompat.app.AppCompatActivity
<span class="lineno">  8 </span>
<span class="caretline"><span class="lineno">  9 </span><span class="keyword">class</span> <span class="warning">SplashActivity</span> : AppCompatActivity() {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 10 </span>
<span class="lineno"> 11 </span>    companion <span class="keyword">object</span> {
<span class="lineno"> 12 </span>        private const <span class="keyword">val</span> SPLASH_DISPLAY_LENGTH = <span class="number">1000L</span> <span class="comment">// 1 second (reduced from 2 seconds for faster launch)</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationCustomSplashScreen" style="display: none;">
Starting in Android 12 (API 31+), the application's Launch Screen is provided by the system and the application should not create its own, otherwise the user will see two splashscreens. Please check the <code>SplashScreen</code> class to check how the Splash Screen can be controlled and customized.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/ui/splash-screen">https://developer.android.com/guide/topics/ui/splash-screen</a>
</div>To suppress this error, use the issue id "CustomSplashScreen" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">CustomSplashScreen</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationCustomSplashScreenLink" onclick="reveal('explanationCustomSplashScreen');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="CustomSplashScreenCardLink" onclick="hideid('CustomSplashScreenCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="InflateParams"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="InflateParamsCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Layout Inflation without a Parent</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/deace/deacear/utils/MessageUtils.kt">../../src/main/java/com/deace/deacear/utils/MessageUtils.kt</a>:27</span>: <span class="message">Avoid passing <code>null</code> as the view root (needed to resolve layout parameters on the inflated layout's root element)</span><br /><pre class="errorlines">
<span class="lineno"> 24 </span>        <span class="keyword">try</span> {
<span class="lineno"> 25 </span>            <span class="comment">// Create a custom toast layout</span>
<span class="lineno"> 26 </span>            <span class="keyword">val</span> inflater = LayoutInflater.from(context)
<span class="caretline"><span class="lineno"> 27 </span>            <span class="keyword">val</span> layout = inflater.inflate(R.layout.custom_toast, <span class="warning"><span class="keyword">null</span></span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 28 </span>
<span class="lineno"> 29 </span>            <span class="comment">// Get the text view from the layout</span>
<span class="lineno"> 30 </span>            <span class="keyword">val</span> textView = layout.findViewById&lt;TextView>(R.id.toast_text)
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationInflateParams" style="display: none;">
When inflating a layout, avoid passing in null as the parent view, since otherwise any layout parameters on the root of the inflated layout will be ignored.<br/><div class="moreinfo">More info: <a href="https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/">https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/</a>
</div>To suppress this error, use the issue id "InflateParams" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">InflateParams</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationInflateParamsLink" onclick="reveal('explanationInflateParams');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="InflateParamsCardLink" onclick="hideid('InflateParamsCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="SelectedPhotoAccess"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SelectedPhotoAccessCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Behavior change when requesting photo library access</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:11</span>: <span class="message">Your app is currently not handling Selected Photos Access introduced in Android 14+</span><br /><pre class="errorlines">
<span class="lineno">  8 </span>    <span class="comment">&lt;!-- Required for saving/loading images --></span>
<span class="lineno">  9 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_EXTERNAL_STORAGE"</span>
<span class="lineno"> 10 </span>        <span class="prefix">android:</span><span class="attribute">maxSdkVersion</span>=<span class="value">"32"</span> />
<span class="caretline"><span class="lineno"> 11 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="warning"><span class="value">android.permission.READ_MEDIA_IMAGES</span></span><span class="value">"</span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 12 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_EXTERNAL_STORAGE"</span>
<span class="lineno"> 13 </span>        <span class="prefix">android:</span><span class="attribute">maxSdkVersion</span>=<span class="value">"28"</span> />
<span class="lineno"> 14 </span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationSelectedPhotoAccess" style="display: none;">
Selected Photo Access is a new ability for users to share partial access to their photo library when apps request access to their device storage on Android 14+.<br/>
<br/>
Instead of letting the system manage the selection lifecycle, we recommend you adapt your app to handle partial access to the photo library.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/about/versions/14/changes/partial-photo-video-access">https://developer.android.com/about/versions/14/changes/partial-photo-video-access</a>
</div>To suppress this error, use the issue id "SelectedPhotoAccess" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">SelectedPhotoAccess</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationSelectedPhotoAccessLink" onclick="reveal('explanationSelectedPhotoAccess');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SelectedPhotoAccessCardLink" onclick="hideid('SelectedPhotoAccessCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="VectorRaster"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="VectorRasterCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Vector Image Generation</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/drawable/deace_logo_sticker.xml">../../src/main/res/drawable/deace_logo_sticker.xml</a>:3</span>: <span class="message">Limit vector icons sizes to 200×200 to keep icon drawing fast; see <a href="https://developer.android.com/studio/write/vector-asset-studio#when">https://developer.android.com/studio/write/vector-asset-studio#when</a> for more</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="lineno">  2 </span><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
<span class="caretline"><span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"</span><span class="warning"><span class="value">240dp</span></span><span class="value">"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"240dp"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"1063"</span>
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">viewportHeight</span>=<span class="value">"1063"</span>>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationVectorRaster" style="display: none;">
Vector icons require API 21 or API 24 depending on used features, but when <code>minSdkVersion</code> is less than 21 or 24 and Android Gradle plugin 1.4 or higher is used, a vector drawable placed in the <code>drawable</code> folder is automatically moved to <code>drawable-anydpi-v21</code> or <code>drawable-anydpi-v24</code> and bitmap images are generated for different screen resolutions for backwards compatibility.<br/>
<br/>
However, there are some limitations to this raster image generation, and this lint check flags elements and attributes that are not fully supported. You should manually check whether the generated output is acceptable for those older devices.<br/>To suppress this error, use the issue id "VectorRaster" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">VectorRaster</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationVectorRasterLink" onclick="reveal('explanationVectorRaster');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="VectorRasterCardLink" onclick="hideid('VectorRasterCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="AndroidGradlePluginVersion"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="AndroidGradlePluginVersionCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete Android Gradle Plugin Version</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:2</span>: <span class="message">A newer version of com.android.application than 8.9.2 is available: 8.10.0. (There is also a newer version of 8.9.&#55349;&#56421; available, if upgrading to 8.10.0 is difficult: 8.9.3)</span><br /><pre class="errorlines">
<span class="lineno">  1 </span>[versions]
<span class="caretline"><span class="lineno">  2 </span>agp = <span class="warning">"8.9.2"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>kotlin = "2.0.21"
<span class="lineno">  4 </span>coreKtx = "1.10.1"
<span class="lineno">  5 </span>junit = "4.13.2"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:2</span>: <span class="message">A newer version of com.android.application than 8.9.2 is available: 8.10.0. (There is also a newer version of 8.9.&#55349;&#56421; available, if upgrading to 8.10.0 is difficult: 8.9.3)</span><br /><pre class="errorlines">
<span class="lineno">  1 </span>[versions]
<span class="caretline"><span class="lineno">  2 </span>agp = <span class="warning">"8.9.2"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>kotlin = "2.0.21"
<span class="lineno">  4 </span>coreKtx = "1.10.1"
<span class="lineno">  5 </span>junit = "4.13.2"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:2</span>: <span class="message">A newer version of com.android.application than 8.9.2 is available: 8.10.0. (There is also a newer version of 8.9.&#55349;&#56421; available, if upgrading to 8.10.0 is difficult: 8.9.3)</span><br /><pre class="errorlines">
<span class="lineno">  1 </span>[versions]
<span class="caretline"><span class="lineno">  2 </span>agp = <span class="warning">"8.9.2"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>kotlin = "2.0.21"
<span class="lineno">  4 </span>coreKtx = "1.10.1"
<span class="lineno">  5 </span>junit = "4.13.2"
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationAndroidGradlePluginVersion" style="display: none;">
This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "AndroidGradlePluginVersion" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">AndroidGradlePluginVersion</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationAndroidGradlePluginVersionLink" onclick="reveal('explanationAndroidGradlePluginVersion');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="AndroidGradlePluginVersionCardLink" onclick="hideid('AndroidGradlePluginVersionCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="GradleDependency"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="GradleDependencyCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete Gradle Dependency</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:62</span>: <span class="message">A newer version of com.google.android.material:material than 1.11.0 is available: 1.12.0</span><br /><pre class="errorlines">
<span class="lineno"> 59 </span>    implementation(libs.generativeai)
<span class="lineno"> 60 </span>    implementation(libs.androidx.appcompat)
<span class="lineno"> 61 </span>    implementation(libs.core)
<span class="caretline"><span class="lineno"> 62 </span>    implementation(<span class="warning">"com.google.android.material:material:1.11.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 63 </span>    testImplementation(libs.junit)
<span class="lineno"> 64 </span>    androidTestImplementation(libs.androidx.junit)
<span class="lineno"> 65 </span>    androidTestImplementation(libs.androidx.espresso.core)
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:4</span>: <span class="message">A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0</span><br /><pre class="errorlines">
<span class="lineno">  1 </span>[versions]
<span class="lineno">  2 </span>agp = "8.9.2"
<span class="lineno">  3 </span>kotlin = "2.0.21"
<span class="caretline"><span class="lineno">  4 </span>coreKtx = <span class="warning">"1.10.1"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="lineno">  7 </span>espressoCore = "3.5.1"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:4</span>: <span class="message">A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0</span><br /><pre class="errorlines">
<span class="lineno">  1 </span>[versions]
<span class="lineno">  2 </span>agp = "8.9.2"
<span class="lineno">  3 </span>kotlin = "2.0.21"
<span class="caretline"><span class="lineno">  4 </span>coreKtx = <span class="warning">"1.10.1"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="lineno">  7 </span>espressoCore = "3.5.1"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:4</span>: <span class="message">A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0</span><br /><pre class="errorlines">
<span class="lineno">  1 </span>[versions]
<span class="lineno">  2 </span>agp = "8.9.2"
<span class="lineno">  3 </span>kotlin = "2.0.21"
<span class="caretline"><span class="lineno">  4 </span>coreKtx = <span class="warning">"1.10.1"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="lineno">  7 </span>espressoCore = "3.5.1"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:6</span>: <span class="message">A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1</span><br /><pre class="errorlines">
<span class="lineno">  3 </span>kotlin = "2.0.21"
<span class="lineno">  4 </span>coreKtx = "1.10.1"
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="caretline"><span class="lineno">  6 </span>junitVersion = <span class="warning">"1.1.5"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>lifecycleRuntimeKtx = "2.6.1"
<span class="lineno">  9 </span>lifecycleViewmodelCompose = "2.6.1"
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="GradleDependencyDivLink" onclick="reveal('GradleDependencyDiv');" />+ 20 More Occurrences...</button>
<div id="GradleDependencyDiv" style="display: none">
<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:6</span>: <span class="message">A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1</span><br /><pre class="errorlines">
<span class="lineno">  3 </span>kotlin = "2.0.21"
<span class="lineno">  4 </span>coreKtx = "1.10.1"
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="caretline"><span class="lineno">  6 </span>junitVersion = <span class="warning">"1.1.5"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>lifecycleRuntimeKtx = "2.6.1"
<span class="lineno">  9 </span>lifecycleViewmodelCompose = "2.6.1"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:6</span>: <span class="message">A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1</span><br /><pre class="errorlines">
<span class="lineno">  3 </span>kotlin = "2.0.21"
<span class="lineno">  4 </span>coreKtx = "1.10.1"
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="caretline"><span class="lineno">  6 </span>junitVersion = <span class="warning">"1.1.5"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>lifecycleRuntimeKtx = "2.6.1"
<span class="lineno">  9 </span>lifecycleViewmodelCompose = "2.6.1"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:7</span>: <span class="message">A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>coreKtx = "1.10.1"
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="caretline"><span class="lineno">  7 </span>espressoCore = <span class="warning">"3.5.1"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>lifecycleRuntimeKtx = "2.6.1"
<span class="lineno">  9 </span>lifecycleViewmodelCompose = "2.6.1"
<span class="lineno"> 10 </span>activityCompose = "1.8.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:7</span>: <span class="message">A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>coreKtx = "1.10.1"
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="caretline"><span class="lineno">  7 </span>espressoCore = <span class="warning">"3.5.1"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>lifecycleRuntimeKtx = "2.6.1"
<span class="lineno">  9 </span>lifecycleViewmodelCompose = "2.6.1"
<span class="lineno"> 10 </span>activityCompose = "1.8.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:7</span>: <span class="message">A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>coreKtx = "1.10.1"
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="caretline"><span class="lineno">  7 </span>espressoCore = <span class="warning">"3.5.1"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>lifecycleRuntimeKtx = "2.6.1"
<span class="lineno">  9 </span>lifecycleViewmodelCompose = "2.6.1"
<span class="lineno"> 10 </span>activityCompose = "1.8.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:8</span>: <span class="message">A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.9.0</span><br /><pre class="errorlines">
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="caretline"><span class="lineno">  8 </span>lifecycleRuntimeKtx = <span class="warning">"2.6.1"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  9 </span>lifecycleViewmodelCompose = "2.6.1"
<span class="lineno"> 10 </span>activityCompose = "1.8.0"
<span class="lineno"> 11 </span>composeBom = "2024.09.00"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:8</span>: <span class="message">A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.9.0</span><br /><pre class="errorlines">
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="caretline"><span class="lineno">  8 </span>lifecycleRuntimeKtx = <span class="warning">"2.6.1"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  9 </span>lifecycleViewmodelCompose = "2.6.1"
<span class="lineno"> 10 </span>activityCompose = "1.8.0"
<span class="lineno"> 11 </span>composeBom = "2024.09.00"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:8</span>: <span class="message">A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.9.0</span><br /><pre class="errorlines">
<span class="lineno">  5 </span>junit = "4.13.2"
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="caretline"><span class="lineno">  8 </span>lifecycleRuntimeKtx = <span class="warning">"2.6.1"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  9 </span>lifecycleViewmodelCompose = "2.6.1"
<span class="lineno"> 10 </span>activityCompose = "1.8.0"
<span class="lineno"> 11 </span>composeBom = "2024.09.00"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:9</span>: <span class="message">A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.6.1 is available: 2.9.0</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>lifecycleRuntimeKtx = "2.6.1"
<span class="caretline"><span class="lineno">  9 </span>lifecycleViewmodelCompose = <span class="warning">"2.6.1"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 10 </span>activityCompose = "1.8.0"
<span class="lineno"> 11 </span>composeBom = "2024.09.00"
<span class="lineno"> 12 </span>generativeai = "0.9.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:9</span>: <span class="message">A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.6.1 is available: 2.9.0</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>lifecycleRuntimeKtx = "2.6.1"
<span class="caretline"><span class="lineno">  9 </span>lifecycleViewmodelCompose = <span class="warning">"2.6.1"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 10 </span>activityCompose = "1.8.0"
<span class="lineno"> 11 </span>composeBom = "2024.09.00"
<span class="lineno"> 12 </span>generativeai = "0.9.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:9</span>: <span class="message">A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.6.1 is available: 2.9.0</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>junitVersion = "1.1.5"
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>lifecycleRuntimeKtx = "2.6.1"
<span class="caretline"><span class="lineno">  9 </span>lifecycleViewmodelCompose = <span class="warning">"2.6.1"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 10 </span>activityCompose = "1.8.0"
<span class="lineno"> 11 </span>composeBom = "2024.09.00"
<span class="lineno"> 12 </span>generativeai = "0.9.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:10</span>: <span class="message">A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1</span><br /><pre class="errorlines">
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>lifecycleRuntimeKtx = "2.6.1"
<span class="lineno">  9 </span>lifecycleViewmodelCompose = "2.6.1"
<span class="caretline"><span class="lineno"> 10 </span>activityCompose = <span class="warning">"1.8.0"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 11 </span>composeBom = "2024.09.00"
<span class="lineno"> 12 </span>generativeai = "0.9.0"
<span class="lineno"> 13 </span>googleAndroidLibrariesMapsplatformSecretsGradlePlugin = "2.0.1"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:10</span>: <span class="message">A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1</span><br /><pre class="errorlines">
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>lifecycleRuntimeKtx = "2.6.1"
<span class="lineno">  9 </span>lifecycleViewmodelCompose = "2.6.1"
<span class="caretline"><span class="lineno"> 10 </span>activityCompose = <span class="warning">"1.8.0"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 11 </span>composeBom = "2024.09.00"
<span class="lineno"> 12 </span>generativeai = "0.9.0"
<span class="lineno"> 13 </span>googleAndroidLibrariesMapsplatformSecretsGradlePlugin = "2.0.1"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:10</span>: <span class="message">A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1</span><br /><pre class="errorlines">
<span class="lineno">  7 </span>espressoCore = "3.5.1"
<span class="lineno">  8 </span>lifecycleRuntimeKtx = "2.6.1"
<span class="lineno">  9 </span>lifecycleViewmodelCompose = "2.6.1"
<span class="caretline"><span class="lineno"> 10 </span>activityCompose = <span class="warning">"1.8.0"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 11 </span>composeBom = "2024.09.00"
<span class="lineno"> 12 </span>generativeai = "0.9.0"
<span class="lineno"> 13 </span>googleAndroidLibrariesMapsplatformSecretsGradlePlugin = "2.0.1"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:11</span>: <span class="message">A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.05.01</span><br /><pre class="errorlines">
<span class="lineno">  8 </span>lifecycleRuntimeKtx = "2.6.1"
<span class="lineno">  9 </span>lifecycleViewmodelCompose = "2.6.1"
<span class="lineno"> 10 </span>activityCompose = "1.8.0"
<span class="caretline"><span class="lineno"> 11 </span>composeBom = <span class="warning">"2024.09.00"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 12 </span>generativeai = "0.9.0"
<span class="lineno"> 13 </span>googleAndroidLibrariesMapsplatformSecretsGradlePlugin = "2.0.1"
<span class="lineno"> 14 </span>appcompat = "1.7.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:11</span>: <span class="message">A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.05.01</span><br /><pre class="errorlines">
<span class="lineno">  8 </span>lifecycleRuntimeKtx = "2.6.1"
<span class="lineno">  9 </span>lifecycleViewmodelCompose = "2.6.1"
<span class="lineno"> 10 </span>activityCompose = "1.8.0"
<span class="caretline"><span class="lineno"> 11 </span>composeBom = <span class="warning">"2024.09.00"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 12 </span>generativeai = "0.9.0"
<span class="lineno"> 13 </span>googleAndroidLibrariesMapsplatformSecretsGradlePlugin = "2.0.1"
<span class="lineno"> 14 </span>appcompat = "1.7.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:11</span>: <span class="message">A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.05.01</span><br /><pre class="errorlines">
<span class="lineno">  8 </span>lifecycleRuntimeKtx = "2.6.1"
<span class="lineno">  9 </span>lifecycleViewmodelCompose = "2.6.1"
<span class="lineno"> 10 </span>activityCompose = "1.8.0"
<span class="caretline"><span class="lineno"> 11 </span>composeBom = <span class="warning">"2024.09.00"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 12 </span>generativeai = "0.9.0"
<span class="lineno"> 13 </span>googleAndroidLibrariesMapsplatformSecretsGradlePlugin = "2.0.1"
<span class="lineno"> 14 </span>appcompat = "1.7.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:15</span>: <span class="message">A newer version of com.google.ar:core than 1.48.0 is available: 1.49.0</span><br /><pre class="errorlines">
<span class="lineno"> 12 </span>generativeai = "0.9.0"
<span class="lineno"> 13 </span>googleAndroidLibrariesMapsplatformSecretsGradlePlugin = "2.0.1"
<span class="lineno"> 14 </span>appcompat = "1.7.0"
<span class="caretline"><span class="lineno"> 15 </span>core = <span class="warning">"1.48.0"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 16 </span>
<span class="lineno"> 17 </span>[libraries]
<span class="lineno"> 18 </span>androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:15</span>: <span class="message">A newer version of com.google.ar:core than 1.48.0 is available: 1.49.0</span><br /><pre class="errorlines">
<span class="lineno"> 12 </span>generativeai = "0.9.0"
<span class="lineno"> 13 </span>googleAndroidLibrariesMapsplatformSecretsGradlePlugin = "2.0.1"
<span class="lineno"> 14 </span>appcompat = "1.7.0"
<span class="caretline"><span class="lineno"> 15 </span>core = <span class="warning">"1.48.0"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 16 </span>
<span class="lineno"> 17 </span>[libraries]
<span class="lineno"> 18 </span>androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:15</span>: <span class="message">A newer version of com.google.ar:core than 1.48.0 is available: 1.49.0</span><br /><pre class="errorlines">
<span class="lineno"> 12 </span>generativeai = "0.9.0"
<span class="lineno"> 13 </span>googleAndroidLibrariesMapsplatformSecretsGradlePlugin = "2.0.1"
<span class="lineno"> 14 </span>appcompat = "1.7.0"
<span class="caretline"><span class="lineno"> 15 </span>core = <span class="warning">"1.48.0"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 16 </span>
<span class="lineno"> 17 </span>[libraries]
<span class="lineno"> 18 </span>androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationGradleDependency" style="display: none;">
This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "GradleDependency" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">GradleDependency</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationGradleDependencyLink" onclick="reveal('explanationGradleDependency');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="GradleDependencyCardLink" onclick="hideid('GradleDependencyCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Performance"></a>
<a name="ObsoleteSdkInt"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ObsoleteSdkIntCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete SDK_INT Version Check</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/deace/deacear/ui/ImagePanel.kt">../../src/main/java/com/deace/deacear/ui/ImagePanel.kt</a>:327</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 29</span><br /><pre class="errorlines">
<span class="lineno"> 324 </span>     intents.add(Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI))
<span class="lineno"> 325 </span>
<span class="lineno"> 326 </span>     <span class="comment">// Option 3: ACTION_OPEN_DOCUMENT (newer API)</span>
<span class="caretline"><span class="lineno"> 327 </span>     <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 328 </span>         intents.add(Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
<span class="lineno"> 329 </span>             type = <span class="string">"image/*"</span>
<span class="lineno"> 330 </span>             addCategory(Intent.CATEGORY_OPENABLE)
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationObsoleteSdkInt" style="display: none;">
This check flags version checks that are not necessary, because the <code>minSdkVersion</code> (or surrounding known API level) is already at least as high as the version checked for.<br/>
<br/>
Similarly, it also looks for resources in <code>-vNN</code> folders, such as <code>values-v14</code> where the version qualifier is less than or equal to the <code>minSdkVersion</code>, where the contents should be merged into the best folder.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ObsoleteSdkInt" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ObsoleteSdkInt</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationObsoleteSdkIntLink" onclick="reveal('explanationObsoleteSdkInt');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ObsoleteSdkIntCardLink" onclick="hideid('ObsoleteSdkIntCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="VectorPath"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="VectorPathCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Long vector paths</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/drawable/ic_settings.xml">../../src/main/res/drawable/ic_settings.xml</a>:9</span>: <span class="message">Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">tint</span>=<span class="value">"#FFFFFF"</span>>
<span class="lineno">  7 </span>  <span class="tag">&lt;path</span><span class="attribute">
</span><span class="lineno">  8 </span><span class="attribute">      </span><span class="prefix">android:</span><span class="attribute">fillColor</span>=<span class="value">"@android:color/white"</span>
<span class="caretline"><span class="lineno">  9 </span>      <span class="prefix">android:</span><span class="attribute">pathData</span>=<span class="value">"</span><span class="warning"><span class="value">M19.14,12.94c0.04,-0.3 0.06,-0.61 0.06,-0.94c0,-0.32 -0.02,-0.64 -0.07,-0.94l2.03,-1.58c0.18,-0.14 0.23,-0.41 0.12,-0.61l-1.92,-3.32c-0.12,-0.22 -0.37,-0.29 -0.59,-0.22l-2.39,0.96c-0.5,-0.38 -1.03,-0.7 -1.62,-0.94L14.4,2.81c-0.04,-0.24 -0.24,-0.41 -0.48,-0.41h-3.84c-0.24,0 -0.43,0.17 -0.47,0.41L9.25,5.35C8.66,5.59 8.12,5.92 7.63,6.29L5.24,5.33c-0.22,-0.08 -0.47,0 -0.59,0.22L2.74,8.87C2.62,9.08 2.66,9.34 2.86,9.48l2.03,1.58C4.84,11.36 4.8,11.69 4.8,12s0.02,0.64 0.07,0.94l-2.03,1.58c-0.18,0.14 -0.23,0.41 -0.12,0.61l1.92,3.32c0.12,0.22 0.37,0.29 0.59,0.22l2.39,-0.96c0.5,0.38 1.03,0.7 1.62,0.94l0.36,2.54c0.05,0.24 0.24,0.41 0.48,0.41h3.84c0.24,0 0.44,-0.17 0.47,-0.41l0.36,-2.54c0.59,-0.24 1.13,-0.56 1.62,-0.94l2.39,0.96c0.22,0.08 0.47,0 0.59,-0.22l1.92,-3.32c0.12,-0.22 0.07,-0.47 -0.12,-0.61L19.14,12.94zM12,15.6c-1.98,0 -3.6,-1.62 -3.6,-3.6s1.62,-3.6 3.6,-3.6s3.6,1.62 3.6,3.6S13.98,15.6 12,15.6z</span></span><span class="value">"</span>/></span>
<span class="lineno"> 10 </span><span class="tag">&lt;/vector></span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationVectorPath" style="display: none;">
Using long vector paths is bad for performance. There are several ways to make the <code>pathData</code> shorter:<br/>
* Using less precision<br/>
* Removing some minor details<br/>
* Using the Android Studio vector conversion tool<br/>
* Rasterizing the image (converting to PNG)<br/>To suppress this error, use the issue id "VectorPath" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">VectorPath</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationVectorPathLink" onclick="reveal('explanationVectorPath');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="VectorPathCardLink" onclick="hideid('VectorPathCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UnusedResources"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnusedResourcesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Unused resources</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/values/arrays.xml">../../src/main/res/values/arrays.xml</a>:3</span>: <span class="message">The resource <code>R.array.grid_formats</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="lineno">  2 </span><span class="tag">&lt;resources></span>
<span class="caretline"><span class="lineno">  3 </span>    <span class="tag">&lt;string-array</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"grid_formats"</span></span>>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  4 </span>        <span class="tag">&lt;item></span>Vertical (9:16)<span class="tag">&lt;/item></span>
<span class="lineno">  5 </span>        <span class="tag">&lt;item></span>Square (1:1)<span class="tag">&lt;/item></span>
<span class="lineno">  6 </span>        <span class="tag">&lt;item></span>Horizontal (16:9)<span class="tag">&lt;/item></span>
</pre>

<span class="location"><a href="../../src/main/res/values/arrays.xml">../../src/main/res/values/arrays.xml</a>:9</span>: <span class="message">The resource <code>R.array.line_thickness_options</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>        <span class="tag">&lt;item></span>Horizontal (16:9)<span class="tag">&lt;/item></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;/string-array></span>
<span class="lineno">  8 </span>
<span class="caretline"><span class="lineno">  9 </span>    <span class="tag">&lt;string-array</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"line_thickness_options"</span></span>>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 10 </span>        <span class="tag">&lt;item></span>Fine<span class="tag">&lt;/item></span>
<span class="lineno"> 11 </span>        <span class="tag">&lt;item></span>Medium<span class="tag">&lt;/item></span>
<span class="lineno"> 12 </span>        <span class="tag">&lt;item></span>Thick<span class="tag">&lt;/item></span>
</pre>

<span class="location"><a href="../../src/main/res/drawable/baseline_grid_on_24.xml">../../src/main/res/drawable/baseline_grid_on_24.xml</a>:1</span>: <span class="message">The resource <code>R.drawable.baseline_grid_on_24</code> appears to be unused</span><br /><pre class="errorlines">
<span class="caretline"><span class="lineno">  1 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  2 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"24"</span></pre>

<span class="location"><a href="../../src/main/res/drawable-hdpi/baseline_grid_on_black_18.png">../../src/main/res/drawable-hdpi/baseline_grid_on_black_18.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable-hdpi/baseline_grid_on_black_18.png" /><span class="message">The resource <code>R.drawable.baseline_grid_on_black_18</code> appears to be unused</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable-hdpi/baseline_grid_on_black_20.png">../../src/main/res/drawable-hdpi/baseline_grid_on_black_20.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable-hdpi/baseline_grid_on_black_20.png" /><span class="message">The resource <code>R.drawable.baseline_grid_on_black_20</code> appears to be unused</span><br clear="right"/>
<button class="mdl-button mdl-js-button mdl-button--primary" id="UnusedResourcesDivLink" onclick="reveal('UnusedResourcesDiv');" />+ 78 More Occurrences...</button>
<div id="UnusedResourcesDiv" style="display: none">
<span class="location"><a href="../../src/main/res/drawable-hdpi/baseline_grid_on_black_24.png">../../src/main/res/drawable-hdpi/baseline_grid_on_black_24.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable-hdpi/baseline_grid_on_black_24.png" /><span class="message">The resource <code>R.drawable.baseline_grid_on_black_24</code> appears to be unused</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable-hdpi/baseline_grid_on_black_36.png">../../src/main/res/drawable-hdpi/baseline_grid_on_black_36.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable-hdpi/baseline_grid_on_black_36.png" /><span class="message">The resource <code>R.drawable.baseline_grid_on_black_36</code> appears to be unused</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable-hdpi/baseline_grid_on_black_48.png">../../src/main/res/drawable-hdpi/baseline_grid_on_black_48.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable-hdpi/baseline_grid_on_black_48.png" /><span class="message">The resource <code>R.drawable.baseline_grid_on_black_48</code> appears to be unused</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/baseline_image_20.xml">../../src/main/res/drawable/baseline_image_20.xml</a>:1</span>: <span class="message">The resource <code>R.drawable.baseline_image_20</code> appears to be unused</span><br /><pre class="errorlines">
<span class="caretline"><span class="lineno">  1 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  2 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"20dp"</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"20dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"20"</span></pre>

<span class="location"><a href="../../src/main/res/drawable/baseline_image_24.xml">../../src/main/res/drawable/baseline_image_24.xml</a>:1</span>: <span class="message">The resource <code>R.drawable.baseline_image_24</code> appears to be unused</span><br /><pre class="errorlines">
<span class="caretline"><span class="lineno">  1 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  2 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"24"</span></pre>

<span class="location"><a href="../../src/main/res/drawable-hdpi/baseline_image_black_18.png">../../src/main/res/drawable-hdpi/baseline_image_black_18.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable-hdpi/baseline_image_black_18.png" /><span class="message">The resource <code>R.drawable.baseline_image_black_18</code> appears to be unused</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable-hdpi/baseline_image_black_20.png">../../src/main/res/drawable-hdpi/baseline_image_black_20.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable-hdpi/baseline_image_black_20.png" /><span class="message">The resource <code>R.drawable.baseline_image_black_20</code> appears to be unused</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable-hdpi/baseline_image_black_24.png">../../src/main/res/drawable-hdpi/baseline_image_black_24.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable-hdpi/baseline_image_black_24.png" /><span class="message">The resource <code>R.drawable.baseline_image_black_24</code> appears to be unused</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable-hdpi/baseline_image_black_36.png">../../src/main/res/drawable-hdpi/baseline_image_black_36.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable-hdpi/baseline_image_black_36.png" /><span class="message">The resource <code>R.drawable.baseline_image_black_36</code> appears to be unused</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable-hdpi/baseline_image_black_48.png">../../src/main/res/drawable-hdpi/baseline_image_black_48.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable-hdpi/baseline_image_black_48.png" /><span class="message">The resource <code>R.drawable.baseline_image_black_48</code> appears to be unused</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:4</span>: <span class="message">The resource <code>R.color.colorPrimary</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="lineno">  2 </span><span class="tag">&lt;resources></span>
<span class="lineno">  3 </span>    <span class="comment">&lt;!-- Material Design Color Palette --></span>
<span class="caretline"><span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"colorPrimary"</span></span>>#6200EE<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Purple 500 --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"colorPrimaryDark"</span>>#3700B3<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Purple 700 --></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"colorAccent"</span>>#03DAC6<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Teal 200 --></span>
<span class="lineno">  7 </span>    
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:5</span>: <span class="message">The resource <code>R.color.colorPrimaryDark</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  2 </span><span class="tag">&lt;resources></span>
<span class="lineno">  3 </span>    <span class="comment">&lt;!-- Material Design Color Palette --></span>
<span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"colorPrimary"</span>>#6200EE<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Purple 500 --></span>
<span class="caretline"><span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"colorPrimaryDark"</span></span>>#3700B3<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Purple 700 --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"colorAccent"</span>>#03DAC6<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Teal 200 --></span>
<span class="lineno">  7 </span>    
<span class="lineno">  8 </span>    <span class="comment">&lt;!-- Status/Navigation Bar Colors --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:6</span>: <span class="message">The resource <code>R.color.colorAccent</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  3 </span>    <span class="comment">&lt;!-- Material Design Color Palette --></span>
<span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"colorPrimary"</span>>#6200EE<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Purple 500 --></span>
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"colorPrimaryDark"</span>>#3700B3<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Purple 700 --></span>
<span class="caretline"><span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"colorAccent"</span></span>>#03DAC6<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Teal 200 --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  7 </span>    
<span class="lineno">  8 </span>    <span class="comment">&lt;!-- Status/Navigation Bar Colors --></span>
<span class="lineno">  9 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"statusBarBackground"</span>>@color/colorPrimaryDark<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:9</span>: <span class="message">The resource <code>R.color.statusBarBackground</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"colorAccent"</span>>#03DAC6<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Teal 200 --></span>
<span class="lineno">  7 </span>    
<span class="lineno">  8 </span>    <span class="comment">&lt;!-- Status/Navigation Bar Colors --></span>
<span class="caretline"><span class="lineno">  9 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"statusBarBackground"</span></span>>@color/colorPrimaryDark<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 10 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"navigationBarBackground"</span>>@color/black<span class="tag">&lt;/color></span>
<span class="lineno"> 11 </span>    
<span class="lineno"> 12 </span>    <span class="comment">&lt;!-- Background Colors --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:10</span>: <span class="message">The resource <code>R.color.navigationBarBackground</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  7 </span>    
<span class="lineno">  8 </span>    <span class="comment">&lt;!-- Status/Navigation Bar Colors --></span>
<span class="lineno">  9 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"statusBarBackground"</span>>@color/colorPrimaryDark<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 10 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"navigationBarBackground"</span></span>>@color/black<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 11 </span>    
<span class="lineno"> 12 </span>    <span class="comment">&lt;!-- Background Colors --></span>
<span class="lineno"> 13 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"background"</span>>#121212<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Dark background for AR view --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:13</span>: <span class="message">The resource <code>R.color.background</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 10 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"navigationBarBackground"</span>>@color/black<span class="tag">&lt;/color></span>
<span class="lineno"> 11 </span>    
<span class="lineno"> 12 </span>    <span class="comment">&lt;!-- Background Colors --></span>
<span class="caretline"><span class="lineno"> 13 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"background"</span></span>>#121212<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Dark background for AR view --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"surface"</span>>#1E1E1E<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Cards/dialogs --></span>
<span class="lineno"> 15 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"error"</span>>#CF6679<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Error color --></span>
<span class="lineno"> 16 </span>    
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:14</span>: <span class="message">The resource <code>R.color.surface</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 11 </span>    
<span class="lineno"> 12 </span>    <span class="comment">&lt;!-- Background Colors --></span>
<span class="lineno"> 13 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"background"</span>>#121212<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Dark background for AR view --></span>
<span class="caretline"><span class="lineno"> 14 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"surface"</span></span>>#1E1E1E<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Cards/dialogs --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 15 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"error"</span>>#CF6679<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Error color --></span>
<span class="lineno"> 16 </span>    
<span class="lineno"> 17 </span>    <span class="comment">&lt;!-- Text Colors --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:15</span>: <span class="message">The resource <code>R.color.error</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 12 </span>    <span class="comment">&lt;!-- Background Colors --></span>
<span class="lineno"> 13 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"background"</span>>#121212<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Dark background for AR view --></span>
<span class="lineno"> 14 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"surface"</span>>#1E1E1E<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Cards/dialogs --></span>
<span class="caretline"><span class="lineno"> 15 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"error"</span></span>>#CF6679<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Error color --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 16 </span>    
<span class="lineno"> 17 </span>    <span class="comment">&lt;!-- Text Colors --></span>
<span class="lineno"> 18 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"textPrimary"</span>>#FFFFFF<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:18</span>: <span class="message">The resource <code>R.color.textPrimary</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 15 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"error"</span>>#CF6679<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Error color --></span>
<span class="lineno"> 16 </span>    
<span class="lineno"> 17 </span>    <span class="comment">&lt;!-- Text Colors --></span>
<span class="caretline"><span class="lineno"> 18 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"textPrimary"</span></span>>#FFFFFF<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 19 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"textSecondary"</span>>#B3B3B3<span class="tag">&lt;/color></span>
<span class="lineno"> 20 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"textHint"</span>>#666666<span class="tag">&lt;/color></span>
<span class="lineno"> 21 </span>    
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:19</span>: <span class="message">The resource <code>R.color.textSecondary</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 16 </span>    
<span class="lineno"> 17 </span>    <span class="comment">&lt;!-- Text Colors --></span>
<span class="lineno"> 18 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"textPrimary"</span>>#FFFFFF<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 19 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"textSecondary"</span></span>>#B3B3B3<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 20 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"textHint"</span>>#666666<span class="tag">&lt;/color></span>
<span class="lineno"> 21 </span>    
<span class="lineno"> 22 </span>    <span class="comment">&lt;!-- UI Element Colors --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:20</span>: <span class="message">The resource <code>R.color.textHint</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 17 </span>    <span class="comment">&lt;!-- Text Colors --></span>
<span class="lineno"> 18 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"textPrimary"</span>>#FFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 19 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"textSecondary"</span>>#B3B3B3<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 20 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"textHint"</span></span>>#666666<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 21 </span>    
<span class="lineno"> 22 </span>    <span class="comment">&lt;!-- UI Element Colors --></span>
<span class="lineno"> 23 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"buttonNormal"</span>>@color/colorPrimary<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:23</span>: <span class="message">The resource <code>R.color.buttonNormal</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 20 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"textHint"</span>>#666666<span class="tag">&lt;/color></span>
<span class="lineno"> 21 </span>    
<span class="lineno"> 22 </span>    <span class="comment">&lt;!-- UI Element Colors --></span>
<span class="caretline"><span class="lineno"> 23 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"buttonNormal"</span></span>>@color/colorPrimary<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 24 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"buttonPressed"</span>>#7F3FD4<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Lighter purple --></span>
<span class="lineno"> 25 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"buttonDisabled"</span>>#424242<span class="tag">&lt;/color></span>
<span class="lineno"> 26 </span>    
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:24</span>: <span class="message">The resource <code>R.color.buttonPressed</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 21 </span>    
<span class="lineno"> 22 </span>    <span class="comment">&lt;!-- UI Element Colors --></span>
<span class="lineno"> 23 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"buttonNormal"</span>>@color/colorPrimary<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 24 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"buttonPressed"</span></span>>#7F3FD4<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Lighter purple --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 25 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"buttonDisabled"</span>>#424242<span class="tag">&lt;/color></span>
<span class="lineno"> 26 </span>    
<span class="lineno"> 27 </span>    <span class="comment">&lt;!-- AR Specific Colors --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:25</span>: <span class="message">The resource <code>R.color.buttonDisabled</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 22 </span>    <span class="comment">&lt;!-- UI Element Colors --></span>
<span class="lineno"> 23 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"buttonNormal"</span>>@color/colorPrimary<span class="tag">&lt;/color></span>
<span class="lineno"> 24 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"buttonPressed"</span>>#7F3FD4<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Lighter purple --></span>
<span class="caretline"><span class="lineno"> 25 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"buttonDisabled"</span></span>>#424242<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 26 </span>    
<span class="lineno"> 27 </span>    <span class="comment">&lt;!-- AR Specific Colors --></span>
<span class="lineno"> 28 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"ar_grid_color"</span>>#4DFFFFFF<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- White grid with 30% opacity --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:28</span>: <span class="message">The resource <code>R.color.ar_grid_color</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 25 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"buttonDisabled"</span>>#424242<span class="tag">&lt;/color></span>
<span class="lineno"> 26 </span>    
<span class="lineno"> 27 </span>    <span class="comment">&lt;!-- AR Specific Colors --></span>
<span class="caretline"><span class="lineno"> 28 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"ar_grid_color"</span></span>>#4DFFFFFF<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- White grid with 30% opacity --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 29 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"ar_plane_color"</span>>#4D00E5FF<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Cyan plane with 30% opacity --></span>
<span class="lineno"> 30 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"ar_reticle_color"</span>>#FF4081<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Pink reticle --></span>
<span class="lineno"> 31 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"ar_anchor_color"</span>>#FFD600<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Yellow anchor --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:29</span>: <span class="message">The resource <code>R.color.ar_plane_color</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 26 </span>    
<span class="lineno"> 27 </span>    <span class="comment">&lt;!-- AR Specific Colors --></span>
<span class="lineno"> 28 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"ar_grid_color"</span>>#4DFFFFFF<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- White grid with 30% opacity --></span>
<span class="caretline"><span class="lineno"> 29 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"ar_plane_color"</span></span>>#4D00E5FF<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Cyan plane with 30% opacity --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 30 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"ar_reticle_color"</span>>#FF4081<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Pink reticle --></span>
<span class="lineno"> 31 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"ar_anchor_color"</span>>#FFD600<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Yellow anchor --></span>
<span class="lineno"> 32 </span>    
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:30</span>: <span class="message">The resource <code>R.color.ar_reticle_color</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 27 </span>    <span class="comment">&lt;!-- AR Specific Colors --></span>
<span class="lineno"> 28 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"ar_grid_color"</span>>#4DFFFFFF<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- White grid with 30% opacity --></span>
<span class="lineno"> 29 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"ar_plane_color"</span>>#4D00E5FF<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Cyan plane with 30% opacity --></span>
<span class="caretline"><span class="lineno"> 30 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"ar_reticle_color"</span></span>>#FF4081<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Pink reticle --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 31 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"ar_anchor_color"</span>>#FFD600<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Yellow anchor --></span>
<span class="lineno"> 32 </span>    
<span class="lineno"> 33 </span>    <span class="comment">&lt;!-- Standard Colors --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:31</span>: <span class="message">The resource <code>R.color.ar_anchor_color</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 28 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"ar_grid_color"</span>>#4DFFFFFF<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- White grid with 30% opacity --></span>
<span class="lineno"> 29 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"ar_plane_color"</span>>#4D00E5FF<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Cyan plane with 30% opacity --></span>
<span class="lineno"> 30 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"ar_reticle_color"</span>>#FF4081<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Pink reticle --></span>
<span class="caretline"><span class="lineno"> 31 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"ar_anchor_color"</span></span>>#FFD600<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Yellow anchor --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 32 </span>    
<span class="lineno"> 33 </span>    <span class="comment">&lt;!-- Standard Colors --></span>
<span class="lineno"> 34 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"white"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:36</span>: <span class="message">The resource <code>R.color.transparent</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 33 </span>    <span class="comment">&lt;!-- Standard Colors --></span>
<span class="lineno"> 34 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"white"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 35 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"black"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 36 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"transparent"</span></span>>#00000000<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 37 </span>    
<span class="lineno"> 38 </span>    <span class="comment">&lt;!-- Notification Colors --></span>
<span class="lineno"> 39 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"notification_success"</span>>#4CAF50<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Green --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:39</span>: <span class="message">The resource <code>R.color.notification_success</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 36 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"transparent"</span>>#00000000<span class="tag">&lt;/color></span>
<span class="lineno"> 37 </span>    
<span class="lineno"> 38 </span>    <span class="comment">&lt;!-- Notification Colors --></span>
<span class="caretline"><span class="lineno"> 39 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"notification_success"</span></span>>#4CAF50<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Green --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 40 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"notification_warning"</span>>#FFC107<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Amber --></span>
<span class="lineno"> 41 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"notification_error"</span>>#F44336<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Red --></span>
<span class="lineno"> 42 </span>    
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:40</span>: <span class="message">The resource <code>R.color.notification_warning</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 37 </span>    
<span class="lineno"> 38 </span>    <span class="comment">&lt;!-- Notification Colors --></span>
<span class="lineno"> 39 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"notification_success"</span>>#4CAF50<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Green --></span>
<span class="caretline"><span class="lineno"> 40 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"notification_warning"</span></span>>#FFC107<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Amber --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 41 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"notification_error"</span>>#F44336<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Red --></span>
<span class="lineno"> 42 </span>    
<span class="lineno"> 43 </span>    <span class="comment">&lt;!-- Gradient Colors (for possible future use) --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:41</span>: <span class="message">The resource <code>R.color.notification_error</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 38 </span>    <span class="comment">&lt;!-- Notification Colors --></span>
<span class="lineno"> 39 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"notification_success"</span>>#4CAF50<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Green --></span>
<span class="lineno"> 40 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"notification_warning"</span>>#FFC107<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Amber --></span>
<span class="caretline"><span class="lineno"> 41 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"notification_error"</span></span>>#F44336<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Red --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 42 </span>    
<span class="lineno"> 43 </span>    <span class="comment">&lt;!-- Gradient Colors (for possible future use) --></span>
<span class="lineno"> 44 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gradient_start"</span>>@color/colorPrimary<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:44</span>: <span class="message">The resource <code>R.color.gradient_start</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 41 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"notification_error"</span>>#F44336<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Red --></span>
<span class="lineno"> 42 </span>    
<span class="lineno"> 43 </span>    <span class="comment">&lt;!-- Gradient Colors (for possible future use) --></span>
<span class="caretline"><span class="lineno"> 44 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"gradient_start"</span></span>>@color/colorPrimary<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 45 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gradient_end"</span>>@color/colorAccent<span class="tag">&lt;/color></span>
<span class="lineno"> 46 </span>    
<span class="lineno"> 47 </span>    <span class="comment">&lt;!-- Panel Backgrounds --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:45</span>: <span class="message">The resource <code>R.color.gradient_end</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 42 </span>    
<span class="lineno"> 43 </span>    <span class="comment">&lt;!-- Gradient Colors (for possible future use) --></span>
<span class="lineno"> 44 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gradient_start"</span>>@color/colorPrimary<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 45 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"gradient_end"</span></span>>@color/colorAccent<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 46 </span>    
<span class="lineno"> 47 </span>    <span class="comment">&lt;!-- Panel Backgrounds --></span>
<span class="lineno"> 48 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"panel_background"</span>>#CC1E1E1E<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Semi-transparent dark --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:48</span>: <span class="message">The resource <code>R.color.panel_background</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 45 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gradient_end"</span>>@color/colorAccent<span class="tag">&lt;/color></span>
<span class="lineno"> 46 </span>    
<span class="lineno"> 47 </span>    <span class="comment">&lt;!-- Panel Backgrounds --></span>
<span class="caretline"><span class="lineno"> 48 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"panel_background"</span></span>>#CC1E1E1E<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Semi-transparent dark --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 49 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"panel_divider"</span>>#33FFFFFF<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Very light divider --></span>
<span class="lineno"> 50 </span>    
<span class="lineno"> 51 </span>    <span class="comment">&lt;!-- Selection States --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:49</span>: <span class="message">The resource <code>R.color.panel_divider</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 46 </span>    
<span class="lineno"> 47 </span>    <span class="comment">&lt;!-- Panel Backgrounds --></span>
<span class="lineno"> 48 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"panel_background"</span>>#CC1E1E1E<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Semi-transparent dark --></span>
<span class="caretline"><span class="lineno"> 49 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"panel_divider"</span></span>>#33FFFFFF<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Very light divider --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 50 </span>    
<span class="lineno"> 51 </span>    <span class="comment">&lt;!-- Selection States --></span>
<span class="lineno"> 52 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"item_selected"</span>>#33FFFFFF<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- White with 20% opacity --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:52</span>: <span class="message">The resource <code>R.color.item_selected</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 49 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"panel_divider"</span>>#33FFFFFF<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- Very light divider --></span>
<span class="lineno"> 50 </span>    
<span class="lineno"> 51 </span>    <span class="comment">&lt;!-- Selection States --></span>
<span class="caretline"><span class="lineno"> 52 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"item_selected"</span></span>>#33FFFFFF<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- White with 20% opacity --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 53 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"item_pressed"</span>>#1AFFFFFF<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- White with 10% opacity --></span>
<span class="lineno"> 54 </span><span class="tag">&lt;/resources></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:53</span>: <span class="message">The resource <code>R.color.item_pressed</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 50 </span>    
<span class="lineno"> 51 </span>    <span class="comment">&lt;!-- Selection States --></span>
<span class="lineno"> 52 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"item_selected"</span>>#33FFFFFF<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- White with 20% opacity --></span>
<span class="caretline"><span class="lineno"> 53 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"item_pressed"</span></span>>#1AFFFFFF<span class="tag">&lt;/color></span>  <span class="comment">&lt;!-- White with 10% opacity --></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 54 </span><span class="tag">&lt;/resources></span></pre>

<span class="location"><a href="../../src/main/res/layout/config_panel.xml">../../src/main/res/layout/config_panel.xml</a>:2</span>: <span class="message">The resource <code>R.layout.config_panel</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">   1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">   2 </span><span class="warning"><span class="tag">&lt;ScrollView</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   3 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">   4 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>>
<span class="lineno">   5 </span>
</pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:6</span>: <span class="message">The resource <code>R.dimen.main_menu_minimized_width</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 3 </span>    <span class="comment">&lt;!-- Other dimensions might be here --></span>
<span class="lineno"> 4 </span>
<span class="lineno"> 5 </span>    <span class="comment">&lt;!-- Define the width for the minimized main menu --></span>
<span class="caretline"><span class="lineno"> 6 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"main_menu_minimized_width"</span></span>>32dp<span class="tag">&lt;/dimen></span> <span class="comment">&lt;!-- Adjusted to match new button size --></span>
</span>
<span class="lineno"> 7 </span>
<span class="lineno"> 8 </span><span class="tag">&lt;/resources></span></pre>

<span class="location"><a href="../../src/main/res/layout/export_panel.xml">../../src/main/res/layout/export_panel.xml</a>:2</span>: <span class="message">The resource <code>R.layout.export_panel</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">  2 </span><span class="warning"><span class="tag">&lt;LinearLayout</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/grid_panel.xml">../../src/main/res/layout/grid_panel.xml</a>:2</span>: <span class="message">The resource <code>R.layout.grid_panel</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">   1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">   2 </span><span class="warning"><span class="tag">&lt;LinearLayout</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   3 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">   4 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno">   5 </span>    <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>
</pre>

<span class="location"><a href="../../src/main/res/drawable/ic_maximize.xml">../../src/main/res/drawable/ic_maximize.xml</a>:2</span>: <span class="message">The resource <code>R.drawable.ic_maximize</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">  2 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"24"</span></pre>

<span class="location"><a href="../../src/main/res/drawable/ic_minimize.xml">../../src/main/res/drawable/ic_minimize.xml</a>:1</span>: <span class="message">The resource <code>R.drawable.ic_minimize</code> appears to be unused</span><br /><pre class="errorlines">
<span class="caretline"><span class="lineno">  1 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  2 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"24"</span></pre>

<span class="location"><a href="../../src/main/res/layout/image_panel.xml">../../src/main/res/layout/image_panel.xml</a>:2</span>: <span class="message">The resource <code>R.layout.image_panel</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">   1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">   2 </span><span class="warning"><span class="tag">&lt;LinearLayout</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   3 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">   4 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno">   5 </span>    <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>
</pre>

<br/><b>NOTE: 33 results omitted.</b><br/><br/></div>
</div>
<div class="metadata"><div class="explanation" id="explanationUnusedResources" style="display: none;">
Unused resources make applications larger and slow down builds.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
,<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UnusedResources" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnusedResources</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnusedResourcesLink" onclick="reveal('explanationUnusedResources');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnusedResourcesCardLink" onclick="hideid('UnusedResourcesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Usability:Typography"></a>
<a name="TypographyEllipsis"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="TypographyEllipsisCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Ellipsis string can be replaced with ellipsis character</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:7</span>: <span class="message">Replace "..." with ellipsis character (&#8230;, &amp;#8230;) ?</span><br /><pre class="errorlines">
<span class="lineno">   4 </span>  <span class="comment">&lt;!-- Common strings --></span>
<span class="lineno">   5 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"scanning_environment"</span> <span class="prefix">tools:</span><span class="attribute">ignore</span>=<span class="value">"DuplicateDefinition"</span>>Point your device at a wall and move it slowly<span class="tag">&lt;/string></span>
<span class="lineno">   6 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"installing_arcore"</span>>Installing ARCore<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">   7 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"initializing_ar"</span>><span class="warning">Initializing AR...</span><span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   8 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"requesting_permissions"</span>>Requesting permissions...<span class="tag">&lt;/string></span>
<span class="lineno">   9 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"arcore_not_supported"</span>>ARCore is not supported on this device<span class="tag">&lt;/string></span>
<span class="lineno">  10 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"permission_required"</span>>Permission Required<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:8</span>: <span class="message">Replace "..." with ellipsis character (&#8230;, &amp;#8230;) ?</span><br /><pre class="errorlines">
<span class="lineno">   5 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"scanning_environment"</span> <span class="prefix">tools:</span><span class="attribute">ignore</span>=<span class="value">"DuplicateDefinition"</span>>Point your device at a wall and move it slowly<span class="tag">&lt;/string></span>
<span class="lineno">   6 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"installing_arcore"</span>>Installing ARCore<span class="tag">&lt;/string></span>
<span class="lineno">   7 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"initializing_ar"</span>>Initializing AR...<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">   8 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"requesting_permissions"</span>><span class="warning">Requesting permissions...</span><span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   9 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"arcore_not_supported"</span>>ARCore is not supported on this device<span class="tag">&lt;/string></span>
<span class="lineno">  10 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"permission_required"</span>>Permission Required<span class="tag">&lt;/string></span>
<span class="lineno">  11 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"camera_permission_required_message"</span>>This app requires camera permission to function. Please enable it in settings.<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:27</span>: <span class="message">Replace "..." with ellipsis character (&#8230;, &amp;#8230;) ?</span><br /><pre class="errorlines">
<span class="lineno">  24 </span>
<span class="lineno">  25 </span>    <span class="comment">&lt;!-- Deace panel --></span>
<span class="lineno">  26 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"confirm_reinit_app"</span>>RESTART APP<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  27 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"app_restarting"</span>><span class="warning">App is restarting...</span><span class="tag">&lt;/string></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  28 </span>
<span class="lineno">  29 </span>    <span class="comment">&lt;!-- --></span>
<span class="lineno">  30 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"baking_title"</span>>Baking Assistant<span class="tag">&lt;/string></span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationTypographyEllipsis" style="display: none;">
You can replace the string "..." with a dedicated ellipsis character, ellipsis character (u2026, &amp;#8230;). This can help make the text more readable.<br/><div class="moreinfo">More info: <a href="https://en.wikipedia.org/wiki/Ellipsis">https://en.wikipedia.org/wiki/Ellipsis</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "TypographyEllipsis" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">TypographyEllipsis</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Typography</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationTypographyEllipsisLink" onclick="reveal('explanationTypographyEllipsis');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="TypographyEllipsisCardLink" onclick="hideid('TypographyEllipsisCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Usability:Icons"></a>
<a name="IconXmlAndPng"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="IconXmlAndPngCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Icon is specified both as .xml file and as a bitmap</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/mipmap-xxxhdpi/ic_launcher.webp">../../src/main/res/mipmap-xxxhdpi/ic_launcher.webp</a></span>: <span class="message">The following images appear both as density independent <code>.xml</code> files and as bitmap files: srcmainresmipmap-anydpiic_launcher.xml, srcmainresmipmap-hdpiic_launcher.webp</span><br />
<ul></ul><button id="Location1DivLink" onclick="reveal('Location1Div');" />+ 5 Additional Locations...</button>
<div id="Location1Div" style="display: none">
Additional locations: <ul>
<li> <span class="location"><a href="../../src/main/res/mipmap-xxhdpi/ic_launcher.webp">../../src/main/res/mipmap-xxhdpi/ic_launcher.webp</a></span>
<li> <span class="location"><a href="../../src/main/res/mipmap-xhdpi/ic_launcher.webp">../../src/main/res/mipmap-xhdpi/ic_launcher.webp</a></span>
<li> <span class="location"><a href="../../src/main/res/mipmap-mdpi/ic_launcher.webp">../../src/main/res/mipmap-mdpi/ic_launcher.webp</a></span>
<li> <span class="location"><a href="../../src/main/res/mipmap-hdpi/ic_launcher.webp">../../src/main/res/mipmap-hdpi/ic_launcher.webp</a></span>
<li> <span class="location"><a href="../../src/main/res/mipmap-anydpi/ic_launcher.xml">../../src/main/res/mipmap-anydpi/ic_launcher.xml</a></span>
</ul>
</div><br/><br/>
<table>
<tr><td><a href="../../src/main/res/mipmap-mdpi/ic_launcher.webp"><img border="0" align="top" src="../../src/main/res/mipmap-mdpi/ic_launcher.webp" /></a>
</td><td><a href="../../src/main/res/mipmap-hdpi/ic_launcher.webp"><img border="0" align="top" src="../../src/main/res/mipmap-hdpi/ic_launcher.webp" /></a>
</td><td><a href="../../src/main/res/mipmap-xhdpi/ic_launcher.webp"><img border="0" align="top" src="../../src/main/res/mipmap-xhdpi/ic_launcher.webp" /></a>
</td><td><a href="../../src/main/res/mipmap-xxhdpi/ic_launcher.webp"><img border="0" align="top" src="../../src/main/res/mipmap-xxhdpi/ic_launcher.webp" /></a>
</td><td><a href="../../src/main/res/mipmap-xxxhdpi/ic_launcher.webp"><img border="0" align="top" src="../../src/main/res/mipmap-xxxhdpi/ic_launcher.webp" /></a>
</td></tr><tr><th></th><th></th><th></th><th></th><th></th></tr>
</table>
<span class="location"><a href="../../src/main/res/mipmap-xxxhdpi/ic_launcher_round.webp">../../src/main/res/mipmap-xxxhdpi/ic_launcher_round.webp</a></span>: <span class="message">The following images appear both as density independent <code>.xml</code> files and as bitmap files: srcmainresmipmap-anydpiic_launcher_round.xml, srcmainresmipmap-hdpiic_launcher_round.webp</span><br />
<ul></ul><button id="Location2DivLink" onclick="reveal('Location2Div');" />+ 5 Additional Locations...</button>
<div id="Location2Div" style="display: none">
Additional locations: <ul>
<li> <span class="location"><a href="../../src/main/res/mipmap-xxhdpi/ic_launcher_round.webp">../../src/main/res/mipmap-xxhdpi/ic_launcher_round.webp</a></span>
<li> <span class="location"><a href="../../src/main/res/mipmap-xhdpi/ic_launcher_round.webp">../../src/main/res/mipmap-xhdpi/ic_launcher_round.webp</a></span>
<li> <span class="location"><a href="../../src/main/res/mipmap-mdpi/ic_launcher_round.webp">../../src/main/res/mipmap-mdpi/ic_launcher_round.webp</a></span>
<li> <span class="location"><a href="../../src/main/res/mipmap-hdpi/ic_launcher_round.webp">../../src/main/res/mipmap-hdpi/ic_launcher_round.webp</a></span>
<li> <span class="location"><a href="../../src/main/res/mipmap-anydpi/ic_launcher_round.xml">../../src/main/res/mipmap-anydpi/ic_launcher_round.xml</a></span>
</ul>
</div><br/><br/>
<table>
<tr><td><a href="../../src/main/res/mipmap-mdpi/ic_launcher_round.webp"><img border="0" align="top" src="../../src/main/res/mipmap-mdpi/ic_launcher_round.webp" /></a>
</td><td><a href="../../src/main/res/mipmap-hdpi/ic_launcher_round.webp"><img border="0" align="top" src="../../src/main/res/mipmap-hdpi/ic_launcher_round.webp" /></a>
</td><td><a href="../../src/main/res/mipmap-xhdpi/ic_launcher_round.webp"><img border="0" align="top" src="../../src/main/res/mipmap-xhdpi/ic_launcher_round.webp" /></a>
</td><td><a href="../../src/main/res/mipmap-xxhdpi/ic_launcher_round.webp"><img border="0" align="top" src="../../src/main/res/mipmap-xxhdpi/ic_launcher_round.webp" /></a>
</td><td><a href="../../src/main/res/mipmap-xxxhdpi/ic_launcher_round.webp"><img border="0" align="top" src="../../src/main/res/mipmap-xxxhdpi/ic_launcher_round.webp" /></a>
</td></tr><tr><th></th><th></th><th></th><th></th><th></th></tr>
</table>
</div>
<div class="metadata"><div class="explanation" id="explanationIconXmlAndPng" style="display: none;">
If a drawable resource appears as an <code>.xml</code> file in the <code>drawable/</code> folder, it's usually not intentional for it to also appear as a bitmap using the same name; generally you expect the drawable XML file to define states and each state has a corresponding drawable bitmap.<br/>To suppress this error, use the issue id "IconXmlAndPng" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">IconXmlAndPng</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Icons</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 7/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationIconXmlAndPngLink" onclick="reveal('explanationIconXmlAndPng');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IconXmlAndPngCardLink" onclick="hideid('IconXmlAndPngCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="IconDuplicates"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="IconDuplicatesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Duplicated icons under different names</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/drawable-xhdpi/baseline_grid_on_black_18.png">../../src/main/res/drawable-xhdpi/baseline_grid_on_black_18.png</a></span>: <span class="message">The following unrelated icon files have identical contents: baseline_grid_on_black_24.png, baseline_grid_on_black_36.png, baseline_grid_on_black_18.png</span><br />
<ul></ul><button id="Location1DivLink" onclick="reveal('Location1Div');" />+ 2 Additional Locations...</button>
<div id="Location1Div" style="display: none">
Additional locations: <ul>
<li> <span class="location"><a href="../../src/main/res/drawable-mdpi/baseline_grid_on_black_36.png">../../src/main/res/drawable-mdpi/baseline_grid_on_black_36.png</a></span>
<li> <span class="location"><a href="../../src/main/res/drawable-hdpi/baseline_grid_on_black_24.png">../../src/main/res/drawable-hdpi/baseline_grid_on_black_24.png</a></span>
</ul>
</div><br/><br/>
<table>
<tr><td><a href="../../src/main/res/drawable-mdpi/baseline_grid_on_black_36.png"><img border="0" align="top" src="../../src/main/res/drawable-mdpi/baseline_grid_on_black_36.png" /></a>
</td><td><a href="../../src/main/res/drawable-hdpi/baseline_grid_on_black_24.png"><img border="0" align="top" src="../../src/main/res/drawable-hdpi/baseline_grid_on_black_24.png" /></a>
</td><td><a href="../../src/main/res/drawable-xhdpi/baseline_grid_on_black_18.png"><img border="0" align="top" src="../../src/main/res/drawable-xhdpi/baseline_grid_on_black_18.png" /></a>
</td></tr><tr><th>mdpi</th><th>hdpi</th><th>xhdpi</th></tr>
</table>
<span class="location"><a href="../../src/main/res/drawable-xxhdpi/baseline_grid_on_black_18.png">../../src/main/res/drawable-xxhdpi/baseline_grid_on_black_18.png</a></span>: <span class="message">The following unrelated icon files have identical contents: baseline_grid_on_black_36.png, baseline_grid_on_black_18.png</span><br />
<ul></ul><button id="Location2DivLink" onclick="reveal('Location2Div');" />+ 1 Additional Locations...</button>
<div id="Location2Div" style="display: none">
Additional locations: <ul>
<li> <span class="location"><a href="../../src/main/res/drawable-hdpi/baseline_grid_on_black_36.png">../../src/main/res/drawable-hdpi/baseline_grid_on_black_36.png</a></span>
</ul>
</div><br/><br/>
<table>
<tr><td><a href="../../src/main/res/drawable-hdpi/baseline_grid_on_black_36.png"><img border="0" align="top" src="../../src/main/res/drawable-hdpi/baseline_grid_on_black_36.png" /></a>
</td><td><a href="../../src/main/res/drawable-xxhdpi/baseline_grid_on_black_18.png"><img border="0" align="top" src="../../src/main/res/drawable-xxhdpi/baseline_grid_on_black_18.png" /></a>
</td></tr><tr><th>hdpi</th><th>xxhdpi</th></tr>
</table>
<span class="location"><a href="../../src/main/res/drawable-xxxhdpi/baseline_grid_on_black_18.png">../../src/main/res/drawable-xxxhdpi/baseline_grid_on_black_18.png</a></span>: <span class="message">The following unrelated icon files have identical contents: baseline_grid_on_black_48.png, baseline_grid_on_black_36.png, baseline_grid_on_black_24.png, baseline_grid_on_black_18.png</span><br />
<ul></ul><button id="Location3DivLink" onclick="reveal('Location3Div');" />+ 3 Additional Locations...</button>
<div id="Location3Div" style="display: none">
Additional locations: <ul>
<li> <span class="location"><a href="../../src/main/res/drawable-xxhdpi/baseline_grid_on_black_24.png">../../src/main/res/drawable-xxhdpi/baseline_grid_on_black_24.png</a></span>
<li> <span class="location"><a href="../../src/main/res/drawable-xhdpi/baseline_grid_on_black_36.png">../../src/main/res/drawable-xhdpi/baseline_grid_on_black_36.png</a></span>
<li> <span class="location"><a href="../../src/main/res/drawable-hdpi/baseline_grid_on_black_48.png">../../src/main/res/drawable-hdpi/baseline_grid_on_black_48.png</a></span>
</ul>
</div><br/><br/>
<table>
<tr><td><a href="../../src/main/res/drawable-hdpi/baseline_grid_on_black_48.png"><img border="0" align="top" src="../../src/main/res/drawable-hdpi/baseline_grid_on_black_48.png" /></a>
</td><td><a href="../../src/main/res/drawable-xhdpi/baseline_grid_on_black_36.png"><img border="0" align="top" src="../../src/main/res/drawable-xhdpi/baseline_grid_on_black_36.png" /></a>
</td><td><a href="../../src/main/res/drawable-xxhdpi/baseline_grid_on_black_24.png"><img border="0" align="top" src="../../src/main/res/drawable-xxhdpi/baseline_grid_on_black_24.png" /></a>
</td><td><a href="../../src/main/res/drawable-xxxhdpi/baseline_grid_on_black_18.png"><img border="0" align="top" src="../../src/main/res/drawable-xxxhdpi/baseline_grid_on_black_18.png" /></a>
</td></tr><tr><th>hdpi</th><th>xhdpi</th><th>xxhdpi</th><th>xxxhdpi</th></tr>
</table>
<span class="location"><a href="../../src/main/res/drawable-xhdpi/baseline_grid_on_black_24.png">../../src/main/res/drawable-xhdpi/baseline_grid_on_black_24.png</a></span>: <span class="message">The following unrelated icon files have identical contents: baseline_grid_on_black_48.png, baseline_grid_on_black_24.png</span><br />
<ul></ul><button id="Location4DivLink" onclick="reveal('Location4Div');" />+ 1 Additional Locations...</button>
<div id="Location4Div" style="display: none">
Additional locations: <ul>
<li> <span class="location"><a href="../../src/main/res/drawable-mdpi/baseline_grid_on_black_48.png">../../src/main/res/drawable-mdpi/baseline_grid_on_black_48.png</a></span>
</ul>
</div><br/><br/>
<table>
<tr><td><a href="../../src/main/res/drawable-mdpi/baseline_grid_on_black_48.png"><img border="0" align="top" src="../../src/main/res/drawable-mdpi/baseline_grid_on_black_48.png" /></a>
</td><td><a href="../../src/main/res/drawable-xhdpi/baseline_grid_on_black_24.png"><img border="0" align="top" src="../../src/main/res/drawable-xhdpi/baseline_grid_on_black_24.png" /></a>
</td></tr><tr><th>mdpi</th><th>xhdpi</th></tr>
</table>
<span class="location"><a href="../../src/main/res/drawable-xxxhdpi/baseline_grid_on_black_24.png">../../src/main/res/drawable-xxxhdpi/baseline_grid_on_black_24.png</a></span>: <span class="message">The following unrelated icon files have identical contents: baseline_grid_on_black_48.png, baseline_grid_on_black_24.png</span><br />
<ul></ul><button id="Location5DivLink" onclick="reveal('Location5Div');" />+ 1 Additional Locations...</button>
<div id="Location5Div" style="display: none">
Additional locations: <ul>
<li> <span class="location"><a href="../../src/main/res/drawable-xhdpi/baseline_grid_on_black_48.png">../../src/main/res/drawable-xhdpi/baseline_grid_on_black_48.png</a></span>
</ul>
</div><br/><br/>
<table>
<tr><td><a href="../../src/main/res/drawable-xhdpi/baseline_grid_on_black_48.png"><img border="0" align="top" src="../../src/main/res/drawable-xhdpi/baseline_grid_on_black_48.png" /></a>
</td><td><a href="../../src/main/res/drawable-xxxhdpi/baseline_grid_on_black_24.png"><img border="0" align="top" src="../../src/main/res/drawable-xxxhdpi/baseline_grid_on_black_24.png" /></a>
</td></tr><tr><th>xhdpi</th><th>xxxhdpi</th></tr>
</table>
<button class="mdl-button mdl-js-button mdl-button--primary" id="IconDuplicatesDivLink" onclick="reveal('IconDuplicatesDiv');" />+ 7 More Occurrences...</button>
<div id="IconDuplicatesDiv" style="display: none">
<span class="location"><a href="../../src/main/res/drawable-xxxhdpi/baseline_grid_on_black_36.png">../../src/main/res/drawable-xxxhdpi/baseline_grid_on_black_36.png</a></span>: <span class="message">The following unrelated icon files have identical contents: baseline_grid_on_black_48.png, baseline_grid_on_black_36.png</span><br />
<ul></ul><button id="Location6DivLink" onclick="reveal('Location6Div');" />+ 1 Additional Locations...</button>
<div id="Location6Div" style="display: none">
Additional locations: <ul>
<li> <span class="location"><a href="../../src/main/res/drawable-xxhdpi/baseline_grid_on_black_48.png">../../src/main/res/drawable-xxhdpi/baseline_grid_on_black_48.png</a></span>
</ul>
</div><br/><br/>
<table>
<tr><td><a href="../../src/main/res/drawable-xxhdpi/baseline_grid_on_black_48.png"><img border="0" align="top" src="../../src/main/res/drawable-xxhdpi/baseline_grid_on_black_48.png" /></a>
</td><td><a href="../../src/main/res/drawable-xxxhdpi/baseline_grid_on_black_36.png"><img border="0" align="top" src="../../src/main/res/drawable-xxxhdpi/baseline_grid_on_black_36.png" /></a>
</td></tr><tr><th>xxhdpi</th><th>xxxhdpi</th></tr>
</table>
<span class="location"><a href="../../src/main/res/drawable-xhdpi/baseline_image_black_18.png">../../src/main/res/drawable-xhdpi/baseline_image_black_18.png</a></span>: <span class="message">The following unrelated icon files have identical contents: baseline_image_black_24.png, baseline_image_black_36.png, baseline_image_black_18.png</span><br />
<ul></ul><button id="Location7DivLink" onclick="reveal('Location7Div');" />+ 2 Additional Locations...</button>
<div id="Location7Div" style="display: none">
Additional locations: <ul>
<li> <span class="location"><a href="../../src/main/res/drawable-mdpi/baseline_image_black_36.png">../../src/main/res/drawable-mdpi/baseline_image_black_36.png</a></span>
<li> <span class="location"><a href="../../src/main/res/drawable-hdpi/baseline_image_black_24.png">../../src/main/res/drawable-hdpi/baseline_image_black_24.png</a></span>
</ul>
</div><br/><br/>
<table>
<tr><td><a href="../../src/main/res/drawable-mdpi/baseline_image_black_36.png"><img border="0" align="top" src="../../src/main/res/drawable-mdpi/baseline_image_black_36.png" /></a>
</td><td><a href="../../src/main/res/drawable-hdpi/baseline_image_black_24.png"><img border="0" align="top" src="../../src/main/res/drawable-hdpi/baseline_image_black_24.png" /></a>
</td><td><a href="../../src/main/res/drawable-xhdpi/baseline_image_black_18.png"><img border="0" align="top" src="../../src/main/res/drawable-xhdpi/baseline_image_black_18.png" /></a>
</td></tr><tr><th>mdpi</th><th>hdpi</th><th>xhdpi</th></tr>
</table>
<span class="location"><a href="../../src/main/res/drawable-xxhdpi/baseline_image_black_18.png">../../src/main/res/drawable-xxhdpi/baseline_image_black_18.png</a></span>: <span class="message">The following unrelated icon files have identical contents: baseline_image_black_36.png, baseline_image_black_18.png</span><br />
<ul></ul><button id="Location8DivLink" onclick="reveal('Location8Div');" />+ 1 Additional Locations...</button>
<div id="Location8Div" style="display: none">
Additional locations: <ul>
<li> <span class="location"><a href="../../src/main/res/drawable-hdpi/baseline_image_black_36.png">../../src/main/res/drawable-hdpi/baseline_image_black_36.png</a></span>
</ul>
</div><br/><br/>
<table>
<tr><td><a href="../../src/main/res/drawable-hdpi/baseline_image_black_36.png"><img border="0" align="top" src="../../src/main/res/drawable-hdpi/baseline_image_black_36.png" /></a>
</td><td><a href="../../src/main/res/drawable-xxhdpi/baseline_image_black_18.png"><img border="0" align="top" src="../../src/main/res/drawable-xxhdpi/baseline_image_black_18.png" /></a>
</td></tr><tr><th>hdpi</th><th>xxhdpi</th></tr>
</table>
<span class="location"><a href="../../src/main/res/drawable-xxxhdpi/baseline_image_black_18.png">../../src/main/res/drawable-xxxhdpi/baseline_image_black_18.png</a></span>: <span class="message">The following unrelated icon files have identical contents: baseline_image_black_48.png, baseline_image_black_36.png, baseline_image_black_24.png, baseline_image_black_18.png</span><br />
<ul></ul><button id="Location9DivLink" onclick="reveal('Location9Div');" />+ 3 Additional Locations...</button>
<div id="Location9Div" style="display: none">
Additional locations: <ul>
<li> <span class="location"><a href="../../src/main/res/drawable-xxhdpi/baseline_image_black_24.png">../../src/main/res/drawable-xxhdpi/baseline_image_black_24.png</a></span>
<li> <span class="location"><a href="../../src/main/res/drawable-xhdpi/baseline_image_black_36.png">../../src/main/res/drawable-xhdpi/baseline_image_black_36.png</a></span>
<li> <span class="location"><a href="../../src/main/res/drawable-hdpi/baseline_image_black_48.png">../../src/main/res/drawable-hdpi/baseline_image_black_48.png</a></span>
</ul>
</div><br/><br/>
<table>
<tr><td><a href="../../src/main/res/drawable-hdpi/baseline_image_black_48.png"><img border="0" align="top" src="../../src/main/res/drawable-hdpi/baseline_image_black_48.png" /></a>
</td><td><a href="../../src/main/res/drawable-xhdpi/baseline_image_black_36.png"><img border="0" align="top" src="../../src/main/res/drawable-xhdpi/baseline_image_black_36.png" /></a>
</td><td><a href="../../src/main/res/drawable-xxhdpi/baseline_image_black_24.png"><img border="0" align="top" src="../../src/main/res/drawable-xxhdpi/baseline_image_black_24.png" /></a>
</td><td><a href="../../src/main/res/drawable-xxxhdpi/baseline_image_black_18.png"><img border="0" align="top" src="../../src/main/res/drawable-xxxhdpi/baseline_image_black_18.png" /></a>
</td></tr><tr><th>hdpi</th><th>xhdpi</th><th>xxhdpi</th><th>xxxhdpi</th></tr>
</table>
<span class="location"><a href="../../src/main/res/drawable-xhdpi/baseline_image_black_24.png">../../src/main/res/drawable-xhdpi/baseline_image_black_24.png</a></span>: <span class="message">The following unrelated icon files have identical contents: baseline_image_black_48.png, baseline_image_black_24.png</span><br />
<ul></ul><button id="Location10DivLink" onclick="reveal('Location10Div');" />+ 1 Additional Locations...</button>
<div id="Location10Div" style="display: none">
Additional locations: <ul>
<li> <span class="location"><a href="../../src/main/res/drawable-mdpi/baseline_image_black_48.png">../../src/main/res/drawable-mdpi/baseline_image_black_48.png</a></span>
</ul>
</div><br/><br/>
<table>
<tr><td><a href="../../src/main/res/drawable-mdpi/baseline_image_black_48.png"><img border="0" align="top" src="../../src/main/res/drawable-mdpi/baseline_image_black_48.png" /></a>
</td><td><a href="../../src/main/res/drawable-xhdpi/baseline_image_black_24.png"><img border="0" align="top" src="../../src/main/res/drawable-xhdpi/baseline_image_black_24.png" /></a>
</td></tr><tr><th>mdpi</th><th>xhdpi</th></tr>
</table>
<span class="location"><a href="../../src/main/res/drawable-xxxhdpi/baseline_image_black_24.png">../../src/main/res/drawable-xxxhdpi/baseline_image_black_24.png</a></span>: <span class="message">The following unrelated icon files have identical contents: baseline_image_black_48.png, baseline_image_black_24.png</span><br />
<ul></ul><button id="Location11DivLink" onclick="reveal('Location11Div');" />+ 1 Additional Locations...</button>
<div id="Location11Div" style="display: none">
Additional locations: <ul>
<li> <span class="location"><a href="../../src/main/res/drawable-xhdpi/baseline_image_black_48.png">../../src/main/res/drawable-xhdpi/baseline_image_black_48.png</a></span>
</ul>
</div><br/><br/>
<table>
<tr><td><a href="../../src/main/res/drawable-xhdpi/baseline_image_black_48.png"><img border="0" align="top" src="../../src/main/res/drawable-xhdpi/baseline_image_black_48.png" /></a>
</td><td><a href="../../src/main/res/drawable-xxxhdpi/baseline_image_black_24.png"><img border="0" align="top" src="../../src/main/res/drawable-xxxhdpi/baseline_image_black_24.png" /></a>
</td></tr><tr><th>xhdpi</th><th>xxxhdpi</th></tr>
</table>
<span class="location"><a href="../../src/main/res/drawable-xxxhdpi/baseline_image_black_36.png">../../src/main/res/drawable-xxxhdpi/baseline_image_black_36.png</a></span>: <span class="message">The following unrelated icon files have identical contents: baseline_image_black_48.png, baseline_image_black_36.png</span><br />
<ul></ul><button id="Location12DivLink" onclick="reveal('Location12Div');" />+ 1 Additional Locations...</button>
<div id="Location12Div" style="display: none">
Additional locations: <ul>
<li> <span class="location"><a href="../../src/main/res/drawable-xxhdpi/baseline_image_black_48.png">../../src/main/res/drawable-xxhdpi/baseline_image_black_48.png</a></span>
</ul>
</div><br/><br/>
<table>
<tr><td><a href="../../src/main/res/drawable-xxhdpi/baseline_image_black_48.png"><img border="0" align="top" src="../../src/main/res/drawable-xxhdpi/baseline_image_black_48.png" /></a>
</td><td><a href="../../src/main/res/drawable-xxxhdpi/baseline_image_black_36.png"><img border="0" align="top" src="../../src/main/res/drawable-xxxhdpi/baseline_image_black_36.png" /></a>
</td></tr><tr><th>xxhdpi</th><th>xxxhdpi</th></tr>
</table>
</div>
</div>
<div class="metadata"><div class="explanation" id="explanationIconDuplicates" style="display: none;">
If an icon is repeated under different names, you can consolidate and just use one of the icons and delete the others to make your application smaller. However, duplicated icons usually are not intentional and can sometimes point to icons that were accidentally overwritten or accidentally not updated.<br/>To suppress this error, use the issue id "IconDuplicates" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">IconDuplicates</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Icons</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationIconDuplicatesLink" onclick="reveal('explanationIconDuplicates');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IconDuplicatesCardLink" onclick="hideid('IconDuplicatesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Usability"></a>
<a name="ButtonStyle"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ButtonStyleCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Button should be borderless</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/config_panel.xml">../../src/main/res/layout/config_panel.xml</a>:355</span>: <span class="message">Buttons in button bars should be borderless; use <code>style="?android:attr/buttonBarButtonStyle"</code> (and <code>?android:attr/buttonBarStyle</code> on the parent)</span><br /><pre class="errorlines">
<span class="lineno"> 352 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"4dp"</span>
<span class="lineno"> 353 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno"> 354 </span>
<span class="caretline"><span class="lineno"> 355 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 356 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/reset_settings_button"</span>
<span class="lineno"> 357 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno"> 358 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/config_panel.xml">../../src/main/res/layout/config_panel.xml</a>:362</span>: <span class="message">Buttons in button bars should be borderless; use <code>style="?android:attr/buttonBarButtonStyle"</code> (and <code>?android:attr/buttonBarStyle</code> on the parent)</span><br /><pre class="errorlines">
<span class="lineno"> 359 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 360 </span>                <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"@string/reset_settings"</span> />
<span class="lineno"> 361 </span>
<span class="caretline"><span class="lineno"> 362 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 363 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/help_about_button"</span>
<span class="lineno"> 364 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno"> 365 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationButtonStyle" style="display: none;">
Button bars typically use a borderless style for the buttons. Set the <code>style="?android:attr/buttonBarButtonStyle"</code> attribute on each of the buttons, and set <code>style="?android:attr/buttonBarStyle"</code> on the parent layout<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/dialogs">https://d.android.com/r/studio-ui/designer/material/dialogs</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ButtonStyle" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ButtonStyle</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationButtonStyleLink" onclick="reveal('explanationButtonStyle');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ButtonStyleCardLink" onclick="hideid('ButtonStyleCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Productivity"></a>
<a name="UseKtx"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UseKtxCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use KTX extension function</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/deace/deacear/ar/ARRenderer.kt">../../src/main/java/com/deace/deacear/ar/ARRenderer.kt</a>:502</span>: <span class="message">Use the KTX function <code>createBitmap</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 499 </span>  <span class="keyword">fun</span> captureARFrame(frame: Frame): Bitmap? {
<span class="lineno"> 500 </span>      <span class="keyword">try</span> {
<span class="lineno"> 501 </span>          <span class="comment">// Create a bitmap with the screen dimensions</span>
<span class="caretline"><span class="lineno"> 502 </span>          <span class="keyword">val</span> bitmap = <span class="warning">Bitmap.createBitmap(screenWidth, screenHeight, Bitmap.Config.ARGB_8888)</span>&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 503 </span>
<span class="lineno"> 504 </span>          <span class="comment">// Allocate a buffer for reading pixels</span>
<span class="lineno"> 505 </span>          <span class="keyword">val</span> buffer = IntBuffer.allocate(screenWidth * screenHeight)
</pre>

<span class="location"><a href="../../src/main/java/com/deace/deacear/ar/ARRenderer.kt">../../src/main/java/com/deace/deacear/ar/ARRenderer.kt</a>:525</span>: <span class="message">Use the KTX function <code>createBitmap</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 522 </span>          bitmap.copyPixelsFromBuffer(buffer)
<span class="lineno"> 523 </span>
<span class="lineno"> 524 </span>          <span class="comment">// OpenGL reads from bottom to top, so we need to flip the bitmap vertically</span>
<span class="caretline"><span class="lineno"> 525 </span>          <span class="keyword">val</span> flippedBitmap = <span class="warning">Bitmap.createBitmap(</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 526 </span>              bitmap.width, bitmap.height, Bitmap.Config.ARGB_8888
<span class="lineno"> 527 </span>          )
</pre>

<span class="location"><a href="../../src/main/java/com/deace/deacear/ar/ARRenderer.kt">../../src/main/java/com/deace/deacear/ar/ARRenderer.kt</a>:531</span>: <span class="message">Use the KTX extension function <code>Bitmap.get</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 528 </span>
<span class="lineno"> 529 </span>       <span class="keyword">for</span> (y <span class="keyword">in</span> <span class="number">0</span> until bitmap.height) {
<span class="lineno"> 530 </span>           <span class="keyword">for</span> (x <span class="keyword">in</span> <span class="number">0</span> until bitmap.width) {
<span class="caretline"><span class="lineno"> 531 </span>               flippedBitmap.setPixel(x, bitmap.height - y - <span class="number">1</span>, <span class="warning">bitmap.getPixel(x, y)</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 532 </span>           }
<span class="lineno"> 533 </span>       }
</pre>

<span class="location"><a href="../../src/main/java/com/deace/deacear/ar/ARRenderer.kt">../../src/main/java/com/deace/deacear/ar/ARRenderer.kt</a>:531</span>: <span class="message">Use the KTX extension function <code>Bitmap.set</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 528 </span>
<span class="lineno"> 529 </span>       <span class="keyword">for</span> (y <span class="keyword">in</span> <span class="number">0</span> until bitmap.height) {
<span class="lineno"> 530 </span>           <span class="keyword">for</span> (x <span class="keyword">in</span> <span class="number">0</span> until bitmap.width) {
<span class="caretline"><span class="lineno"> 531 </span>               <span class="warning">flippedBitmap.setPixel(x, bitmap.height - y - <span class="number">1</span>, bitmap.getPixel(x, y))</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 532 </span>           }
<span class="lineno"> 533 </span>       }
</pre>

<span class="location"><a href="../../src/main/java/com/deace/deacear/ar/ARSessionManager.kt">../../src/main/java/com/deace/deacear/ar/ARSessionManager.kt</a>:2127</span>: <span class="message">Use the KTX extension function <code>Bitmap.scale</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 2124 </span>     <span class="keyword">val</span> targetHeight = (currentHeight / scaleFactor).roundToInt()
<span class="lineno"> 2125 </span>
<span class="lineno"> 2126 </span>     <span class="comment">// Create a new scaled bitmap. This uses more memory temporarily.</span>
<span class="caretline"><span class="lineno"> 2127 </span>     <span class="keyword">val</span> scaledBitmap = <span class="warning">Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, <span class="keyword">true</span>)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 2128 </span>
<span class="lineno"> 2129 </span>     <span class="comment">// Important: If the original bitmap is no longer needed, recycle it</span>
<span class="lineno"> 2130 </span>     <span class="comment">// ONLY if you are sure it's not used elsewhere and you created it yourself.</span></pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="UseKtxDivLink" onclick="reveal('UseKtxDiv');" />+ 8 More Occurrences...</button>
<div id="UseKtxDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/deace/deacear/ui/ConfigPanel.kt">../../src/main/java/com/deace/deacear/ui/ConfigPanel.kt</a>:260</span>: <span class="message">Use the KTX extension property <code>View.isVisible</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 257 </span><span class="javadoc">     * Returns whether this panel is currently active (visible or should be visible)
</span><span class="lineno"> 258 </span><span class="javadoc">     */</span>
<span class="lineno"> 259 </span>    <span class="keyword">fun</span> isActive(): Boolean {
<span class="caretline"><span class="lineno"> 260 </span>        <span class="keyword">return</span> <span class="warning">scrollView.visibility == View.VISIBLE</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 261 </span>    }
<span class="lineno"> 262 </span>}</pre>

<span class="location"><a href="../../src/main/java/com/deace/deacear/ui/DeacePanel.kt">../../src/main/java/com/deace/deacear/ui/DeacePanel.kt</a>:105</span>: <span class="message">Use the KTX extension property <code>View.isVisible</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 102 </span><span class="javadoc">     * Returns whether this panel is currently active (visible)
</span><span class="lineno"> 103 </span><span class="javadoc">     */</span>
<span class="lineno"> 104 </span>    <span class="keyword">fun</span> isActive(): Boolean {
<span class="caretline"><span class="lineno"> 105 </span>        <span class="keyword">return</span> <span class="warning">panelLayout.visibility == View.VISIBLE</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 106 </span>    }
<span class="lineno"> 107 </span>
<span class="lineno"> 108 </span>    <span class="javadoc">/**
</span></pre>

<span class="location"><a href="../../src/main/java/com/deace/deacear/ui/ExportPanel.kt">../../src/main/java/com/deace/deacear/ui/ExportPanel.kt</a>:229</span>: <span class="message">Use the KTX extension property <code>View.isVisible</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 226 </span><span class="javadoc">     * Returns whether this panel is currently active (visible or should be visible)
</span><span class="lineno"> 227 </span><span class="javadoc">     */</span>
<span class="lineno"> 228 </span>    <span class="keyword">fun</span> isActive(): Boolean {
<span class="caretline"><span class="lineno"> 229 </span>        <span class="keyword">return</span> <span class="warning">panelLayout.visibility == View.VISIBLE</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 230 </span>    }
<span class="lineno"> 231 </span>
<span class="lineno"> 232 </span>    <span class="javadoc">/**
</span></pre>

<span class="location"><a href="../../src/main/java/com/deace/deacear/ui/GridPanel.kt">../../src/main/java/com/deace/deacear/ui/GridPanel.kt</a>:339</span>: <span class="message">Use the KTX extension property <code>View.isVisible</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 336 </span><span class="javadoc">     * Returns whether this panel is currently active (visible or should be visible)
</span><span class="lineno"> 337 </span><span class="javadoc">     */</span>
<span class="lineno"> 338 </span>    <span class="keyword">fun</span> isActive(): Boolean {
<span class="caretline"><span class="lineno"> 339 </span>        <span class="keyword">return</span> <span class="warning">panelLayout.visibility == View.VISIBLE</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 340 </span>    }
<span class="lineno"> 341 </span>
<span class="lineno"> 342 </span>    <span class="javadoc">/**
</span></pre>

<span class="location"><a href="../../src/main/java/com/deace/deacear/ui/ImagePanel.kt">../../src/main/java/com/deace/deacear/ui/ImagePanel.kt</a>:571</span>: <span class="message">Use the KTX extension function <code>Bitmap.scale</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 568 </span>  <span class="keyword">try</span> {
<span class="lineno"> 569 </span>      <span class="comment">// Create a scaled-down version of the bitmap for the thumbnail</span>
<span class="lineno"> 570 </span>      <span class="keyword">val</span> thumbnailSize = <span class="number">32</span> <span class="comment">// Match the size in the layout</span>
<span class="caretline"><span class="lineno"> 571 </span>      <span class="keyword">val</span> scaledBitmap = <span class="warning">Bitmap.createScaledBitmap(loadedBitmap, thumbnailSize, thumbnailSize, <span class="keyword">true</span>)</span></span>
<span class="lineno"> 572 </span>
<span class="lineno"> 573 </span>      <span class="comment">// Set the thumbnail image and make it visible</span>
<span class="lineno"> 574 </span>      imageThumbnail.setImageBitmap(scaledBitmap)
</pre>

<span class="location"><a href="../../src/main/java/com/deace/deacear/ui/ImagePanel.kt">../../src/main/java/com/deace/deacear/ui/ImagePanel.kt</a>:750</span>: <span class="message">Use the KTX extension property <code>View.isVisible</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 747 </span><span class="javadoc">     * Returns whether this panel is currently active (visible or should be visible)
</span><span class="lineno"> 748 </span><span class="javadoc">     */</span>
<span class="lineno"> 749 </span>    <span class="keyword">fun</span> isActive(): Boolean {
<span class="caretline"><span class="lineno"> 750 </span>        <span class="keyword">return</span> <span class="warning">panelLayout.visibility == View.VISIBLE</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 751 </span>    }
<span class="lineno"> 752 </span>
<span class="lineno"> 753 </span>    <span class="keyword">fun</span> reset() {
</pre>

<span class="location"><a href="../../src/main/java/com/deace/deacear/MainActivity.kt">../../src/main/java/com/deace/deacear/MainActivity.kt</a>:1762</span>: <span class="message">Use the KTX extension property <code>View.isVisible</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 1759 </span>  <span class="comment">// Add a direct click listener to the button</span>
<span class="lineno"> 1760 </span>  loadButton.setOnClickListener {
<span class="lineno"> 1761 </span>      Log.d(<span class="string">"ImageDebug"</span>, <span class="string">"===== LOAD IMAGE BUTTON CLICKED ====="</span>)
<span class="caretline"><span class="lineno"> 1762 </span>      Log.d(<span class="string">"ImageDebug"</span>, <span class="string">"Button state: enabled=${</span>loadButton.isEnabled<span class="string">}, visible=${</span><span class="warning">loadButton.visibility == View.VISIBLE</span><span class="string">}"</span>)</span>
<span class="lineno"> 1763 </span>
<span class="lineno"> 1764 </span>      <span class="comment">// Show visual feedback</span>
<span class="lineno"> 1765 </span>      loadButton.alpha = <span class="number">0.5f</span></pre>

<span class="location"><a href="../../src/main/java/com/deace/deacear/MainActivity.kt">../../src/main/java/com/deace/deacear/MainActivity.kt</a>:1884</span>: <span class="message">Use the KTX extension property <code>View.isVisible</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 1881 </span>          <span class="string">"Image placed! Press 'Lock Image' when positioned correctly."</span>,
<span class="lineno"> 1882 </span>          Toast.LENGTH_LONG
<span class="lineno"> 1883 </span>      )
<span class="caretline"><span class="lineno"> 1884 </span>  } <span class="keyword">else</span> <span class="keyword">if</span> (!shouldShow &amp;&amp; <span class="warning">binding.quickLockImageButton.visibility == View.VISIBLE</span>) {&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 1885 </span>      <span class="comment">// Animate hiding</span>
<span class="lineno"> 1886 </span>      binding.quickLockImageButton.animate()
<span class="lineno"> 1887 </span>          .alpha(<span class="number">0f</span>)
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationUseKtx" style="display: none;">
The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>remove-defaults</b> (default is true):<br/>
Whether to skip arguments that match the defaults provided by the extension.<br/>
<br/>
Extensions often provide default values for some of the parameters. For example:
<pre>
fun Path.readLines(charset: Charset = Charsets.UTF_8): List&lt;String> { return Files.readAllLines(this, charset) }
</pre>
This lint check will by default automatically omit parameters that match the default, so if your code was calling <code></code><code>kotlin<br/>
Files.readAllLines(file, Charset.UTF_8)<br/>
</code><code></code><br/>
lint would replace this with
<pre>
file.readLines()
</pre>
rather than
<pre>
file.readLines(Charset.UTF_8
</pre>
You can turn this behavior off using this option.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UseKtx"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"remove-defaults"</span> <span class="attribute">value</span>=<span class="value">"true"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
<b>require-present</b> (default is true):<br/>
Whether to only offer extensions already available.<br/>
<br/>
This option lets you only have lint suggest extension replacements if those extensions are already available on the class path (in other words, you're already depending on the library containing the extension method.)<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UseKtx"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"require-present"</span> <span class="attribute">value</span>=<span class="value">"true"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div>To suppress this error, use the issue id "UseKtx" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UseKtx</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Productivity</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUseKtxLink" onclick="reveal('explanationUseKtx');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UseKtxCardLink" onclick="hideid('UseKtxCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UseTomlInstead"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UseTomlInsteadCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use TOML Version Catalog Instead</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:62</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 59 </span>    implementation(libs.generativeai)
<span class="lineno"> 60 </span>    implementation(libs.androidx.appcompat)
<span class="lineno"> 61 </span>    implementation(libs.core)
<span class="caretline"><span class="lineno"> 62 </span>    implementation(<span class="warning">"com.google.android.material:material:1.11.0"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 63 </span>    testImplementation(libs.junit)
<span class="lineno"> 64 </span>    androidTestImplementation(libs.androidx.junit)
<span class="lineno"> 65 </span>    androidTestImplementation(libs.androidx.espresso.core)
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUseTomlInstead" style="display: none;">
If your project is using a <code>libs.versions.toml</code> file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically).<br/>To suppress this error, use the issue id "UseTomlInstead" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UseTomlInstead</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Productivity</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUseTomlInsteadLink" onclick="reveal('explanationUseTomlInstead');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UseTomlInsteadCardLink" onclick="hideid('UseTomlInsteadCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Accessibility"></a>
<a name="ClickableViewAccessibility"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ClickableViewAccessibilityCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Accessibility in Custom Views</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/deace/deacear/MainActivity.kt">../../src/main/java/com/deace/deacear/MainActivity.kt</a>:1174</span>: <span class="message">Custom view `<code>GLSurfaceView</code>` has <code>setOnTouchListener</code> called on it but does not override <code>performClick</code></span><br /><pre class="errorlines">
<span class="lineno"> 1171 </span>        })
<span class="lineno"> 1172 </span>
<span class="lineno"> 1173 </span>        <span class="comment">// Important: Set touch listener on the SurfaceView</span>
<span class="caretline"><span class="lineno"> 1174 </span>        <span class="warning">binding.arSurfaceView.setOnTouchListener { _, event -></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 1175 </span>            <span class="keyword">var</span> consumed = <span class="keyword">false</span>
<span class="lineno"> 1176 </span>
<span class="lineno"> 1177 </span>            <span class="comment">// Handle user interaction for auto-hiding controls (spec 4.3)</span></pre>

<span class="location"><a href="../../src/main/java/com/deace/deacear/MainActivity.kt">../../src/main/java/com/deace/deacear/MainActivity.kt</a>:1174</span>: <span class="message"><code>onTouch</code> lambda should call <code>View#performClick</code> when a click is detected</span><br /><pre class="errorlines">
<span class="lineno"> 1171 </span>        })
<span class="lineno"> 1172 </span>
<span class="lineno"> 1173 </span>        <span class="comment">// Important: Set touch listener on the SurfaceView</span>
<span class="caretline"><span class="lineno"> 1174 </span>        binding.arSurfaceView.setOnTouchListener <span class="warning">{ _, event -></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 1175 </span>            <span class="keyword">var</span> consumed = <span class="keyword">false</span>
<span class="lineno"> 1176 </span>
<span class="lineno"> 1177 </span>            <span class="comment">// Handle user interaction for auto-hiding controls (spec 4.3)</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationClickableViewAccessibility" style="display: none;">
If a <code>View</code> that overrides <code>onTouchEvent</code> or uses an <code>OnTouchListener</code> does not also implement <code>performClick</code> and call it when clicks are detected, the <code>View</code> may not handle accessibility actions properly. Logic handling the click actions should ideally be placed in <code>View#performClick</code> as some accessibility services invoke <code>performClick</code> when a click action should occur.<br/>To suppress this error, use the issue id "ClickableViewAccessibility" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ClickableViewAccessibility</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Accessibility</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationClickableViewAccessibilityLink" onclick="reveal('explanationClickableViewAccessibility');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ClickableViewAccessibilityCardLink" onclick="hideid('ClickableViewAccessibilityCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Internationalization"></a>
<a name="SetTextI18n"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SetTextI18nCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">TextView Internationalization</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/deace/deacear/ui/GridPanel.kt">../../src/main/java/com/deace/deacear/ui/GridPanel.kt</a>:83</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno">  80 </span>
<span class="lineno">  81 </span>            <span class="keyword">val</span> initialProgress = (defaultOpacity * <span class="number">100f</span>).toInt()
<span class="lineno">  82 </span>            opacitySeekBar.progress = initialProgress
<span class="caretline"><span class="lineno">  83 </span>            opacityValueText.text = <span class="warning"><span class="string">"$initialProgress%"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  84 </span>
<span class="lineno">  85 </span>            <span class="keyword">return</span> <span class="keyword">true</span> <span class="comment">// Success</span>
</pre>

<span class="location"><a href="../../src/main/java/com/deace/deacear/ui/GridPanel.kt">../../src/main/java/com/deace/deacear/ui/GridPanel.kt</a>:157</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 154 </span>  opacitySeekBar.setOnSeekBarChangeListener(<span class="keyword">object</span> : SeekBar.OnSeekBarChangeListener {
<span class="lineno"> 155 </span>      override <span class="keyword">fun</span> onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
<span class="lineno"> 156 </span>          <span class="comment">// Always update the text display with direct percentage value</span>
<span class="caretline"><span class="lineno"> 157 </span>          opacityValueText.text = <span class="warning"><span class="string">"$progress%"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 158 </span>
<span class="lineno"> 159 </span>          <span class="comment">// Reset auto-hide timer on user interaction</span>
<span class="lineno"> 160 </span>          <span class="keyword">if</span> (fromUser) {
</pre>

<span class="location"><a href="../../src/main/java/com/deace/deacear/ui/GridPanel.kt">../../src/main/java/com/deace/deacear/ui/GridPanel.kt</a>:269</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 266 </span>      <span class="keyword">val</span> currentOpacity = manager.getCurrentGrid()?.getOpacity() ?: defaultOpacity
<span class="lineno"> 267 </span>      <span class="keyword">val</span> currentProgress = (currentOpacity * <span class="number">100f</span>).toInt()
<span class="lineno"> 268 </span>      opacitySeekBar.progress = currentProgress
<span class="caretline"><span class="lineno"> 269 </span>      opacityValueText.text = <span class="warning"><span class="string">"$currentProgress%"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 270 </span>      Log.d(<span class="string">"GridPanel"</span>, <span class="string">"Synced UI: isFixed=$isActuallyFixed, opacityProgress=$currentProgress"</span>)
<span class="lineno"> 271 </span>  } ?: run {
<span class="lineno"> 272 </span>      Log.w(<span class="string">"GridPanel"</span>, <span class="string">"Attempted to sync UI state but ARSessionManager is null."</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/deace/deacear/ui/GridPanel.kt">../../src/main/java/com/deace/deacear/ui/GridPanel.kt</a>:275</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 272 </span>            Log.w(<span class="string">"GridPanel"</span>, <span class="string">"Attempted to sync UI state but ARSessionManager is null."</span>)
<span class="lineno"> 273 </span>            updateFixButtonState(<span class="keyword">false</span>) <span class="comment">// Assume not fixed if no manager</span>
<span class="lineno"> 274 </span>            opacitySeekBar.progress = (defaultOpacity * <span class="number">100f</span>).toInt()
<span class="caretline"><span class="lineno"> 275 </span>            opacityValueText.text = <span class="warning"><span class="string">"${</span>opacitySeekBar.progress<span class="string">}%"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 276 </span>        }
<span class="lineno"> 277 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/deace/deacear/ui/ImagePanel.kt">../../src/main/java/com/deace/deacear/ui/ImagePanel.kt</a>:132</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 129 </span>
<span class="lineno"> 130 </span>  <span class="keyword">if</span> (::imageSelectedStatus.isInitialized) {
<span class="lineno"> 131 </span>      <span class="comment">// Set initial status</span>
<span class="caretline"><span class="lineno"> 132 </span>      imageSelectedStatus.text = <span class="string">"</span><span class="warning"><span class="string">No image selected</span></span><span class="string">"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 133 </span>      imageSelectedStatus.visibility = View.VISIBLE
<span class="lineno"> 134 </span>      Log.d(<span class="string">"ImagePanel"</span>, <span class="string">"Successfully found and initialized imageSelectedStatus TextView"</span>)
<span class="lineno"> 135 </span>  } <span class="keyword">else</span> {
</pre>

<span class="location"><a href="../../src/main/java/com/deace/deacear/ui/ImagePanel.kt">../../src/main/java/com/deace/deacear/ui/ImagePanel.kt</a>:768</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 765 </span>  <span class="comment">// Show the filename text again and reset it</span>
<span class="lineno"> 766 </span>  <span class="keyword">if</span> (::imageSelectedStatus.isInitialized) {
<span class="lineno"> 767 </span>      imageSelectedStatus.visibility = View.VISIBLE
<span class="caretline"><span class="lineno"> 768 </span>      imageSelectedStatus.text = <span class="string">"</span><span class="warning"><span class="string">No image selected</span></span><span class="string">"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 769 </span>  }
<span class="lineno"> 770 </span>  <span class="comment">// Correctly access isImageFixed property</span>
<span class="lineno"> 771 </span>  arSessionManager?.let { updateFixButtonState(it.isImageFixed) } ?: updateFixButtonState(<span class="keyword">false</span>)
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationSetTextI18n" style="display: none;">
When calling <code>TextView#setText</code><br/>
* Never call <code>Number#toString()</code> to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using <code>String#format</code> with proper format specifications (<code>%d</code> or <code>%f</code>) instead.<br/>
* Do not pass a string literal (e.g. "Hello") to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.<br/>
* Do not build messages by concatenating text chunks. Such messages can not be properly translated.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/resources/localization.html">https://developer.android.com/guide/topics/resources/localization.html</a>
</div>To suppress this error, use the issue id "SetTextI18n" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">SetTextI18n</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationSetTextI18nLink" onclick="reveal('explanationSetTextI18n');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SetTextI18nCardLink" onclick="hideid('SetTextI18nCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="HardcodedText"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="HardcodedTextCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Hardcoded text</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:202</span>: <span class="message">Hardcoded string "50%", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 199 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 200 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 201 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"4dp"</span>
<span class="caretline"><span class="lineno"> 202 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"50%"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 203 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"11sp"</span>
<span class="lineno"> 204 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span> />
<span class="lineno"> 205 </span>    <span class="tag">&lt;/LinearLayout></span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:250</span>: <span class="message">Hardcoded string "No image selected", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 247 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 248 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 249 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 250 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"No image selected"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 251 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"12sp"</span>
<span class="lineno"> 252 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span> />
<span class="lineno"> 253 </span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:279</span>: <span class="message">Hardcoded string "TEST: Place", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 276 </span>            <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/rounded_button_background"</span>
<span class="lineno"> 277 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"#FFFFFF"</span>
<span class="lineno"> 278 </span>            <span class="prefix">android:</span><span class="attribute">backgroundTint</span>=<span class="value">"#FF5722"</span>
<span class="caretline"><span class="lineno"> 279 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"TEST: Place"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 280 </span>
<span class="lineno"> 281 </span>        <span class="comment">&lt;!-- Hidden elements that we'll keep for functionality but not show in the UI --></span>
<span class="lineno"> 282 </span>        <span class="tag">&lt;androidx.appcompat.widget.AppCompatButton</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:662</span>: <span class="message">Hardcoded string "Lock Image", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 659 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"16dp"</span>
<span class="lineno"> 660 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/rounded_button_background"</span>
<span class="lineno"> 661 </span>        <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"8dp"</span>
<span class="caretline"><span class="lineno"> 662 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Lock Image"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 663 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="lineno"> 664 </span>        <span class="prefix">android:</span><span class="attribute">visibility</span>=<span class="value">"gone"</span> />
<span class="lineno"> 665 </span><span class="tag">&lt;/RelativeLayout></span></pre>

<span class="location"><a href="../../src/main/res/layout/config_panel.xml">../../src/main/res/layout/config_panel.xml</a>:303</span>: <span class="message">Hardcoded string "Graffiti_AR_{NNN}", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 300 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno"> 301 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 302 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno"> 303 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">hint</span>=<span class="value">"Graffiti_AR_{NNN}"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 304 </span>        <span class="tag">&lt;/LinearLayout></span>
<span class="lineno"> 305 </span>
<span class="lineno"> 306 </span>        <span class="comment">&lt;!-- Save Directory --></span>
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="HardcodedTextDivLink" onclick="reveal('HardcodedTextDiv');" />+ 16 More Occurrences...</button>
<div id="HardcodedTextDiv" style="display: none">
<span class="location"><a href="../../src/main/res/layout/config_panel.xml">../../src/main/res/layout/config_panel.xml</a>:325</span>: <span class="message">Hardcoded string "/Pictures/Graffiti_AR/", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 322 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno"> 323 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 324 </span>                <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno"> 325 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"/Pictures/Graffiti_AR/"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 326 </span>
<span class="lineno"> 327 </span>            <span class="tag">&lt;Button</span><span class="attribute">
</span><span class="lineno"> 328 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/save_directory_button"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/dialog_tracking_help.xml">../../src/main/res/layout/dialog_tracking_help.xml</a>:12</span>: <span class="message">Hardcoded string "Tracking Help", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">   9 </span>    <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno">  10 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  11 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  12 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Tracking Help"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  13 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span>
<span class="lineno">  14 </span>        <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
<span class="lineno">  15 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span></pre>

<span class="location"><a href="../../src/main/res/layout/dialog_tracking_help.xml">../../src/main/res/layout/dialog_tracking_help.xml</a>:34</span>: <span class="message">Hardcoded string "Red: Tracking lost. Move your device slowly to scan the environment.", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  31 </span>   <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno">  32 </span><span class="attribute">       </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  33 </span>       <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  34 </span>       <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Red: Tracking lost. Move your device slowly to scan the environment."</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  35 </span>       <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="lineno">  36 </span>       <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span>
<span class="lineno">  37 </span>       <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"8dp"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/dialog_tracking_help.xml">../../src/main/res/layout/dialog_tracking_help.xml</a>:55</span>: <span class="message">Hardcoded string "Yellow: Limited tracking. Continue moving to improve tracking quality.", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  52 </span>  <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno">  53 </span><span class="attribute">      </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  54 </span>      <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  55 </span>      <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Yellow: Limited tracking. Continue moving to improve tracking quality."</span></span>&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  56 </span>      <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="lineno">  57 </span>      <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span>
<span class="lineno">  58 </span>      <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"8dp"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/dialog_tracking_help.xml">../../src/main/res/layout/dialog_tracking_help.xml</a>:76</span>: <span class="message">Hardcoded string "Green: Good tracking. Tap on a wall to place content.", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  73 </span>        <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno">  74 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  75 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  76 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Green: Good tracking. Tap on a wall to place content."</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  77 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="lineno">  78 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span>
<span class="lineno">  79 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"8dp"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/dialog_tracking_help.xml">../../src/main/res/layout/dialog_tracking_help.xml</a>:85</span>: <span class="message">Hardcoded string "Tips for Detecting Vertical Walls:", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  82 </span>    <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno">  83 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  84 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  85 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Tips for Detecting Vertical Walls:"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  86 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"16sp"</span>
<span class="lineno">  87 </span>        <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
<span class="lineno">  88 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span></pre>

<span class="location"><a href="../../src/main/res/layout/dialog_tracking_help.xml">../../src/main/res/layout/dialog_tracking_help.xml</a>:94</span>: <span class="message">Hardcoded string "&#8226; Move your device VERY SLOWLY in a figure-8 patternn&#8226; Point camera directly at walls (not at an angle)n&#8226; Scan textured walls with patterns or objectsn&#8226; Avoid plain white or reflective surfacesn&#8226; Keep 0.5-1.5m distance from wallsn&#8226; Ensure bright, even lightingn&#8226; Make sure camera lens is cleann&#8226; Try different walls if one isn't detected", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  91 </span>  <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno">  92 </span><span class="attribute">      </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  93 </span>      <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  94 </span>      <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"• Move your device VERY SLOWLY in a figure-8 pattern\n• Point camera directly at walls (not at an angle)\n• Scan textured walls with patterns or objects\n• Avoid plain white or reflective surfaces\n• Keep 0.5-1.5m distance from walls\n• Ensure bright, even lighting\n• Make sure camera lens is clean\n• Try different walls if one isn't detected"</span></span></span>
<span class="lineno">  95 </span>      <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="lineno">  96 </span>      <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span>
<span class="lineno">  97 </span>      <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"16dp"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/dialog_tracking_help.xml">../../src/main/res/layout/dialog_tracking_help.xml</a>:103</span>: <span class="message">Hardcoded string "Got it", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 100 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_close_tracking_help"</span>
<span class="lineno"> 101 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 102 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 103 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Got it"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 104 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="lineno"> 105 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/rounded_button_background"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/image_panel.xml">../../src/main/res/layout/image_panel.xml</a>:48</span>: <span class="message">Hardcoded string "TEST: Place", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  45 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"8dp"</span>
<span class="lineno">  46 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"22sp"</span>
<span class="lineno">  47 </span>            <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
<span class="caretline"><span class="lineno">  48 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"TEST: Place"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  49 </span>            <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/rounded_button_background"</span>
<span class="lineno">  50 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"#FFFFFF"</span>
<span class="lineno">  51 </span>            <span class="prefix">android:</span><span class="attribute">backgroundTint</span>=<span class="value">"#FF5722"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/image_panel.xml">../../src/main/res/layout/image_panel.xml</a>:119</span>: <span class="message">Hardcoded string "Image Rotation", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 116 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 117 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 118 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"16dp"</span>
<span class="caretline"><span class="lineno"> 119 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Image Rotation"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 120 </span>        <span class="prefix">android:</span><span class="attribute">textAppearance</span>=<span class="value">"@style/TextAppearance.AppCompat.Medium"</span> />
<span class="lineno"> 121 </span>
<span class="lineno"> 122 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/layout/image_panel.xml">../../src/main/res/layout/image_panel.xml</a>:136</span>: <span class="message">Hardcoded string "&#8634; 90°", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 133 </span>            <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 134 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"4dp"</span>
<span class="lineno"> 135 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span>
<span class="caretline"><span class="lineno"> 136 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"↺ 90°"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 137 </span>            <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/rounded_button_background"</span>
<span class="lineno"> 138 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"#FFFFFF"</span>
<span class="lineno"> 139 </span>            <span class="prefix">android:</span><span class="attribute">backgroundTint</span>=<span class="value">"#2196F3"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/image_panel.xml">../../src/main/res/layout/image_panel.xml</a>:149</span>: <span class="message">Hardcoded string "&#8635; 90°", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 146 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"4dp"</span>
<span class="lineno"> 147 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"4dp"</span>
<span class="lineno"> 148 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span>
<span class="caretline"><span class="lineno"> 149 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"↻ 90°"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 150 </span>            <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/rounded_button_background"</span>
<span class="lineno"> 151 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"#FFFFFF"</span>
<span class="lineno"> 152 </span>            <span class="prefix">android:</span><span class="attribute">backgroundTint</span>=<span class="value">"#2196F3"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/image_panel.xml">../../src/main/res/layout/image_panel.xml</a>:161</span>: <span class="message">Hardcoded string "180°", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 158 </span>            <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 159 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"4dp"</span>
<span class="lineno"> 160 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span>
<span class="caretline"><span class="lineno"> 161 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"180°"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 162 </span>            <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/rounded_button_background"</span>
<span class="lineno"> 163 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"#FFFFFF"</span>
<span class="lineno"> 164 </span>            <span class="prefix">android:</span><span class="attribute">backgroundTint</span>=<span class="value">"#2196F3"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/image_panel.xml">../../src/main/res/layout/image_panel.xml</a>:182</span>: <span class="message">Hardcoded string "&#8634; 15°", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 179 </span>            <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 180 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"4dp"</span>
<span class="lineno"> 181 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"12sp"</span>
<span class="caretline"><span class="lineno"> 182 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"↺ 15°"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 183 </span>            <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/rounded_button_background"</span>
<span class="lineno"> 184 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"#FFFFFF"</span>
<span class="lineno"> 185 </span>            <span class="prefix">android:</span><span class="attribute">backgroundTint</span>=<span class="value">"#4CAF50"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/image_panel.xml">../../src/main/res/layout/image_panel.xml</a>:195</span>: <span class="message">Hardcoded string "&#8635; 15°", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 192 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"4dp"</span>
<span class="lineno"> 193 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"4dp"</span>
<span class="lineno"> 194 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"12sp"</span>
<span class="caretline"><span class="lineno"> 195 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"↻ 15°"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 196 </span>            <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/rounded_button_background"</span>
<span class="lineno"> 197 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"#FFFFFF"</span>
<span class="lineno"> 198 </span>            <span class="prefix">android:</span><span class="attribute">backgroundTint</span>=<span class="value">"#4CAF50"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/image_panel.xml">../../src/main/res/layout/image_panel.xml</a>:207</span>: <span class="message">Hardcoded string "Flip H", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 204 </span>            <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 205 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"4dp"</span>
<span class="lineno"> 206 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"12sp"</span>
<span class="caretline"><span class="lineno"> 207 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Flip H"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 208 </span>            <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/rounded_button_background"</span>
<span class="lineno"> 209 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"#FFFFFF"</span>
<span class="lineno"> 210 </span>            <span class="prefix">android:</span><span class="attribute">backgroundTint</span>=<span class="value">"#FF9800"</span> />
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationHardcodedText" style="display: none;">
Hardcoding text attributes directly in layout files is bad for several reasons:<br/>
<br/>
* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)<br/>
<br/>
* The application cannot be translated to other languages by just adding new translations for existing string resources.<br/>
<br/>
There are quickfixes to automatically extract this hardcoded string into a resource lookup.<br/>To suppress this error, use the issue id "HardcodedText" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">HardcodedText</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationHardcodedTextLink" onclick="reveal('explanationHardcodedText');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="HardcodedTextCardLink" onclick="hideid('HardcodedTextCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Internationalization:Bidirectional Text"></a>
<a name="RtlHardcoded"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RtlHardcodedCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using left/right instead of start/end attributes</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:42</span>: <span class="message">Consider replacing <code>android:layout_alignParentLeft</code> with <code>android:layout_alignParentStart="true"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno">  39 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/main_menu_layout"</span>
<span class="lineno">  40 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  41 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  42 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_alignParentLeft</span></span>=<span class="value">"true"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  43 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentTop</span>=<span class="value">"true"</span>
<span class="lineno">  44 </span>        <span class="prefix">android:</span><span class="attribute">layout_margin</span>=<span class="value">"16dp"</span>
<span class="lineno">  45 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/rounded_panel_background"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:122</span>: <span class="message">Consider replacing <code>android:layout_toRightOf</code> with <code>android:layout_toEndOf="@id/main_menu_layout"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 119 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 120 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 121 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentTop</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno"> 122 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_toRightOf</span></span>=<span class="value">"@id/main_menu_layout"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 123 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"16dp"</span>
<span class="lineno"> 124 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/rounded_panel_background"</span>
<span class="lineno"> 125 </span>        <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:213</span>: <span class="message">Consider replacing <code>android:layout_toRightOf</code> with <code>android:layout_toEndOf="@id/main_menu_layout"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 210 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 211 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 212 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentTop</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno"> 213 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_toRightOf</span></span>=<span class="value">"@id/main_menu_layout"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 214 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"16dp"</span>
<span class="lineno"> 215 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/rounded_panel_background"</span>
<span class="lineno"> 216 </span>        <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:331</span>: <span class="message">Consider replacing <code>android:layout_toRightOf</code> with <code>android:layout_toEndOf="@id/main_menu_layout"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 328 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 329 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 330 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentTop</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno"> 331 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_toRightOf</span></span>=<span class="value">"@id/main_menu_layout"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 332 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"16dp"</span>
<span class="lineno"> 333 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/rounded_panel_background"</span>
<span class="lineno"> 334 </span>        <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:358</span>: <span class="message">Consider replacing <code>android:layout_toRightOf</code> with <code>android:layout_toEndOf="@id/main_menu_layout"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 355 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 356 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 357 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentTop</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno"> 358 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_toRightOf</span></span>=<span class="value">"@id/main_menu_layout"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 359 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"16dp"</span>
<span class="lineno"> 360 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/rounded_panel_background"</span>
<span class="lineno"> 361 </span>        <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="RtlHardcodedDivLink" onclick="reveal('RtlHardcodedDiv');" />+ 8 More Occurrences...</button>
<div id="RtlHardcodedDiv" style="display: none">
<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:577</span>: <span class="message">Consider replacing <code>android:layout_alignParentLeft</code> with <code>android:layout_alignParentStart="true"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 574 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/tracking_indicator_container"</span>
<span class="lineno"> 575 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 576 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 577 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_alignParentLeft</span></span>=<span class="value">"true"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 578 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentBottom</span>=<span class="value">"true"</span>
<span class="lineno"> 579 </span>        <span class="prefix">android:</span><span class="attribute">layout_margin</span>=<span class="value">"16dp"</span>
<span class="lineno"> 580 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/rounded_panel_background"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:604</span>: <span class="message">Consider replacing <code>android:layout_marginLeft</code> with <code>android:layout_marginStart="8dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 601 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/tracking_quality_text"</span>
<span class="lineno"> 602 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 603 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 604 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"8dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 605 </span>                <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"@string/tracking_lost"</span>
<span class="lineno"> 606 </span>                <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="lineno"> 607 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:644</span>: <span class="message">Consider replacing <code>android:layout_alignParentRight</code> with <code>android:layout_alignParentEnd="true"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 641 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/help_button"</span>
<span class="lineno"> 642 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"48dp"</span>
<span class="lineno"> 643 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"48dp"</span>
<span class="caretline"><span class="lineno"> 644 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_alignParentRight</span></span>=<span class="value">"true"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 645 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentBottom</span>=<span class="value">"true"</span>
<span class="lineno"> 646 </span>        <span class="prefix">android:</span><span class="attribute">layout_margin</span>=<span class="value">"16dp"</span>
<span class="lineno"> 647 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/rounded_panel_background"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:657</span>: <span class="message">Consider replacing <code>android:layout_toLeftOf</code> with <code>android:layout_toStartOf="@id/help_button"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 654 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 655 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 656 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentBottom</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno"> 657 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_toLeftOf</span></span>=<span class="value">"@id/help_button"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 658 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"16dp"</span>
<span class="lineno"> 659 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginRight</span>=<span class="value">"16dp"</span>
<span class="lineno"> 660 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/rounded_button_background"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:659</span>: <span class="message">Consider replacing <code>android:layout_marginRight</code> with <code>android:layout_marginEnd="16dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 656 </span>        <span class="prefix">android:</span><span class="attribute">layout_alignParentBottom</span>=<span class="value">"true"</span>
<span class="lineno"> 657 </span>        <span class="prefix">android:</span><span class="attribute">layout_toLeftOf</span>=<span class="value">"@id/help_button"</span>
<span class="lineno"> 658 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"16dp"</span>
<span class="caretline"><span class="lineno"> 659 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginRight</span></span>=<span class="value">"16dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 660 </span>        <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/rounded_button_background"</span>
<span class="lineno"> 661 </span>        <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"8dp"</span>
<span class="lineno"> 662 </span>        <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Lock Image"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/dialog_tracking_help.xml">../../src/main/res/layout/dialog_tracking_help.xml</a>:37</span>: <span class="message">Consider replacing <code>android:layout_marginLeft</code> with <code>android:layout_marginStart="8dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno">  34 </span>           <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Red: Tracking lost. Move your device slowly to scan the environment."</span>
<span class="lineno">  35 </span>           <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="lineno">  36 </span>           <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span>
<span class="caretline"><span class="lineno">  37 </span>           <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"8dp"</span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  38 </span>   <span class="tag">&lt;/LinearLayout></span>
<span class="lineno">  39 </span>
<span class="lineno">  40 </span>   <span class="tag">&lt;LinearLayout</span></pre>

<span class="location"><a href="../../src/main/res/layout/dialog_tracking_help.xml">../../src/main/res/layout/dialog_tracking_help.xml</a>:58</span>: <span class="message">Consider replacing <code>android:layout_marginLeft</code> with <code>android:layout_marginStart="8dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno">  55 </span>          <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Yellow: Limited tracking. Continue moving to improve tracking quality."</span>
<span class="lineno">  56 </span>          <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="lineno">  57 </span>          <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span>
<span class="caretline"><span class="lineno">  58 </span>          <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"8dp"</span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  59 </span>  <span class="tag">&lt;/LinearLayout></span>
<span class="lineno">  60 </span>
<span class="lineno">  61 </span>  <span class="tag">&lt;LinearLayout</span></pre>

<span class="location"><a href="../../src/main/res/layout/dialog_tracking_help.xml">../../src/main/res/layout/dialog_tracking_help.xml</a>:79</span>: <span class="message">Consider replacing <code>android:layout_marginLeft</code> with <code>android:layout_marginStart="8dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno">  76 </span>            <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Green: Good tracking. Tap on a wall to place content."</span>
<span class="lineno">  77 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="lineno">  78 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span>
<span class="caretline"><span class="lineno">  79 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"8dp"</span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  80 </span>    <span class="tag">&lt;/LinearLayout></span>
<span class="lineno">  81 </span>
<span class="lineno">  82 </span>    <span class="tag">&lt;TextView</span></pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationRtlHardcoded" style="display: none;">
Using <code>Gravity#LEFT</code> and <code>Gravity#RIGHT</code> can lead to problems when a layout is rendered in locales where text flows from right to left. Use <code>Gravity#START</code> and <code>Gravity#END</code> instead. Similarly, in XML <code>gravity</code> and <code>layout_gravity</code> attributes, use <code>start</code> rather than <code>left</code>.<br/>
<br/>
For XML attributes such as paddingLeft and <code>layout_marginLeft</code>, use <code>paddingStart</code> and <code>layout_marginStart</code>. <b>NOTE</b>: If your <code>minSdkVersion</code> is less than 17, you should add <b>both</b> the older left/right attributes <b>as well as</b> the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.<br/>
<br/>
(Note: For <code>Gravity#LEFT</code> and <code>Gravity#START</code>, you can use these constants even when targeting older platforms, because the <code>start</code> bitmask is a superset of the <code>left</code> bitmask. Therefore, you can use <code>gravity="start"</code> rather than <code>gravity="left|start"</code>.)<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "RtlHardcoded" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">RtlHardcoded</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Bidirectional Text</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRtlHardcodedLink" onclick="reveal('explanationRtlHardcoded');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RtlHardcodedCardLink" onclick="hideid('RtlHardcodedCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="ExtraIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ExtraIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Included Additional Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
This card lists all the extra checks run by lint, provided from libraries,
build configuration and extra flags. This is included to help you verify
whether a particular check is included in analysis when configuring builds.
(Note that the list does not include the hundreds of built-in checks into lint,
only additional ones.)
<div id="IncludedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">ArcAnimationSpecTypeIssue<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
ArcAnimationSpec is designed for 2D values. Particularly, for positional values such as Offset.<br/>
Trying to use it for values of different dimensions (Float, Size, Color, etc.) will result in unpredictable animation behavior.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.animation.core<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AutoboxingStateCreation<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling <code>mutableStateOf&lt;T>()</code> when <code>T</code> is either backed by a primitive type on the JVM or is a value class results in a state implementation that requires all state values to be boxed. This usually causes an additional allocation for each state write, and adds some additional work to auto-unbox values when reading the value of the state. Instead, prefer to use a specialized primitive state implementation for <code>Int</code>, <code>Long</code>, <code>Float</code>, and <code>Double</code> when the state does not need to track null values and does not override the default <code>SnapshotMutationPolicy</code>. See <code>mutableIntStateOf()</code>, <code>mutableLongStateOf()</code>, <code>mutableFloatStateOf()</code>, and <code>mutableDoubleStateOf()</code> for more information.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AutoboxingStateValueProperty<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Avoid using the generic <code>value</code> property when using a specialized State type. Reading or writing to the state's generic <code>value</code> property will result in an unnecessary autoboxing operation. Prefer the specialized value property (e.g. <code>intValue</code> for <code>MutableIntState</code>), or use property delegation to avoid unnecessary allocations.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableNaming<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
@Composable functions without a return type should use similar naming to classes, starting with an uppercase letter and ending with a noun. @Composable functions with a return type should be treated as normal Kotlin functions, starting with a lowercase letter.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">CompositionLocalNaming<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
CompositionLocal properties should be prefixed with <code>Local</code>. This helps make it clear at their use site that these values are local to the current composition. Typically the full name will be <code>Local</code> + the type of the CompositionLocal, for example val LocalFoo = compositionLocalOf { Foo() }.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ConflictingOnColor<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
In the Material color system background colors have a corresponding 'on' color which is used for the content color inside a component. For example, a button colored <code>primary</code> will have <code>onPrimary</code> text. Because of this, it is important that there is only one possible <code>onColor</code> for a given color value, otherwise there is no way to know which 'on' color should be used inside a component. To fix this either use the same 'on' color for identical background colors, or use a different background color for each 'on' color.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.material<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">CoroutineCreationDuringComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Creating a coroutine with <code>async</code> or <code>launch</code> during composition is often incorrect - this means that a coroutine will be created even if the composition fails / is rolled back, and it also means that multiple coroutines could end up mutating the same state, causing inconsistent results. Instead, use <code>LaunchedEffect</code> and create coroutines inside the suspending block. The block will only run after a successful composition, and will cancel existing coroutines when <code>key</code> changes, allowing correct cleanup.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DetachAndAttachSameFragment<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When doing a FragmentTransaction that includes both attach()                 and detach() operations being committed on the same fragment instance, it is a                 no-op. The reason for this is that the FragmentManager optimizes all operations                 within a single transaction so the attach() and detach() cancel each other out                 and neither is actually executed. To get the desired behavior, you should separate                 the attach() and detach() calls into separate FragmentTransactions.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DialogFragmentCallbacksDetector<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When using a <code>DialogFragment</code>, the <code>setOnCancelListener</code> and                 <code>setOnDismissListener</code> callback functions within the <code>onCreateDialog</code> function                  __must not be used__ because the <code>DialogFragment</code> owns these callbacks.                  Instead the respective <code>onCancel</code> and <code>onDismiss</code> functions can be used to                  achieve the desired effect.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerMetadata<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When a library defines a Initializer, it needs to be accompanied by a corresponding &lt;meta-data> entry in the AndroidManifest.xml file.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerNoArgConstr<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Every <code>Initializer</code> must have a no argument constructor.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExperimentalAnnotationRetention<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Experimental annotations defined in Java source should use default (<code>CLASS</code>) retention, while Kotlin-sourced annotations should use <code>BINARY</code> retention.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FlowOperatorInvokedInComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling a Flow operator function within composition will result in a new Flow being created every recomposition, which will reset collectAsState() and cause other related problems. Instead Flow operators should be called inside <code>remember</code>, or a side effect such as LaunchedEffect.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentAddMenuProvider<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The Fragment lifecycle can result in a Fragment being active                 longer than its view. This can lead to unexpected behavior from lifecycle aware                 objects remaining active longer than the Fragment's view. To solve this issue,                 getViewLifecycleOwner() should be used as a LifecycleOwner rather than the                 Fragment instance once it is safe to access the view lifecycle in a                 Fragment's onCreateView, onViewCreated, onActivityCreated, or                 onViewStateRestored methods.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentBackPressedCallback<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The Fragment lifecycle can result in a Fragment being active                 longer than its view. This can lead to unexpected behavior from lifecycle aware                 objects remaining active longer than the Fragment's view. To solve this issue,                 getViewLifecycleOwner() should be used as a LifecycleOwner rather than the                 Fragment instance once it is safe to access the view lifecycle in a                 Fragment's onCreateView, onViewCreated, onActivityCreated, or                 onViewStateRestored methods.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentLiveDataObserve<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When observing a LiveData object from a fragment's onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment's view is active.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentTagUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
FragmentContainerView replaces the &lt;fragment> tag as the preferred                 way of adding fragments via XML. Unlike the &lt;fragment> tag, FragmentContainerView                 uses a normal <code>FragmentTransaction</code> under the hood to add the initial fragment,                 allowing further FragmentTransaction operations on the FragmentContainerView                 and providing a consistent timing for lifecycle events.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html">https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FrequentlyChangedStateReadInComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This property is observable and is updated after every scroll or remeasure. If you use it in the composable function directly, it will be recomposed on every change, causing potential performance issues including infinity recomposition loops. Prefer wrapping it with derivedStateOf to use calculation based on this property in composition or collect changes inside LaunchedEffect instead.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.foundation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidColorHexValue<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Creating a Color with a hex value requires a 32 bit value (such as 0xFF000000), with 8 bits being used per channel (ARGB). Not passing a full 32 bit value will result in channels being undefined / incorrect.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui.graphics<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidFragmentVersionForActivityResult<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
In order to use the ActivityResult APIs you must upgrade your                 Fragment version to 1.3.0. Previous versions of FragmentActivity                 failed to call super.onRequestPermissionsResult() and used invalid request codes<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/permissions/requesting#make-the-request">https://developer.android.com/training/permissions/requesting#make-the-request</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.activity<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=527362">https://issuetracker.google.com/issues/new?component=527362</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidLanguageTagDelimiter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
A language tag must be compliant with IETF BCP47, specifically a sequence of subtags must be separated by hyphens (-) instead of underscores (_)<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui.text<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=779818">https://issuetracker.google.com/issues/new?component=779818</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LaunchDuringComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling <code>launch</code> during composition is incorrect. Doing so will cause launch to be called multiple times resulting in a RuntimeException. Instead, use <code>SideEffect</code> and <code>launch</code> inside of the suspending block. The block will only run after a successful composition.<br/><div class="vendor">
Vendor: Jetpack Activity Compose<br/>
Identifier: androidx.activity.compose<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingColorAlphaChannel<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Creating a Color with a hex value requires a 32 bit value (such as 0xFF000000), with 8 bits being used per channel (ARGB). Not passing a full 32 bit value will result in channels being undefined. For example, passing 0xFF0000 will result in a missing alpha channel, so the color will not appear visible.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui.graphics<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierFactoryExtensionFunction<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Modifier factory functions should be defined as extension functions on Modifier to allow modifiers to be fluently chained.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierFactoryReturnType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Modifier factory functions should return Modifier as their type, and not a subtype of Modifier (such as Modifier.Element).<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierFactoryUnreferencedReceiver<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Modifier factory functions are fluently chained to construct a chain of Modifier objects that will be applied to a layout. As a result, each factory function <i>must</i> use the receiver <code>Modifier</code> parameter, to ensure that the function is returning a chain that includes previous items in the chain. Make sure the returned chain either explicitly includes <code>this</code>, such as <code>return this.then(MyModifier)</code> or implicitly by returning a chain that starts with an implicit call to another factory function, such as <code>return myModifier()</code>, where <code>myModifier</code> is defined as <code>fun Modifier.myModifier(): Modifier</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierNodeInspectableProperties<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
ModifierNodeElements may override inspectableProperties() to provide information about the modifier in the layout inspector. The default implementation attempts to read all of the properties on the class reflectively, which may not comprehensively or effectively describe the modifier.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The first (or only) Modifier parameter in a Composable function should follow the following rules:<br/>
- Be named <code>modifier</code><br/>
- Have a type of <code>Modifier</code><br/>
- Either have no default value, or have a default value of <code>Modifier</code><br/>
- If optional, be the first optional parameter in the parameter list<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MultipleAwaitPointerEventScopes<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Pointer Input events are queued inside awaitPointerEventScope. Multiple calls to awaitPointerEventScope may exit the scope. During this time there is no guarantee that the events will be queued and some events may be dropped. It is recommended to use a single top-level block and perform the pointer events processing within such block.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MutableCollectionMutableState<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Writes to mutable collections inside a MutableState will not cause a recomposition - only writes to the MutableState itself will. In most cases you should either use a read-only collection (such as List or Map) and assign a new instance to the MutableState when your data changes, or you can use an snapshot-backed collection such as SnapshotStateList or SnapshotStateMap which will correctly cause a recomposition when their contents are modified.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoCollectCallFound<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
You must call collect on the progress in the onBack function. The collect call is what properly splits the callback so it knows what to do when the back gestures is started vs when it is completed. Failing to call collect will cause all code in the block to run when the gesture is started.<br/><div class="vendor">
Vendor: Jetpack Activity Compose<br/>
Identifier: androidx.activity.compose<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NullSafeMutableLiveData<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This check ensures that LiveData values are not null when explicitly                 declared as non-nullable.<br/>
<br/>
                Kotlin interoperability does not support enforcing explicit null-safety when using                 generic Java type parameters. Since LiveData is a Java class its value can always                 be null even when its type is explicitly declared as non-nullable. This can lead                 to runtime exceptions from reading a null LiveData value that is assumed to be                 non-nullable.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">OpaqueUnitKey<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Certain Compose functions including <code>remember</code>, <code>LaunchedEffect</code>, and <code>DisposableEffect</code> declare (and sometimes require) one or more key parameters. When a key parameter changes, it is a signal that the previous invocation is now invalid. In certain cases, it may be required to pass <code>Unit</code> as a key to one of these functions, indicating that the invocation never becomes invalid. Using <code>Unit</code> as a key should be done infrequently, and should always be done explicitly by passing the <code>Unit</code> literal. This inspection checks for invocations where <code>Unit</code> is being passed as a key argument in any form other than the <code>Unit</code> literal. This is usually done by mistake, and can harm readability. If a Unit expression is being passed as a key, it is always equivalent to move the expression before the function invocation and pass the <code>Unit</code> literal instead.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ProduceStateDoesNotAssignValue<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
produceState returns an observable State using values assigned inside the producer lambda. If the lambda never assigns (i.e <code>value = foo</code>), then the State will never change. Make sure to assign a value when the source you are producing values from changes / emits a new value. For sample usage see the produceState documentation.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RememberReturnType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
A call to <code>remember</code> that returns <code>Unit</code> is always an error. This typically happens when using <code>remember</code> to mutate variables on an object. <code>remember</code> is executed during the composition, which means that if the composition fails or is happening on a separate thread, the mutated variables may not reflect the true state of the composition. Instead, use <code>SideEffect</code> to make deferred changes once the composition succeeds, or mutate <code>MutableState</code> backed variables directly, as these will handle composition failure for you.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RememberSaveableSaverParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The first parameter to <code>rememberSaveable</code> is a vararg parameter for inputs that when changed will cause the state to reset. Passing a <code>Saver</code> object to this parameter is an error, as the intention is to pass the <code>Saver</code> object to the saver parameter. Since the saver parameter is not the first parameter, it must be explicitly named.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime.saveable<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RepeatOnLifecycleWrongUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The repeatOnLifecycle APIs should be used when the View is created,                 that is in the <code>onCreate</code> lifecycle method for Activities, or <code>onViewCreated</code> in                 case you're using Fragments.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ReturnFromAwaitPointerEventScope<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Pointer Input events are queued inside awaitPointerEventScope. By using the return value of awaitPointerEventScope one might unexpectedly lose events. If another awaitPointerEventScope is restarted there is no guarantee that the events will persist between those calls. In this case you should keep all events inside the awaitPointerEventScope block<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StateFlowValueCalledInComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling StateFlow.value within composition will not observe changes to the StateFlow, so changes might not be reflected within the composition. Instead you should use stateFlow.collectAsState() to observe changes to the StateFlow, and recompose when it changes.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SuspiciousCompositionLocalModifierRead<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Jetpack Compose is unable to send updated values of a CompositionLocal when it's read in a Modifier.Node's initializer and onAttach() or onDetach() callbacks. Modifier.Node's callbacks are not aware of snapshot reads, and their lifecycle callbacks are not invoked on every recomposition. If you read a CompositionLocal in onAttach() or onDetach(), you will only get the CompositionLocal's value once at the moment of the read, which may lead to unexpected behaviors. We recommend instead reading CompositionLocals at time-of-use in callbacks that apply your Modifier's behavior, like measure() for LayoutModifierNode, draw() for DrawModifierNode, and so on. To observe the value of the CompositionLocal manually, extend from the ObserverNode interface and place the read inside an observeReads {} block within the onObservedReadsChanged() callback.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SuspiciousModifierThen<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling a Modifier factory function with an implicit receiver inside Modifier.then will result in the receiver (<code>this</code>) being added twice to the chain. For example, fun Modifier.myModifier() = this.then(otherModifier()) - the implementation of factory functions such as Modifier.otherModifier() will internally call this.then(...) to chain the provided modifier with their implementation. When you expand this.then(otherModifier()), it becomes: this.then(this.then(OtherModifierImplementation)) - so you can see that <code>this</code> is included twice in the chain, which results in modifiers such as padding being applied twice, for example. Instead, you should either remove the then() and directly chain the factory function on the receiver, this.otherModifier(), or add the empty Modifier as the receiver for the factory, such as this.then(Modifier.otherModifier())<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TestManifestGradleConfiguration<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The androidx.compose.ui:ui-test-manifest dependency is needed for launching a Compose host, such as with createComposeRule. However, it only needs to be present in testing configurations therefore use this dependency with the debugImplementation configuration<br/><div class="moreinfo">More info: <a href="https://developer.android.com/jetpack/compose/testing#setup">https://developer.android.com/jetpack/compose/testing#setup</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.compose.ui.test.manifest<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=741505">https://issuetracker.google.com/issues/new?component=741505</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnnecessaryComposedModifier<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>Modifier.composed</code> allows invoking @Composable functions when creating a <code>Modifier</code> instance - for example, using <code>remember</code> to have instance-specific state, allowing the same <code>Modifier</code> object to be safely used in multiple places. Using <code>Modifier.composed</code> without calling any @Composable functions inside is unnecessary, and since the Modifier is no longer skippable, this can cause a lot of extra work inside the composed body, leading to worse performance.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnrememberedAnimatable<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Animatable instances created during composition need to be `remember`ed, otherwise they will be recreated during recomposition, and lose their state. Either hoist the Animatable to an object that is not created during composition, or wrap the Animatable in a call to <code>remember</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.animation.core<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnrememberedMutableInteractionSource<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
MutableInteractionSource instances created during composition need to be `remember`ed, otherwise they will be recreated during recomposition, and lose their state. Either hoist the MutableInteractionSource to an object that is not created during composition, or wrap the MutableInteractionSource in a call to <code>remember</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.foundation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnrememberedMutableState<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
State objects created during composition need to be `remember`ed, otherwise they will be recreated during recomposition, and lose their state. Either hoist the state to an object that is not created during composition, or wrap the state in a call to <code>remember</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeLifecycleWhenUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If the <code>Lifecycle</code> is destroyed within the block of                     <code>Lifecycle.whenStarted</code> or any similar <code>Lifecycle.when</code> method is suspended,                     the block will be cancelled, which will also cancel any child coroutine                     launched inside the block. As as a result, If you have a try finally block                     in your code, the finally might run after the Lifecycle moves outside                     the desired state. It is recommended to check the <code>Lifecycle.isAtLeast</code>                     before accessing UI in finally block. Similarly,                     if you have a catch statement that might catch <code>CancellationException</code>,                     you should check the <code>Lifecycle.isAtLeast</code> before accessing the UI. See                     documentation of <code>Lifecycle.whenStateAtLeast</code> for more details<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageError<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with error-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageError</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageError">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageWarning<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with warning-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageWarning</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageWarning">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeRepeatOnLifecycleDetector<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The repeatOnLifecycle APIs should be used with the viewLifecycleOwner                 in Fragments as opposed to lifecycleOwner.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedBoxWithConstraintsScope<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The <code>content</code> lambda in BoxWithConstraints has a scope which will include the incoming constraints. If this scope is ignored, then the cost of subcomposition is being wasted and this BoxWithConstraints should be replaced with a Box.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.foundation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedContentLambdaTargetStateParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>content</code> lambda in AnimatedContent works as a lookup function that returns the corresponding content based on the parameter (a state of type <code>T</code>). It is important for this lambda to return content <i>specific</i> to the input parameter, so that the different contents can be properly animated. Not using the input parameter to the content lambda will result in the same content for different input (i.e. target state) and therefore an erroneous transition between the exact same content.`<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.animation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedCrossfadeTargetStateParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>content</code> lambda in Crossfade works as a lookup function that returns the corresponding content based on the parameter (a state of type <code>T</code>). It is important for this lambda to return content <i>specific</i> to the input parameter, so that the different contents can be properly crossfaded. Not using the input parameter to the content lambda will result in the same content for different input (i.e. target state) and therefore an erroneous crossfade between the exact same content.`<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.animation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedMaterial3ScaffoldPaddingParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The <code>content</code> lambda in Scaffold has a padding parameter which will include any inner padding for the content due to app bars. If this parameter is ignored, then content may be obscured by the app bars resulting in visual issues or elements that can't be interacted with.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.material3<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedMaterialScaffoldPaddingParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The <code>content</code> lambda in Scaffold has a padding parameter which will include any inner padding for the content due to app bars. If this parameter is ignored, then content may be obscured by the app bars resulting in visual issues or elements that can't be interacted with.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.material<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedTransitionTargetStateParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Transition.animate* functions provide a target state parameter in the lambda that will be used to calculate the value for a given state. This target state parameter in the lambda may or may not be the same as the actual state, as the animation system occasionally needs to look up target values for other states to do proper seeking/tooling preview. Relying on other state than the provided <code>targetState</code> could also result in unnecessary recompositions. Therefore, it is generally considered an error if this <code>targetState</code> parameter is not used.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.animation.core<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseAndroidAlpha<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>ColorStateList</code> uses app:alpha without <code>android:alpha</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseAppTint<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>ImageView</code> or <code>ImageButton</code> uses <code>android:tint</code> instead of <code>app:tint</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatLoadingForColorStateLists<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of color state lists<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatLoadingForDrawables<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of drawables<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatTextViewDrawableApis<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of compound text view drawables<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatTextViewDrawableXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>TextView</code> uses <code>android:</code> compound drawable attributes instead of <code>app:</code> ones<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseGetLayoutInflater<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Using LayoutInflater.from(Context) can return a LayoutInflater                  that does not have the correct theme.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseOfNonLambdaOffsetOverload<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>Modifier.offset()</code> is recommended to be used with static arguments only to avoid unnecessary recompositions. <code>Modifier.offset{ }</code> is preferred in the cases where the arguments are backed by a <code>State</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.foundation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseRequireInsteadOfGet<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
AndroidX added new "require____()" versions of common "get___()" APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSupportActionBar<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>AppCompatActivity.setSupportActionBar</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSwitchCompatOrMaterialCode<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>SwitchCompat</code> from AppCompat or <code>MaterialSwitch</code> from Material library<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSwitchCompatOrMaterialXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>SwitchCompat</code> from AppCompat or <code>MaterialSwitch</code> from Material library<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UsingMaterialAndMaterial3Libraries<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
material and material3 are separate design system libraries that are incompatible with each other, as they have their own distinct theming systems. Using components from both libraries concurrently can cause issues: for example material components will not pick up the correct content color from a material3 container, and vice versa.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.material3<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UsingOnClickInXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Old versions of the platform do not properly support resolving <code>android:onClick</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongRequiresOptIn<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Experimental features defined in Kotlin source code must be annotated with the Kotlin<br/>
<code>@RequiresOptIn</code> annotation. Using <code>androidx.annotation.RequiresOptIn</code> will prevent the<br/>
Kotlin compiler from enforcing its opt-in policies.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IncludedIssuesLink" onclick="reveal('IncludedIssues');">
List Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ExtraIssuesCardLink" onclick="hideid('ExtraIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="MissingIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Disabled Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
One or more issues were not run by lint, either
because the check is not enabled by default, or because
it was disabled with a command line flag or via one or
more <code>lint.xml</code> configuration files in the project directories.
<div id="SuppressedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">AppCompatMethod<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
When using the appcompat library, there are some methods you should be calling instead of the normal ones; for example, <code>getSupportActionBar()</code> instead of <code>getActionBar()</code>. This lint check looks for calls to the wrong method.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/libraries/support-library/">https://developer.android.com/topic/libraries/support-library/</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AppLinksAutoVerify<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that app links are correctly set and associated with website.<br/><div class="moreinfo">More info: <a href="https://g.co/appindexing/applinks">https://g.co/appindexing/applinks</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BackButton<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
According to the Android Design Guide,<br/>
<br/>
"Other platforms use an explicit back button with label to allow the user to navigate up the application's hierarchy. Instead, Android uses the main action bar's app icon for hierarchical navigation and the navigation bar's back button for temporal navigation."<br/>
<br/>
This check is not very sophisticated (it just looks for buttons with the label "Back"), so it is disabled by default to not trigger on common scenarios like pairs of Back/Next buttons to paginate through screens.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/design">https://d.android.com/r/studio-ui/designer/material/design</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableLambdaParameterNaming<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Composable functions with only one composable lambda parameter should use the name <code>content</code> for the parameter.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableLambdaParameterPosition<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Composable functions with only one composable lambda parameter should place the parameter at the end of the parameter list, so it can be used as a trailing lambda.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ConvertToWebp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The WebP format is typically more compact than PNG and JPEG. As of Android 4.2.1 it supports transparency and lossless conversion as well. Note that there is a quickfix in the IDE which lets you perform conversion.<br/>
<br/>
Previously, launcher icons were required to be in the PNG format but that restriction is no longer there, so lint now flags these.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DalvikOverride<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The Dalvik virtual machine will treat a package private method in one class as overriding a package private method in its super class, even if they are in separate packages.<br/>
<br/>
If you really did intend for this method to override the other, make the method <code>protected</code> instead.<br/>
<br/>
If you did <b>not</b> intend the override, consider making the method private, or changing its name or signature.<br/>
<br/>
Note that this check is disabled be default, because ART (the successor to Dalvik) no longer has this behavior.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DefaultEncoding<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Some APIs will implicitly use the default system character encoding instead of UTF-8 when converting to or from bytes, such as when creating a default <code>FileReader</code>.<br/>
<br/>
This is <i>usually</i> not correct; you only want to do this if you need to read files created by other programs where they have deliberately written in the same encoding. The default encoding varies from platform to platform and can vary from locale to locale, so this makes it difficult to interpret files containing non-ASCII characters.<br/>
<br/>
We recommend using UTF-8 everywhere.<br/>
<br/>
Note that on Android, the default file encoding is always UTF-8 (see <a href="https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(">https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(</a>) for more), so this lint check deliberately does not flag any problems in Android code, since it is always safe to rely on the default character encoding there.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DuplicateStrings<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Duplicate strings can make applications larger unnecessarily.<br/>
<br/>
This lint check looks for duplicate strings, including differences for strings where the only difference is in capitalization. Title casing and all uppercase can all be adjusted in the layout or in code.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType">https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EasterEgg<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
An "easter egg" is code deliberately hidden in the code, both from potential users and even from other developers. This lint check looks for code which looks like it may be hidden from sight.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExpensiveAssertion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
In Kotlin, assertions are not handled the same way as from the Java programming language. In particular, they're just implemented as a library call, and inside the library call the error is only thrown if assertions are enabled.<br/>
<br/>
This means that the arguments to the <code>assert</code> call will <b>always</b> be evaluated. If you're doing any computation in the expression being asserted, that computation will unconditionally be performed whether or not assertions are turned on. This typically turns into wasted work in release builds.<br/>
<br/>
This check looks for cases where the assertion condition is nontrivial, e.g. it is performing method calls or doing more work than simple comparisons on local variables or fields.<br/>
<br/>
You can work around this by writing your own inline assert method instead:<br/>

<pre>
@Suppress("INVISIBLE_REFERENCE", "INVISIBLE_MEMBER")
inline fun assert(condition: () -> Boolean) {
    if (_Assertions.ENABLED &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>
In Android, because assertions are not enforced at runtime, instead use this:<br/>

<pre>
inline fun assert(condition: () -> Boolean) {
    if (BuildConfig.DEBUG &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconExpectedSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
There are predefined sizes (for each density) for launcher icons. You should follow these conventions to make sure your icons fit in with the overall look of the platform.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/iconography">https://d.android.com/r/studio-ui/designer/material/iconography</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidPackage<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check scans through libraries looking for calls to APIs that are not included in Android.<br/>
<br/>
When you create Android projects, the classpath is set up such that you can only access classes in the API packages that are included in Android. However, if you add other projects to your libs/ folder, there is no guarantee that those .jar files were built with an Android specific classpath, and in particular, they could be accessing unsupported APIs such as java.applet.<br/>
<br/>
This check scans through library jars and looks for references to API packages that are not included in Android and flags these. This is only an error if your code calls one of the library classes which wind up referencing the unsupported package.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlinPropertyAccess<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
For a method to be represented as a property in Kotlin, strict &#8220;bean&#8221;-style prefixing must be used.<br/>
<br/>
Accessor methods require a <code>get</code> prefix or for boolean-returning methods an <code>is</code> prefix can be used.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#property-prefixes">https://android.github.io/kotlin-guides/interop.html#property-prefixes</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlincFE10<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
K2, the new version of Kotlin compiler, which encompasses the new frontend, is coming. Try to avoid using internal APIs from the old frontend if possible.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LambdaLast<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve calling this code from Kotlin, parameter types eligible for SAM conversion should be last.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last">https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintDocExample<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Lint's tool for generating documentation for each issue has special support for including a code example which shows how to trigger the report. It will pick the first unit test it can find and pick out the source file referenced from the error message, but you can instead designate a unit test to be the documentation example, and in that case, all the files are included.<br/>
<br/>
To designate a unit test as the documentation example for an issue, name the test <code>testDocumentationExample</code>, or if your detector reports multiple issues, <code>testDocumentationExample</code>&lt;Id>, such as <code>testDocumentationExampleMyId</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplPsiEquals<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
You should never compare two PSI elements for equality with <code>equals</code>; use <code>PsiEquivalenceUtil.areElementsEquivalent(PsiElement, PsiElement)</code> instead.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplUnexpectedDomain<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This checks flags URLs to domains that have not been explicitly allowed for use as a documentation source.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LogConditional<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>BuildConfig</code> class provides a constant, <code>DEBUG</code>, which indicates whether the code is being built in release mode or in debug mode. In release mode, you typically want to strip out all the logging calls. Since the compiler will automatically remove all code which is inside a <code>if (false)</code> check, surrounding your logging calls with a check for <code>BuildConfig.DEBUG</code> is a good idea.<br/>
<br/>
If you <b>really</b> intend for the logging to be present in release mode, you can suppress this warning with a <code>@SuppressLint</code> annotation for the intentional logging calls.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MangledCRLF<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
On Windows, line endings are typically recorded as carriage return plus newline: \r\n.<br/>
<br/>
This detector looks for invalid line endings with repeated carriage return characters (without newlines). Previous versions of the ADT plugin could accidentally introduce these into the file, and when editing the file, the editor could produce confusing visual artifacts.<br/><div class="moreinfo">More info: <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421">https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MinSdkTooLow<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The value of the <code>minSdkVersion</code> property is too low and can be incremented without noticeably reducing the number of supported devices.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NegativeMargin<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Margin values should be positive. Negative values are generally a sign that you are making assumptions about views surrounding the current one, or may be tempted to turn off child clipping to allow a view to escape its parent. Turning off child clipping to do this not only leads to poor graphical performance, it also results in wrong touch event handling since touch events are based strictly on a chain of parent-rect hit tests. Finally, making assumptions about the size of strings can lead to localization problems.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NewerVersionAvailable<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the <code>GradleDependency</code> check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also <b>much</b> slower.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoHardKeywords<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Do not use Kotlin&#8217;s hard keywords as the name of methods or fields. These require the use of backticks to escape when calling from Kotlin. Soft keywords, modifier keywords, and special identifiers are allowed.<br/>
<br/>
For example, ActionEvent's <code>getWhen()</code> method requires backticks when used from Kotlin:
<pre>
val timestamp = event.`when`
</pre>
<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#no-hard-keywords">https://android.github.io/kotlin-guides/interop.html#no-hard-keywords</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoOp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check looks for code which looks like it's a no-op -- usually leftover expressions from interactive debugging, but in some cases bugs where you had intended to do something with the expression such as assign it to a field.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>pure-getters</b> (default is false):<br/>
Whether to assume methods with getter-names have no side effects.<br/>
<br/>
Getter methods (where names start with <code>get</code> or <code>is</code>, and have non-void return types, and no arguments) should not have side effects. With this option turned on, lint will assume that is the case and will list any getter calls whose results are ignored as suspicious code.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"NoOp"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"pure-getters"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PermissionNamingConvention<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Permissions should be prefixed with an app's package name, using reverse-domain-style naming. This prefix should be followed by <code>.permission.</code>, and then a description of the capability that the permission represents, in upper SNAKE_CASE. For example, <code>com.example.myapp.permission.ENGAGE_HYPERSPACE</code>.<br/>
<br/>
Following this recommendation avoids naming collisions, and helps clearly identify the owner and intention of a custom permission.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PrivacySandboxBlockedCall<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Many APIs are unavailable in the Privacy Sandbox, depending on the <code>targetSdk</code>.<br/>
<br/>
If your code is designed to run in the sandbox (and never outside the sandbox) then you should remove the blocked calls to avoid exceptions at runtime.<br/>
<br/>
If your code is part of a library that can be executed both inside and outside the sandbox, surround the code with <code>if (!Process.isSdkSandbox()) { ... }</code> (or use your own field or method annotated with <code>@ChecksRestrictedEnvironment</code>) to avoid executing blocked calls when in the sandbox. Or, add the <code>@RestrictedForEnvironment</code> annotation to the containing method if the entire method should not be called when in the sandbox.<br/>
<br/>
This check is disabled by default, and should only be enabled in modules that may execute in the Privacy Sandbox.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Registered<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Activities, services and content providers should be registered in the <code>AndroidManifest.xml</code> file using <code>&lt;activity></code>, <code>&lt;service></code> and <code>&lt;provider></code> tags.<br/>
<br/>
If your activity is simply a parent class intended to be subclassed by other "real" activities, make it an abstract class.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/manifest-intro.html">https://developer.android.com/guide/topics/manifest/manifest-intro.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RequiredSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
All views must specify an explicit <code>layout_width</code> and <code>layout_height</code> attribute. There is a runtime check for this, so if you fail to specify a size, an exception is thrown at runtime.<br/>
<br/>
It's possible to specify these widths via styles as well. GridLayout, as a special case, does not require you to specify a size.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SelectableText<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
If a <code>&lt;TextView></code> is used to display data, the user might want to copy that data and paste it elsewhere. To allow this, the <code>&lt;TextView></code> should specify <code>android:textIsSelectable="true"</code>.<br/>
<br/>
This lint check looks for TextViews which are likely to be displaying data: views whose text is set dynamically.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StopShip<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Using the comment <code>// STOPSHIP</code> can be used to flag code that is incomplete but checked in. This comment marker can be used to indicate that the code should not be shipped until the issue is addressed, and lint will look for these. In Gradle projects, this is only checked for non-debug (release) builds.<br/>
<br/>
In Kotlin, the <code>TODO()</code> method is also treated as a stop ship marker; you can use it to make incomplete code compile, but it will throw an exception at runtime and therefore should be fixed before shipping releases.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StringFormatTrivial<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Every call to <code>String.format</code> creates a new <code>Formatter</code> instance, which will decrease the performance of your app. <code>String.format</code> should only be used when necessary--if the formatted string contains only trivial conversions (e.g. <code>b</code>, <code>s</code>, <code>c</code>) and there are no translation concerns, it will be more efficient to replace them and concatenate with <code>+</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SyntheticAccessor<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
A private inner class which is accessed from the outer class will force the compiler to insert a synthetic accessor; this means that you are causing extra overhead. This is not important in small projects, but is important for large apps running up against the 64K method handle limit, and especially for <b>libraries</b> where you want to make sure your library is as small as possible for the cases where your library is used in an app running up against the 64K limit.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TypographyQuotes<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Straight single quotes and double quotes, when used as a pair, can be replaced by "curvy quotes" (or directional quotes). Use the right single quotation mark for apostrophes. Never use generic quotes ", ' or free-standing accents `, ´ for quotation marks, apostrophes, or primes. This can make the text more readable.<br/><div class="moreinfo">More info: <a href="https://en.wikipedia.org/wiki/Quotation_mark">https://en.wikipedia.org/wiki/Quotation_mark</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnknownNullness<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve referencing this code from Kotlin, consider adding explicit nullness information here with either <code>@NonNull</code> or <code>@Nullable</code>.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>ignore-deprecated</b> (default is false):<br/>
Whether to ignore classes and members that have been annotated with <code>@Deprecated</code>.<br/>
<br/>
Normally this lint check will flag all unannotated elements, but by setting this option to <code>true</code> it will skip any deprecated elements.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UnknownNullness"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"ignore-deprecated"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><div class="moreinfo">More info: <a href="https://developer.android.com/kotlin/interop#nullability_annotations">https://developer.android.com/kotlin/interop#nullability_annotations</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsupportedChromeOsHardware<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>&lt;uses-feature></code> element should not require this unsupported large screen hardware feature. Any &lt;uses-feature> not explicitly marked with <code>required="false"</code> is necessary on the device to be installed on. Ensure that any features that might prevent it from being installed on a ChromeOS, large screen, or foldable device are reviewed and marked as not required in the manifest.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/arc/manifest.html#incompat-entries">https://developer.android.com/topic/arc/manifest.html#incompat-entries</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedIds<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This resource id definition appears not to be needed since it is not referenced from anywhere. Having id definitions, even if unused, is not necessarily a bad idea since they make working on layouts and menus easier, so there is not a strong reason to delete these.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ValidActionsXml<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that an actions XML file is properly formed<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">VulnerableCordovaVersion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The version of Cordova used in the app is vulnerable to security issues. Please update to the latest Apache Cordova version.<br/><div class="moreinfo">More info: <a href="https://cordova.apache.org/announcements/2015/11/20/security.html">https://cordova.apache.org/announcements/2015/11/20/security.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongThreadInterprocedural<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Searches for interprocedural call paths that violate thread annotations in the program. Tracks the flow of instantiated types and lambda expressions to increase accuracy across method boundaries.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/components/processes-and-threads.html#Threads">https://developer.android.com/guide/components/processes-and-threads.html#Threads</a>
</div><br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SuppressedIssuesLink" onclick="reveal('SuppressedIssues');">
List Missing Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingIssuesCardLink" onclick="hideid('MissingIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="SuppressInfo"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SuppressCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Suppressing Warnings and Errors</h2>
  </div>
              <div class="mdl-card__supporting-text">
Lint errors can be suppressed in a variety of ways:<br/>
<br/>
1. With a <code>@SuppressLint</code> annotation in the Java code<br/>
2. With a <code>tools:ignore</code> attribute in the XML file<br/>
3. With a //noinspection comment in the source code<br/>
4. With ignore flags specified in the <code>build.gradle</code> file, as explained below<br/>
5. With a <code>lint.xml</code> configuration file in the project<br/>
6. With a <code>lint.xml</code> configuration file passed to lint via the --config flag<br/>
7. With the --ignore flag passed to lint.<br/>
<br/>
To suppress a lint warning with an annotation, add a <code>@SuppressLint("id")</code> annotation on the class, method or variable declaration closest to the warning instance you want to disable. The id can be one or more issue id's, such as <code>"UnusedResources"</code> or <code>{"UnusedResources","UnusedIds"}</code>, or it can be <code>"all"</code> to suppress all lint warnings in the given scope.<br/>
<br/>
To suppress a lint warning with a comment, add a <code>//noinspection id</code> comment on the line before the statement with the error.<br/>
<br/>
To suppress a lint warning in an XML file, add a <code>tools:ignore="id"</code> attribute on the element containing the error, or one of its surrounding elements. You also need to define the namespace for the tools prefix on the root element in your document, next to the <code>xmlns:android</code> declaration:<br/>
<code>xmlns:tools="http://schemas.android.com/tools"</code><br/>
<br/>
To suppress a lint warning in a <code>build.gradle</code> file, add a section like this:<br/>

<pre>
android {
    lintOptions {
        disable 'TypographyFractions','TypographyQuotes'
    }
}
</pre>
<br/>
Here we specify a comma separated list of issue id's after the disable command. You can also use <code>warning</code> or <code>error</code> instead of <code>disable</code> to change the severity of issues.<br/>
<br/>
To suppress lint warnings with a configuration XML file, create a file named <code>lint.xml</code> and place it at the root directory of the module in which it applies.<br/>
<br/>
The format of the <code>lint.xml</code> file is something like the following:<br/>

<pre>
&lt;?xml version="1.0" encoding="UTF-8"?>
&lt;lint>
    &lt;!-- Ignore everything in the test source set -->
    &lt;issue id="all">
        &lt;ignore path="\*/test/\*" />
    &lt;/issue>

    &lt;!-- Disable this given check in this project -->
    &lt;issue id="IconMissingDensityFolder" severity="ignore" />

    &lt;!-- Ignore the ObsoleteLayoutParam issue in the given files -->
    &lt;issue id="ObsoleteLayoutParam">
        &lt;ignore path="res/layout/activation.xml" />
        &lt;ignore path="res/layout-xlarge/activation.xml" />
        &lt;ignore regexp="(foo|bar)\.java" />
    &lt;/issue>

    &lt;!-- Ignore the UselessLeaf issue in the given file -->
    &lt;issue id="UselessLeaf">
        &lt;ignore path="res/layout/main.xml" />
    &lt;/issue>

    &lt;!-- Change the severity of hardcoded strings to "error" -->
    &lt;issue id="HardcodedText" severity="error" />
&lt;/lint>
</pre>
<br/>
To suppress lint checks from the command line, pass the --ignore flag with a comma separated list of ids to be suppressed, such as:<br/>
<code>$ lint --ignore UnusedResources,UselessLeaf /my/project/path</code><br/>
<br/>
For more information, see <a href="https://developer.android.com/studio/write/lint.html#config">https://developer.android.com/studio/write/lint.html#config</a><br/>

            </div>
            </div>
          </section>    </div>
  </main>
</div>
</body>
</html>