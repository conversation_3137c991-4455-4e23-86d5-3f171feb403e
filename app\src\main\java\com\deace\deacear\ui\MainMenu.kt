package com.deace.deacear.ui

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.widget.ImageButton
import android.widget.LinearLayout
import com.deace.deacear.R

class MainMenu(private val menuLayout: LinearLayout, private val context: Context) {
    private var isExpanded = true
    private var currentActiveTab: View? = null
    private var isVisible = true

    // Auto-hide timer as specified in 4.3
    private val AUTO_HIDE_DELAY_MS = 3500L // 3.5 seconds
    private val handler = Handler(Looper.getMainLooper())
    private val hideRunnable = Runnable {
        if (isVisible && isExpanded) {
            minimize()
        }
    }

    private lateinit var deaceLogoView: View
    private lateinit var gridTabButton: ImageButton
    private lateinit var imageTabButton: ImageButton
    private lateinit var exportTabButton: ImageButton
    private lateinit var configTabButton: ImageButton

    private var onDeaceLogoSelected: (() -> Unit)? = null
    private var onGridTabSelected: (() -> Unit)? = null
    private var onImageTabSelected: (() -> Unit)? = null
    private var onExportTabSelected: (() -> Unit)? = null
    private var onConfigTabSelected: (() -> Unit)? = null

    init {
        Log.d("MainMenu", "Initializing...")
        try {
            // Initialize views immediately to avoid lateinit property access errors
            initializeViews()

            // Post listener setup to the UI thread to ensure views are fully inflated
            menuLayout.post {
                setupListeners()
                Log.d("MainMenu", "Listeners setup complete")
            }
        } catch (e: Exception) {
            Log.e("MainMenu", "Error during initialization", e)
        }
    }

    private fun initializeViews() {
        try {
            deaceLogoView = menuLayout.findViewById(R.id.deace_logo)
            gridTabButton = menuLayout.findViewById(R.id.grid_tab_button)
            imageTabButton = menuLayout.findViewById(R.id.image_tab_button)
            exportTabButton = menuLayout.findViewById(R.id.export_tab_button)
            configTabButton = menuLayout.findViewById(R.id.config_tab_button)

            // Verify all views were found
            if (deaceLogoView == null || gridTabButton == null || imageTabButton == null ||
                exportTabButton == null || configTabButton == null) {
                throw NullPointerException("One or more required views not found in layout")
            }

            Log.d("MainMenu", "All views initialized successfully")
        } catch (e: Exception) {
            Log.e("MainMenu", "Failed to initialize views", e)
            throw e // Re-throw to ensure caller knows initialization failed
        }
    }

    private fun setupListeners() {
        deaceLogoView.setOnClickListener {
            onDeaceLogoSelected?.invoke()
            currentActiveTab = deaceLogoView
        }

        gridTabButton.setOnClickListener {
            onGridTabSelected?.invoke()
            currentActiveTab = gridTabButton
        }

        imageTabButton.setOnClickListener {
            onImageTabSelected?.invoke()
            currentActiveTab = imageTabButton
        }

        exportTabButton.setOnClickListener {
            onExportTabSelected?.invoke()
            currentActiveTab = exportTabButton
        }

        configTabButton.setOnClickListener {
            onConfigTabSelected?.invoke()
            currentActiveTab = configTabButton
        }
    }

    fun setOnDeaceLogoSelected(listener: () -> Unit) {
        onDeaceLogoSelected = listener
    }

    fun setOnGridTabSelected(listener: () -> Unit) {
        onGridTabSelected = listener
    }

    fun setOnImageTabSelected(listener: () -> Unit) {
        onImageTabSelected = listener
    }

    fun setOnExportTabSelected(listener: () -> Unit) {
        onExportTabSelected = listener
    }

    fun setOnConfigTabSelected(listener: () -> Unit) {
        onConfigTabSelected = listener
    }

    fun toggle() {
        if (isExpanded) {
            minimize()
        } else {
            expand()
        }
    }

    fun hideGridIcon() {
        gridTabButton.visibility = View.GONE
        // Ensure all other icons remain visible
        imageTabButton.visibility = View.VISIBLE
        exportTabButton.visibility = View.VISIBLE
        configTabButton.visibility = View.VISIBLE
    }

    fun showGridIcon() {
        gridTabButton.visibility = View.VISIBLE
    }

    private fun expand() {
        menuLayout.layoutParams.width = LinearLayout.LayoutParams.WRAP_CONTENT
        isExpanded = true
    }

    fun minimize() {
        // Keep the menu at full width to ensure all icons remain visible
        menuLayout.layoutParams.width = LinearLayout.LayoutParams.WRAP_CONTENT
        isExpanded = false
    }

    fun show() {
        menuLayout.visibility = View.VISIBLE
        isVisible = true
        // Start auto-hide timer
        resetAutoHideTimer()
    }

    fun hide() {
        menuLayout.visibility = View.GONE
        isVisible = false
        // Cancel auto-hide timer when manually hidden
        cancelAutoHideTimer()
    }

    /**
     * Resets the auto-hide timer to hide controls after inactivity
     * Implements the auto-hiding controls requirement from specs (4.3)
     */
    fun resetAutoHideTimer() {
        // Remove any pending hide operations
        cancelAutoHideTimer()
        // Post a new hide operation
        handler.postDelayed(hideRunnable, AUTO_HIDE_DELAY_MS)
    }

    /**
     * Cancels the auto-hide timer
     */
    fun cancelAutoHideTimer() {
        handler.removeCallbacks(hideRunnable)
    }

    /**
     * Call this method when user interaction is detected
     * to reset the auto-hide timer
     */
    fun onUserInteraction() {
        if (!isVisible) {
            // If not visible, make it visible
            show()
        } else if (!isExpanded) {
            // If minimized, expand it
            expand()
        }
        // Reset the timer
        resetAutoHideTimer()
    }
}