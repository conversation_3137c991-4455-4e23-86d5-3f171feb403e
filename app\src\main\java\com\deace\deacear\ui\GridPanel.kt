package com.deace.deacear.ui

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log // Import Log for debugging
import android.view.View
import android.widget.Button
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.SeekBar
import android.widget.TextView
import android.widget.Toast
import com.deace.deacear.utils.MessageUtils
import com.deace.deacear.MainActivity
import com.deace.deacear.R // Ensure this import is present
import com.deace.deacear.ar.ARSessionManager
// Import ARGrid if needed for type casting, although direct interaction is preferred via ARSessionManager
// import com.deace.deacear.ar.objects.ARGrid

class GridPanel(
    private val panelLayout: LinearLayout,
    private val context: Context
) {
    // --- Views ---
    private lateinit var verticalFormatButton: ImageButton
    private lateinit var squareFormatButton: ImageButton
    private lateinit var horizontalFormatButton: ImageButton
    private lateinit var fixGridButton: Button
    private lateinit var releaseGridButton: Button
    private lateinit var opacitySeekBar: SeekBar
    private lateinit var opacityValueText: TextView

    // --- State & Dependencies ---
    private var arSessionManager: ARSessionManager? = null

    // Default opacity if no grid is present initially or manager not set yet
    private val defaultOpacity = 0.5f // Example: 50%

    // Auto-hide timer constants and components
    private val AUTO_HIDE_DELAY_MS = 5000L // 5 seconds
    private val handler = Handler(Looper.getMainLooper())
    private val hideRunnable = Runnable {
        hide()
        Log.d("GridPanel", "Auto-hiding grid panel after inactivity")
    }

    init {
        // Initialize views first
        Log.d("GridPanel", "Initializing...")
        // Post to the UI thread to ensure views are fully inflated
        panelLayout.post {
            if (initializeViews()) {
                // Setup listeners only if views were found
                setupListeners()
            } else {
                // If views fail to initialize, don't proceed with listeners
                Log.e("GridPanel", "Initialization failed. Listeners not set up.")
                panelLayout.visibility = View.GONE
            }
        }
    }

    /**
     * Finds and initializes all views within the panelLayout.
     * Returns true if successful, false otherwise.
     */
    private fun initializeViews(): Boolean {
        try {
            verticalFormatButton = panelLayout.findViewById(R.id.vertical_format_button)
            squareFormatButton = panelLayout.findViewById(R.id.square_format_button)
            horizontalFormatButton = panelLayout.findViewById(R.id.horizontal_format_button)
            fixGridButton = panelLayout.findViewById(R.id.fix_grid_button)
            releaseGridButton = panelLayout.findViewById(R.id.release_grid_button)
            opacitySeekBar = panelLayout.findViewById(R.id.grid_opacity_seekbar)
            opacityValueText = panelLayout.findViewById(R.id.grid_opacity_value)

            releaseGridButton.visibility = View.GONE
            fixGridButton.visibility = View.VISIBLE

            val initialProgress = (defaultOpacity * 100f).toInt()
            opacitySeekBar.progress = initialProgress
            opacityValueText.text = "$initialProgress%"

            return true // Success

        } catch (e: NullPointerException) {
            Log.e("GridPanel", "Error finding views in panelLayout. Check XML layout IDs.", e)
            return false // Failure
        } catch (e: Exception) {
            Log.e("GridPanel", "Unexpected error during view initialization.", e)
            return false // Failure
        }
    }

    /**
     * Sets up listeners for interactive UI elements.
     */
    private fun setupListeners() {
        verticalFormatButton.setOnClickListener {
            Log.d("GridPanel", "Vertical format button clicked")
            resetAutoHideTimer() // Reset auto-hide timer on user interaction

            arSessionManager?.let { manager ->
                // Remove existing grid if any
                manager.resetARSession()
                // Create a new grid with vertical format (9:16 aspect ratio)
                createGridWithFormat(0) // 0 = GRID_FORMAT_VERTICAL
            } ?: run {
                Log.e("GridPanel", "ARSessionManager not set when vertical format button clicked")
            }
        }

        squareFormatButton.setOnClickListener {
            Log.d("GridPanel", "Square format button clicked")
            resetAutoHideTimer() // Reset auto-hide timer on user interaction

            arSessionManager?.let { manager ->
                // Remove existing grid if any
                manager.resetARSession()
                // Create a new grid with square format (1:1 aspect ratio)
                createGridWithFormat(1) // 1 = GRID_FORMAT_SQUARE
            } ?: run {
                Log.e("GridPanel", "ARSessionManager not set when square format button clicked")
            }
        }

        horizontalFormatButton.setOnClickListener {
            Log.d("GridPanel", "Horizontal format button clicked")
            resetAutoHideTimer() // Reset auto-hide timer on user interaction

            arSessionManager?.let { manager ->
                // Remove existing grid if any
                manager.resetARSession()
                // Create a new grid with horizontal format (16:9 aspect ratio)
                createGridWithFormat(2) // 2 = GRID_FORMAT_HORIZONTAL
            } ?: run {
                Log.e("GridPanel", "ARSessionManager not set when horizontal format button clicked")
            }
        }

        fixGridButton.setOnClickListener {
            resetAutoHideTimer() // Reset auto-hide timer on user interaction
            arSessionManager?.fixGrid()
            updateFixButtonState(true)
        }

        releaseGridButton.setOnClickListener {
            resetAutoHideTimer() // Reset auto-hide timer on user interaction
            arSessionManager?.releaseGrid()
            updateFixButtonState(false)
        }

        opacitySeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                // Always update the text display with direct percentage value
                opacityValueText.text = "$progress%"

                // Reset auto-hide timer on user interaction
                if (fromUser) {
                    resetAutoHideTimer()

                    // Apply the opacity change to the current grid if it exists
                    val opacity = progress / 100f
                    arSessionManager?.getCurrentGrid()?.let { grid ->
                        grid.setOpacity(opacity)
                        Log.d("GridPanel", "Grid opacity set to $opacity")
                    } ?: run {
                        Log.d("GridPanel", "No grid available to set opacity")
                    }
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                Log.d("GridPanel", "Started adjusting opacity")
                resetAutoHideTimer() // Reset auto-hide timer on user interaction
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                Log.d("GridPanel", "Finished adjusting opacity to ${seekBar?.progress ?: 0}%")
                resetAutoHideTimer() // Reset auto-hide timer on user interaction
            }
        })
    }

    /**
     * Updates the visibility and enabled state of buttons based on the grid's fixed status.
     * @param isFixed True if the grid is intended to be fixed, false otherwise.
     */
    private fun updateFixButtonState(isFixed: Boolean) {
        Log.d("GridPanel", "Updating fix button state: isFixed = $isFixed")

        // Check if views are initialized before attempting to access them
        if (!::fixGridButton.isInitialized || !::releaseGridButton.isInitialized ||
            !::verticalFormatButton.isInitialized || !::squareFormatButton.isInitialized ||
            !::horizontalFormatButton.isInitialized || !::opacitySeekBar.isInitialized) {

            Log.w("GridPanel", "updateFixButtonState called before views were initialized")
            return
        }

        if (isFixed) {
            fixGridButton.visibility = View.GONE
            releaseGridButton.visibility = View.VISIBLE

            // Disable format buttons
            verticalFormatButton.isEnabled = false
            verticalFormatButton.alpha = 0.5f

            squareFormatButton.isEnabled = false
            squareFormatButton.alpha = 0.5f

            horizontalFormatButton.isEnabled = false
            horizontalFormatButton.alpha = 0.5f

            opacitySeekBar.isEnabled = false
        } else {
            fixGridButton.visibility = View.VISIBLE
            releaseGridButton.visibility = View.GONE

            // Enable format buttons
            verticalFormatButton.isEnabled = true
            verticalFormatButton.alpha = 1.0f

            squareFormatButton.isEnabled = true
            squareFormatButton.alpha = 1.0f

            horizontalFormatButton.isEnabled = true
            horizontalFormatButton.alpha = 1.0f

            opacitySeekBar.isEnabled = true
        }
    }

    /**
     * Sets the ARSessionManager dependency and syncs the UI state.
     * @param manager The initialized ARSessionManager instance.
     */
    fun setARSessionManager(manager: ARSessionManager) {
        this.arSessionManager = manager
        Log.d("GridPanel", "ARSessionManager set.")

        // Ensure we only sync UI after views are fully initialized
        panelLayout.post {
            syncUiStateFromManager()
        }
    }

    /**
     * Reads the current state from ARSessionManager and updates the UI elements.
     */
    private fun syncUiStateFromManager() {
        // Check if UI has been initialized before proceeding
        if (!::fixGridButton.isInitialized || !::releaseGridButton.isInitialized ||
            !::opacitySeekBar.isInitialized || !::opacityValueText.isInitialized) {
            Log.w("GridPanel", "syncUiStateFromManager called before views were initialized")
            return
        }

        arSessionManager?.let { manager ->
            // Sync fixed state - Access property directly
            val isActuallyFixed = manager.isGridFixed // CORRECTED ACCESS
            updateFixButtonState(isActuallyFixed)

            // Sync opacity state
            val currentOpacity = manager.getCurrentGrid()?.getOpacity() ?: defaultOpacity
            val currentProgress = (currentOpacity * 100f).toInt()
            opacitySeekBar.progress = currentProgress
            opacityValueText.text = "$currentProgress%"
            Log.d("GridPanel", "Synced UI: isFixed=$isActuallyFixed, opacityProgress=$currentProgress")
        } ?: run {
            Log.w("GridPanel", "Attempted to sync UI state but ARSessionManager is null.")
            updateFixButtonState(false) // Assume not fixed if no manager
            opacitySeekBar.progress = (defaultOpacity * 100f).toInt()
            opacityValueText.text = "${opacitySeekBar.progress}%"
        }
    }

    /**
     * Makes the panel visible and ensures its UI state is current.
     * Also starts the auto-hide timer.
     */
    fun show() {
        syncUiStateFromManager()
        panelLayout.visibility = View.VISIBLE
        Log.d("GridPanel", "Panel shown.")

        // Start auto-hide timer
        resetAutoHideTimer()
    }

    /**
     * Hides the panel and cancels any pending auto-hide operations.
     * Also notifies MainActivity to show the grid icon back.
     */
    fun hide() {
        panelLayout.visibility = View.GONE
        Log.d("GridPanel", "Panel hidden.")

        // Cancel auto-hide timer when manually hidden
        cancelAutoHideTimer()

        // Show the grid icon back in the main menu
        // We need to get the MainActivity instance to access the mainMenu
        try {
            val mainActivity = context as? MainActivity
            mainActivity?.let {
                it.runOnUiThread {
                    it.showGridIcon()
                }
            }
        } catch (e: Exception) {
            Log.e("GridPanel", "Error showing grid icon", e)
        }
    }

    /**
     * Resets the auto-hide timer to hide the panel after inactivity
     */
    fun resetAutoHideTimer() {
        // Remove any pending hide operations
        cancelAutoHideTimer()
        // Post a new hide operation
        handler.postDelayed(hideRunnable, AUTO_HIDE_DELAY_MS)
        Log.d("GridPanel", "Auto-hide timer reset. Panel will hide in ${AUTO_HIDE_DELAY_MS/1000} seconds if no interaction")
    }

    /**
     * Cancels any pending auto-hide operations
     */
    private fun cancelAutoHideTimer() {
        handler.removeCallbacks(hideRunnable)
    }

    /**
     * Returns whether this panel is currently active (visible or should be visible)
     */
    fun isActive(): Boolean {
        return panelLayout.visibility == View.VISIBLE
    }

    /**
     * Helper method to create a grid with the specified format
     * @param formatType The grid format type (0=vertical, 1=square, 2=horizontal)
     */
    private fun createGridWithFormat(formatType: Int) {
        // Import the constants from SettingsManager if needed
        val GRID_FORMAT_VERTICAL = 0
        val GRID_FORMAT_SQUARE = 1
        val GRID_FORMAT_HORIZONTAL = 2

        // Update the settings manager with the new format
        arSessionManager?.let { manager ->
            // Show a toast to guide the user
            val formatName = when(formatType) {
                GRID_FORMAT_VERTICAL -> "Vertical (9:16)"
                GRID_FORMAT_SQUARE -> "Square (1:1)"
                GRID_FORMAT_HORIZONTAL -> "Horizontal (16:9)"
                else -> "Unknown"
            }

            // Show a toast message to guide the user
            MessageUtils.showToast(
                context,
                "Tap on a detected surface to place a $formatName grid",
                Toast.LENGTH_LONG
            )

            // Update the UI to reflect the selected format
            // For ImageButtons, we need to use background tinting to show selection
            verticalFormatButton.setBackgroundResource(
                if (formatType == GRID_FORMAT_VERTICAL) R.drawable.selected_button_background else 0
            )
            squareFormatButton.setBackgroundResource(
                if (formatType == GRID_FORMAT_SQUARE) R.drawable.selected_button_background else 0
            )
            horizontalFormatButton.setBackgroundResource(
                if (formatType == GRID_FORMAT_HORIZONTAL) R.drawable.selected_button_background else 0
            )
        }
    }
}