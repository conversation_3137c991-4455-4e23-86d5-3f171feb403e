package com.deace.deacear.ar.objects

import android.content.Context
import android.opengl.GLES30
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.FloatBuffer
import android.opengl.Matrix

class ARGrid(context: Context, private val aspectRatio: Float, initialColor: FloatArray) {
    private var vertexBuffer: FloatBuffer
    private var colorBuffer: FloatBuffer
    private var indexBuffer: ByteBuffer
    
    private val vertexShaderCode =
        "uniform mat4 uMVPMatrix;" +
        "attribute vec4 vPosition;" +
        "attribute vec4 vColor;" +
        "varying vec4 fColor;" +
        "void main() {" +
        "  fColor = vColor;" +
        "  gl_Position = uMVPMatrix * vPosition;" +
        "}"
    
    private val fragmentShaderCode =
        "precision mediump float;" +
        "varying vec4 fColor;" +
        "void main() {" +
        "  gl_FragColor = fColor;" +
        "}"
    
    private var mProgram: Int = 0
    private var mPositionHandle: Int = 0
    private var mColorHandle: Int = 0
    private var mMVPMatrixHandle: Int = 0
    
    private val modelMatrix = FloatArray(16)
    private var opacity: Float = 1.0f
    private var lineThickness = 1.0f
    private var color = initialColor.copyOf()
    
    private val gridSize = 1.0f // Default size
    private val divisions = 10 // Number of divisions
    
    init {
        Matrix.setIdentityM(modelMatrix, 0)
        
        // Calculate vertices for the grid
        val vertices = mutableListOf<Float>()
        val colors = mutableListOf<Float>()
        
        // Calculate the actual width and height based on aspect ratio
        val width = if (aspectRatio > 1.0f) gridSize else gridSize * aspectRatio
        val height = if (aspectRatio < 1.0f) gridSize else gridSize / aspectRatio
        
        // Horizontal lines
        for (i in 0..divisions) {
            val y = -height / 2 + i * (height / divisions)
            vertices.add(-width / 2)
            vertices.add(y)
            vertices.add(0f)
            vertices.add(width / 2)
            vertices.add(y)
            vertices.add(0f)
            
            // Add color for both points
            colors.addAll(color.toList())
            colors.addAll(color.toList())
        }
        
        // Vertical lines
        for (i in 0..divisions) {
            val x = -width / 2 + i * (width / divisions)
            vertices.add(x)
            vertices.add(-height / 2)
            vertices.add(0f)
            vertices.add(x)
            vertices.add(height / 2)
            vertices.add(0f)
            
            // Add color for both points
            colors.addAll(color.toList())
            colors.addAll(color.toList())
        }
        
        // Convert vertices to buffer
        val bb = ByteBuffer.allocateDirect(vertices.size * 4)
        bb.order(ByteOrder.nativeOrder())
        vertexBuffer = bb.asFloatBuffer()
        vertexBuffer.put(vertices.toFloatArray())
        vertexBuffer.position(0)
        
        // Convert colors to buffer
        val cb = ByteBuffer.allocateDirect(colors.size * 4)
        cb.order(ByteOrder.nativeOrder())
        colorBuffer = cb.asFloatBuffer()
        colorBuffer.put(colors.toFloatArray())
        colorBuffer.position(0)
        
        // Create indices
        val indices = ByteArray(vertices.size / 3 * 2)
        for (i in indices.indices) {
            indices[i] = i.toByte()
        }
        
        indexBuffer = ByteBuffer.allocateDirect(indices.size)
        indexBuffer.put(indices)
        indexBuffer.position(0)
        
        // Prepare shaders and OpenGL program
        val vertexShader = loadShader(GLES30.GL_VERTEX_SHADER, vertexShaderCode)
        val fragmentShader = loadShader(GLES30.GL_FRAGMENT_SHADER, fragmentShaderCode)
        
        mProgram = GLES30.glCreateProgram()
        GLES30.glAttachShader(mProgram, vertexShader)
        GLES30.glAttachShader(mProgram, fragmentShader)
        GLES30.glLinkProgram(mProgram)
        
        mPositionHandle = GLES30.glGetAttribLocation(mProgram, "vPosition")
        mColorHandle = GLES30.glGetAttribLocation(mProgram, "vColor")
        mMVPMatrixHandle = GLES30.glGetUniformLocation(mProgram, "uMVPMatrix")
    }
    
    fun draw(projectionMatrix: FloatArray, viewMatrix: FloatArray) {
        // Combine the model, view, and projection matrices
        val mvpMatrix = FloatArray(16)
        Matrix.multiplyMM(mvpMatrix, 0, viewMatrix, 0, modelMatrix, 0)
        Matrix.multiplyMM(mvpMatrix, 0, projectionMatrix, 0, mvpMatrix, 0)
        
        // Add program to OpenGL environment
        GLES30.glUseProgram(mProgram)
        
        // Enable vertex arrays
        GLES30.glEnableVertexAttribArray(mPositionHandle)
        GLES30.glEnableVertexAttribArray(mColorHandle)
        
        // Prepare the coordinate data
        GLES30.glVertexAttribPointer(
            mPositionHandle, 3,
            GLES30.GL_FLOAT, false,
            3 * 4, vertexBuffer
        )
        
        // Prepare the color data
        GLES30.glVertexAttribPointer(
            mColorHandle, 4,
            GLES30.GL_FLOAT, false,
            4 * 4, colorBuffer
        )
        
        // Apply opacity to color
        val currentColor = color.copyOf()
        currentColor[3] = currentColor[3] * opacity
        
        // Set line width
        GLES30.glLineWidth(lineThickness)
        
        // Apply the combined projection and view transformation
        GLES30.glUniformMatrix4fv(mMVPMatrixHandle, 1, false, mvpMatrix, 0)
        
        // Draw the grid lines
        GLES30.glDrawElements(
            GLES30.GL_LINES, indexBuffer.remaining(),
            GLES30.GL_UNSIGNED_BYTE, indexBuffer
        )
        
        // Disable vertex arrays
        GLES30.glDisableVertexAttribArray(mPositionHandle)
        GLES30.glDisableVertexAttribArray(mColorHandle)
    }

    fun getOpacity(): Float { // Public (default), named getOpacity, returns Float
        return this.opacity // Return the internal variable
    }

    // Make sure setOpacity also exists and works with the internal variable
    fun setOpacity(newOpacity: Float) {
        this.opacity = newOpacity.coerceIn(0.0f, 1.0f)
    }

    fun updateModelMatrix(pose: com.google.ar.core.Pose) {
        pose.toMatrix(modelMatrix, 0)
    }
    
    fun translate(dx: Float, dy: Float) {
        // Simple translation - in a real app, this would be more sophisticated
        // to ensure movement stays on the detected plane
        modelMatrix[12] += dx
        modelMatrix[13] += dy
    }
    
    fun scale(scaleFactor: Float) {
        // Uniform scaling
        for (i in 0..2) {
            modelMatrix[i * 4 + 0] *= scaleFactor
            modelMatrix[i * 4 + 1] *= scaleFactor
            modelMatrix[i * 4 + 2] *= scaleFactor
        }
    }
    
    fun rotate(angleDegrees: Float) {
        // Rotate around Z-axis (perpendicular to the grid plane)
        Matrix.rotateM(modelMatrix, 0, angleDegrees, 0f, 0f, 1f)
    }

    fun setColor(color: FloatArray) {
        this.color = color.copyOf()
        // Update color buffer if needed
    }
    
    fun setLineThickness(thickness: Float) {
        this.lineThickness = thickness.coerceAtLeast(1f)
    }
    
    private fun loadShader(type: Int, shaderCode: String): Int {
        val shader = GLES30.glCreateShader(type)
        GLES30.glShaderSource(shader, shaderCode)
        GLES30.glCompileShader(shader)
        return shader
    }
    
    /**
     * Releases OpenGL resources used by this ARGrid
     */
    fun release() {
        if (mProgram != 0) {
            GLES30.glDeleteProgram(mProgram)
            mProgram = 0
        }
        
        // Clear buffers
        vertexBuffer.clear()
        colorBuffer.clear()
        indexBuffer.clear()
        
        // Clear buffer references to allow garbage collection
        vertexBuffer = ByteBuffer.allocateDirect(0).order(ByteOrder.nativeOrder()).asFloatBuffer()
        colorBuffer = ByteBuffer.allocateDirect(0).order(ByteOrder.nativeOrder()).asFloatBuffer()
        indexBuffer = ByteBuffer.allocateDirect(0)
    }
}