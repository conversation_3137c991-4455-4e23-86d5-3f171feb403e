# PowerShell script to fix ARSessionManager.kt

# Read the file content
$content = Get-Content -Path "app/src/main/java/com/deace/deacear/ar/ARSessionManager.kt" -Raw

# Replace all occurrences of calculateDistance with DistanceUtils.calculateDistance
$content = $content -replace "val distance = calculateDistance\(", "val distance = DistanceUtils.calculateDistance("
$content = $content -replace "val planeDistance = calculateDistance\(", "val planeDistance = DistanceUtils.calculateDistance("

# Write the modified content back to the file
$content | Set-Content -Path "app/src/main/java/com/deace/deacear/ar/ARSessionManager.kt"

Write-Host "Fixed ARSessionManager.kt"
