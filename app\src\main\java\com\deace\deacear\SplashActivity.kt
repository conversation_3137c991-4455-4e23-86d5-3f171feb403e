package com.deace.deacear

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.appcompat.app.AppCompatActivity

class SplashActivity : AppCompatActivity() {

    companion object {
        private const val SPLASH_DISPLAY_LENGTH = 1000L // 1 second (reduced from 2 seconds for faster launch)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // No need to set contentView since we're using a theme with background

        // Use a handler to delay loading the MainActivity
        Handler(Looper.getMainLooper()).postDelayed({
            // Create an Intent to start the MainActivity
            val mainIntent = Intent(this, MainActivity::class.java)
            startActivity(mainIntent)

            // Close splash activity
            finish()
        }, SPLASH_DISPLAY_LENGTH)
    }
}