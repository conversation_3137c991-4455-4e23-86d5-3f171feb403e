<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/load_image_button"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:textSize="24sp"
        android:textStyle="bold"
        android:text="@string/load_image" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/image_size_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/no_image_loaded"
        android:textAppearance="@style/TextAppearance.AppCompat.Small" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/fix_image_button"
            android:layout_width="0dp"
            android:layout_height="120dp"
            android:layout_weight="1"
            android:textSize="22sp"
            android:textStyle="bold"
            android:text="@string/fix_image" />

        <!-- Test button for debugging tap detection -->
        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/test_image_placement_button"
            android:layout_width="0dp"
            android:layout_height="120dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:textSize="22sp"
            android:textStyle="bold"
            android:text="TEST: Place"
            android:background="@drawable/rounded_button_background"
            android:textColor="#FFFFFF"
            android:backgroundTint="#FF5722" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/release_image_button"
            android:layout_width="0dp"
            android:layout_height="120dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:textSize="22sp"
            android:textStyle="bold"
            android:visibility="gone"
            android:text="@string/release_image" />
    </LinearLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/image_opacity"
        android:textAppearance="@style/TextAppearance.AppCompat.Medium" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatSeekBar
            android:id="@+id/image_opacity_seekbar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:max="100"
            android:progress="50" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/image_opacity_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="@string/opacity_format_50" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/preserve_aspect_switch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:text="@string/preserve_aspect_ratio" />
    </LinearLayout>

    <!-- Rotation Controls -->
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Image Rotation"
        android:textAppearance="@style/TextAppearance.AppCompat.Medium" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal"
        android:gravity="center">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/rotate_left_90_button"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_weight="1"
            android:layout_marginEnd="4dp"
            android:textSize="14sp"
            android:text="↺ 90°"
            android:background="@drawable/rounded_button_background"
            android:textColor="#FFFFFF"
            android:backgroundTint="#2196F3" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/rotate_right_90_button"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_weight="1"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:textSize="14sp"
            android:text="↻ 90°"
            android:background="@drawable/rounded_button_background"
            android:textColor="#FFFFFF"
            android:backgroundTint="#2196F3" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/rotate_180_button"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_weight="1"
            android:layout_marginStart="4dp"
            android:textSize="14sp"
            android:text="180°"
            android:background="@drawable/rounded_button_background"
            android:textColor="#FFFFFF"
            android:backgroundTint="#2196F3" />
    </LinearLayout>

    <!-- Fine Rotation Controls -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal"
        android:gravity="center">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/rotate_left_15_button"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:layout_marginEnd="4dp"
            android:textSize="12sp"
            android:text="↺ 15°"
            android:background="@drawable/rounded_button_background"
            android:textColor="#FFFFFF"
            android:backgroundTint="#4CAF50" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/rotate_right_15_button"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:textSize="12sp"
            android:text="↻ 15°"
            android:background="@drawable/rounded_button_background"
            android:textColor="#FFFFFF"
            android:backgroundTint="#4CAF50" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/flip_horizontal_button"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:layout_marginStart="4dp"
            android:textSize="12sp"
            android:text="Flip H"
            android:background="@drawable/rounded_button_background"
            android:textColor="#FFFFFF"
            android:backgroundTint="#FF9800" />
    </LinearLayout>
</LinearLayout>