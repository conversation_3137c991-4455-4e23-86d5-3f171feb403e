Here's the complete implementation of `SettingsManager.kt` for the Graffiti AR Projection Assistant:

```kotlin
package com.example.graffitiar.utils

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit

class SettingsManager(context: Context) {
    companion object {
        // Grid format constants
        const val GRID_FORMAT_VERTICAL = 0
        const val GRID_FORMAT_SQUARE = 1
        const val GRID_FORMAT_HORIZONTAL = 2

        // Image quality constants
        const val IMAGE_QUALITY_NORMAL = 0
        const val IMAGE_QUALITY_MAX = 1

        // File format constants
        const val FILE_FORMAT_PNG = 0
        const val FILE_FORMAT_JPG = 1

        // Default values
        private const val DEFAULT_GRID_FORMAT = GRID_FORMAT_VERTICAL
        private const val DEFAULT_GRID_OPACITY = 0.5f
        private val DEFAULT_GRID_COLOR = floatArrayOf(0.2f, 0.8f, 0.8f, 1.0f) // RGBA
        private const val DEFAULT_GRID_LINE_THICKNESS = 1.0f // 1px
        private const val DEFAULT_IMAGE_OPACITY = 0.5f
        private const val DEFAULT_PRESERVE_ASPECT_RATIO = true
        private const val DEFAULT_IMAGE_QUALITY = IMAGE_QUALITY_NORMAL
        private const val DEFAULT_FILE_FORMAT = FILE_FORMAT_PNG
        private const val DEFAULT_FILENAME_PATTERN = "Graffiti_AR_{timestamp}"
        private const val DEFAULT_SHOW_PLANES = true
    }

    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences("GraffitiARPrefs", Context.MODE_PRIVATE)

    // AR Settings
    var showPlanes: Boolean
        get() = sharedPreferences.getBoolean("show_planes", DEFAULT_SHOW_PLANES)
        set(value) = sharedPreferences.edit { putBoolean("show_planes", value) }

    // Grid Options
    var defaultGridFormat: Int
        get() = sharedPreferences.getInt("grid_format", DEFAULT_GRID_FORMAT)
        set(value) = sharedPreferences.edit { putInt("grid_format", value) }

    var defaultGridOpacity: Float
        get() = sharedPreferences.getFloat("grid_opacity", DEFAULT_GRID_OPACITY)
        set(value) = sharedPreferences.edit { putFloat("grid_opacity", value) }

    var defaultGridColor: FloatArray
        get() = floatArrayOf(
            sharedPreferences.getFloat("grid_color_r", DEFAULT_GRID_COLOR[0]),
            sharedPreferences.getFloat("grid_color_g", DEFAULT_GRID_COLOR[1]),
            sharedPreferences.getFloat("grid_color_b", DEFAULT_GRID_COLOR[2]),
            sharedPreferences.getFloat("grid_color_a", DEFAULT_GRID_COLOR[3])
        )
        set(value) = sharedPreferences.edit {
            putFloat("grid_color_r", value[0])
            putFloat("grid_color_g", value[1])
            putFloat("grid_color_b", value[2])
            putFloat("grid_color_a", value[3])
        }

    var defaultGridLineThickness: Float
        get() = sharedPreferences.getFloat("grid_line_thickness", DEFAULT_GRID_LINE_THICKNESS)
        set(value) = sharedPreferences.edit { putFloat("grid_line_thickness", value) }

    // Image Options
    var defaultImageOpacity: Float
        get() = sharedPreferences.getFloat("image_opacity", DEFAULT_IMAGE_OPACITY)
        set(value) = sharedPreferences.edit { putFloat("image_opacity", value) }

    var preserveAspectRatio: Boolean
        get() = sharedPreferences.getBoolean("preserve_aspect", DEFAULT_PRESERVE_ASPECT_RATIO)
        set(value) = sharedPreferences.edit { putBoolean("preserve_aspect", value) }

    // Export Settings
    var imageQuality: Int
        get() = sharedPreferences.getInt("image_quality", DEFAULT_IMAGE_QUALITY)
        set(value) = sharedPreferences.edit { putInt("image_quality", value) }

    var fileFormat: Int
        get() = sharedPreferences.getInt("file_format", DEFAULT_FILE_FORMAT)
        set(value) = sharedPreferences.edit { putInt("file_format", value) }

    var filenamePattern: String
        get() = sharedPreferences.getString("filename_pattern", DEFAULT_FILENAME_PATTERN) 
            ?: DEFAULT_FILENAME_PATTERN
        set(value) = sharedPreferences.edit { putString("filename_pattern", value) }

    var saveDirectory: String
        get() = sharedPreferences.getString("save_directory", 
            "${Environment.DIRECTORY_PICTURES}/Graffiti_AR") 
            ?: "${Environment.DIRECTORY_PICTURES}/Graffiti_AR"
        set(value) = sharedPreferences.edit { putString("save_directory", value) }

    // Reset all settings to defaults
    fun resetToDefaults() {
        sharedPreferences.edit {
            clear()
            apply()
        }
    }

    // Helper function to process filename pattern with current timestamp
    fun generateFilename(): String {
        val timestamp = System.currentTimeMillis()
        return filenamePattern.replace("{timestamp}", timestamp.toString())
    }

    // Helper to get file extension based on format
    fun getFileExtension(): String {
        return when (fileFormat) {
            FILE_FORMAT_PNG -> ".png"
            FILE_FORMAT_JPG -> ".jpg"
            else -> ".png"
        }
    }
}
```

### Key Features:

1. **Comprehensive Settings Management**:
   - Handles all configurable options from the specifications
   - Organized into logical categories (AR, Grid, Image, Export)

2. **Type-Safe Access**:
   - Properly typed properties for all settings
   - Default values defined as constants

3. **Persistence**:
   - Uses SharedPreferences for simple, reliable storage
   - Atomic writes with `edit()` and `apply()`

4. **Convenience Methods**:
   - `resetToDefaults()` for restoring factory settings
   - `generateFilename()` for creating timestamped filenames
   - `getFileExtension()` for format-specific extensions

5. **Performance Optimized**:
   - Single SharedPreferences instance
   - Efficient storage of color values as separate components

### Usage Examples:

1. **Accessing Settings**:
```kotlin
val settingsManager = SettingsManager(context)
val gridOpacity = settingsManager.defaultGridOpacity
```

2. **Modifying Settings**:
```kotlin
settingsManager.defaultGridFormat = SettingsManager.GRID_FORMAT_SQUARE
settingsManager.defaultImageOpacity = 0.7f
```

3. **Generating Filenames**:
```kotlin
val filename = settingsManager.generateFilename() + settingsManager.getFileExtension()
```

4. **Resetting Settings**:
```kotlin
settingsManager.resetToDefaults()
```

### Integration Notes:

1. **Initialization**:
   - Should be initialized once (in Application class or main Activity)
   - Can be passed to other components that need settings

2. **Thread Safety**:
   - All operations are thread-safe
   - Uses `apply()` for asynchronous writes

3. **Default Values**:
   - Carefully chosen defaults that match the app's requirements
   - Easily customizable if needed

4. **Color Handling**:
   - Stores RGBA colors as separate float components (0-1 range)
   - Can be converted to Android color integers when needed

This implementation provides a clean, type-safe way to manage all of the application's settings while maintaining good performance and reliability. It's designed to work seamlessly with the other components in your Graffiti AR Projection Assistant.