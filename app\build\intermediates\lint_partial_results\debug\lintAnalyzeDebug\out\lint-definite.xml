<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.9.2" type="incidents">

    <incident
        id="InflateParams"
        severity="warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/utils/MessageUtils.kt"
            line="27"
            column="66"
            startOffset="906"
            endLine="27"
            endColumn="70"
            endOffset="910"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/deace_logo_sticker.xml"
            line="3"
            column="20"
            startOffset="127"
            endLine="3"
            endColumn="25"
            endOffset="132"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.9.2 is available: 8.10.0. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.10.0 is difficult: 8.9.3)">
        <fix-alternatives>
            <fix-replace
                description="Change to 8.10.0"
                family="Update versions"
                oldString="8.9.2"
                replacement="8.10.0"
                priority="0"/>
            <fix-replace
                description="Change to 8.9.3"
                family="Update versions"
                robot="true"
                independent="true"
                oldString="8.9.2"
                replacement="8.9.3"
                priority="0"/>
        </fix-alternatives>
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="17"
            endLine="2"
            endColumn="14"
            endOffset="24"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.android.material:material than 1.11.0 is available: 1.12.0">
        <fix-replace
            description="Change to 1.12.0"
            family="Update versions"
            oldString="1.11.0"
            replacement="1.12.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="62"
            column="20"
            startOffset="1681"
            endLine="62"
            endColumn="65"
            endOffset="1726"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0">
        <fix-replace
            description="Change to 1.16.0"
            family="Update versions"
            oldString="1.10.1"
            replacement="1.16.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="4"
            column="11"
            startOffset="53"
            endLine="4"
            endColumn="19"
            endOffset="61"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="6"
            column="16"
            startOffset="94"
            endLine="6"
            endColumn="23"
            endOffset="101"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1">
        <fix-replace
            description="Change to 3.6.1"
            family="Update versions"
            oldString="3.5.1"
            replacement="3.6.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="7"
            column="16"
            startOffset="117"
            endLine="7"
            endColumn="23"
            endOffset="124"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.6.1 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="8"
            column="23"
            startOffset="147"
            endLine="8"
            endColumn="30"
            endOffset="154"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.6.1 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="9"
            column="29"
            startOffset="183"
            endLine="9"
            endColumn="36"
            endOffset="190"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1">
        <fix-replace
            description="Change to 1.10.1"
            family="Update versions"
            oldString="1.8.0"
            replacement="1.10.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="10"
            column="19"
            startOffset="209"
            endLine="10"
            endColumn="26"
            endOffset="216"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.05.01">
        <fix-replace
            description="Change to 2025.05.01"
            family="Update versions"
            oldString="2024.09.00"
            replacement="2025.05.01"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="11"
            column="14"
            startOffset="230"
            endLine="11"
            endColumn="26"
            endOffset="242"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.ar:core than 1.48.0 is available: 1.49.0">
        <fix-replace
            description="Change to 1.49.0"
            family="Update versions"
            oldString="1.48.0"
            replacement="1.49.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="15"
            column="8"
            startOffset="357"
            endLine="15"
            endColumn="16"
            endOffset="365"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 29">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/ImagePanel.kt"
            line="327"
            column="13"
            startOffset="14044"
            endLine="327"
            endColumn="64"
            endOffset="14095"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml"
            line="9"
            column="25"
            startOffset="288"
            endLine="9"
            endColumn="929"
            endOffset="1192"/>
    </incident>

    <incident
        id="TypographyEllipsis"
        severity="warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?">
        <fix-replace
            description="Replace with …"
            oldString="..."
            replacement="…"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="7"
            column="36"
            startOffset="379"
            endLine="7"
            endColumn="54"
            endOffset="397"/>
    </incident>

    <incident
        id="TypographyEllipsis"
        severity="warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?">
        <fix-replace
            description="Replace with …"
            oldString="..."
            replacement="…"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="8"
            column="43"
            startOffset="450"
            endLine="8"
            endColumn="68"
            endOffset="475"/>
    </incident>

    <incident
        id="TypographyEllipsis"
        severity="warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?">
        <fix-replace
            description="Replace with …"
            oldString="..."
            replacement="…"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="27"
            column="35"
            startOffset="1402"
            endLine="27"
            endColumn="55"
            endOffset="1422"/>
    </incident>

    <incident
        id="IconXmlAndPng"
        severity="warning"
        message="The following images appear both as density independent `.xml` files and as bitmap files: src\main\res\mipmap-anydpi\ic_launcher.xml, src\main\res\mipmap-hdpi\ic_launcher.webp">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher.xml"/>
    </incident>

    <incident
        id="IconXmlAndPng"
        severity="warning"
        message="The following images appear both as density independent `.xml` files and as bitmap files: src\main\res\mipmap-anydpi\ic_launcher_round.xml, src\main\res\mipmap-hdpi\ic_launcher_round.webp">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher_round.xml"/>
    </incident>

    <incident
        id="IconDuplicates"
        severity="warning"
        message="The following unrelated icon files have identical contents: baseline_grid_on_black_24.png, baseline_grid_on_black_36.png, baseline_grid_on_black_18.png">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/baseline_grid_on_black_18.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/baseline_grid_on_black_36.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_grid_on_black_24.png"/>
    </incident>

    <incident
        id="IconDuplicates"
        severity="warning"
        message="The following unrelated icon files have identical contents: baseline_grid_on_black_36.png, baseline_grid_on_black_18.png">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/baseline_grid_on_black_18.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_grid_on_black_36.png"/>
    </incident>

    <incident
        id="IconDuplicates"
        severity="warning"
        message="The following unrelated icon files have identical contents: baseline_grid_on_black_48.png, baseline_grid_on_black_36.png, baseline_grid_on_black_24.png, baseline_grid_on_black_18.png">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/baseline_grid_on_black_18.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/baseline_grid_on_black_24.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/baseline_grid_on_black_36.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_grid_on_black_48.png"/>
    </incident>

    <incident
        id="IconDuplicates"
        severity="warning"
        message="The following unrelated icon files have identical contents: baseline_grid_on_black_48.png, baseline_grid_on_black_24.png">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/baseline_grid_on_black_24.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/baseline_grid_on_black_48.png"/>
    </incident>

    <incident
        id="IconDuplicates"
        severity="warning"
        message="The following unrelated icon files have identical contents: baseline_grid_on_black_48.png, baseline_grid_on_black_24.png">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/baseline_grid_on_black_24.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/baseline_grid_on_black_48.png"/>
    </incident>

    <incident
        id="IconDuplicates"
        severity="warning"
        message="The following unrelated icon files have identical contents: baseline_grid_on_black_48.png, baseline_grid_on_black_36.png">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/baseline_grid_on_black_36.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/baseline_grid_on_black_48.png"/>
    </incident>

    <incident
        id="IconDuplicates"
        severity="warning"
        message="The following unrelated icon files have identical contents: baseline_image_black_24.png, baseline_image_black_36.png, baseline_image_black_18.png">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/baseline_image_black_18.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/baseline_image_black_36.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_image_black_24.png"/>
    </incident>

    <incident
        id="IconDuplicates"
        severity="warning"
        message="The following unrelated icon files have identical contents: baseline_image_black_36.png, baseline_image_black_18.png">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/baseline_image_black_18.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_image_black_36.png"/>
    </incident>

    <incident
        id="IconDuplicates"
        severity="warning"
        message="The following unrelated icon files have identical contents: baseline_image_black_48.png, baseline_image_black_36.png, baseline_image_black_24.png, baseline_image_black_18.png">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/baseline_image_black_18.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/baseline_image_black_24.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/baseline_image_black_36.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/baseline_image_black_48.png"/>
    </incident>

    <incident
        id="IconDuplicates"
        severity="warning"
        message="The following unrelated icon files have identical contents: baseline_image_black_48.png, baseline_image_black_24.png">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/baseline_image_black_24.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/baseline_image_black_48.png"/>
    </incident>

    <incident
        id="IconDuplicates"
        severity="warning"
        message="The following unrelated icon files have identical contents: baseline_image_black_48.png, baseline_image_black_24.png">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/baseline_image_black_24.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/baseline_image_black_48.png"/>
    </incident>

    <incident
        id="IconDuplicates"
        severity="warning"
        message="The following unrelated icon files have identical contents: baseline_image_black_48.png, baseline_image_black_36.png">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/baseline_image_black_36.png"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/baseline_image_black_48.png"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/config_panel.xml"
                    startOffset="13991"
                    endOffset="14819"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/config_panel.xml"
                    startOffset="14210"
                    endOffset="14473"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/config_panel.xml"
                    startOffset="14489"
                    endOffset="14794"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/config_panel.xml"
            line="355"
            column="14"
            startOffset="14211"
            endLine="355"
            endColumn="20"
            endOffset="14217"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/config_panel.xml"
                    startOffset="13991"
                    endOffset="14819"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/config_panel.xml"
                    startOffset="14210"
                    endOffset="14473"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/config_panel.xml"
                    startOffset="14489"
                    endOffset="14794"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/config_panel.xml"
            line="362"
            column="14"
            startOffset="14490"
            endLine="362"
            endColumn="20"
            endOffset="14496"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX function `createBitmap` instead?">
        <fix-replace
            description="Replace with the createBitmap function"
            family="Replace with the createBitmap function"
            robot="true"
            independent="true"
            oldString="Bitmap.createBitmap(screenWidth, screenHeight, Bitmap.Config.ARGB_8888)"
            replacement="createBitmap(screenWidth, screenHeight)"
            reformat="value"
            imports="androidx.core.graphics.createBitmap"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ar/ARRenderer.kt"
                startOffset="21919"
                endOffset="21990"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ar/ARRenderer.kt"
            line="502"
            column="26"
            startOffset="21919"
            endLine="502"
            endColumn="97"
            endOffset="21990"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX function `createBitmap` instead?">
        <fix-replace
            description="Replace with the createBitmap function"
            family="Replace with the createBitmap function"
            robot="true"
            independent="true"
            oldString="Bitmap.createBitmap(&#xA;                bitmap.width, bitmap.height, Bitmap.Config.ARGB_8888&#xA;            )"
            replacement="createBitmap(bitmap.width, bitmap.height)"
            reformat="value"
            imports="androidx.core.graphics.createBitmap"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ar/ARRenderer.kt"
                startOffset="22807"
                endOffset="22910"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ar/ARRenderer.kt"
            line="525"
            column="33"
            startOffset="22807"
            endLine="527"
            endColumn="14"
            endOffset="22910"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `Bitmap.get` instead?">
        <fix-replace
            description="Replace with the get extension function"
            family="Replace with the get extension function"
            robot="true"
            independent="true"
            oldString="bitmap.getPixel(x, y)"
            replacement="bitmap[x, y]"
            reformat="value"
            imports="androidx.core.graphics.get"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ar/ARRenderer.kt"
                startOffset="23078"
                endOffset="23099"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ar/ARRenderer.kt"
            line="531"
            column="70"
            startOffset="23078"
            endLine="531"
            endColumn="91"
            endOffset="23099"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `Bitmap.set` instead?">
        <fix-replace
            description="Replace with the set extension function"
            family="Replace with the set extension function"
            robot="true"
            independent="true"
            oldString="flippedBitmap.setPixel(x, bitmap.height - y - 1, bitmap.getPixel(x, y))"
            replacement="flippedBitmap[x, bitmap.height - y - 1] = bitmap.getPixel(x, y)"
            reformat="value"
            imports="androidx.core.graphics.set"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ar/ARRenderer.kt"
                startOffset="23029"
                endOffset="23100"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ar/ARRenderer.kt"
            line="531"
            column="21"
            startOffset="23029"
            endLine="531"
            endColumn="92"
            endOffset="23100"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `Bitmap.scale` instead?">
        <fix-replace
            description="Replace with the scale extension function"
            family="Replace with the scale extension function"
            robot="true"
            independent="true"
            oldString="Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, true)"
            replacement="bitmap.scale(targetWidth, targetHeight)"
            reformat="value"
            imports="androidx.core.graphics.scale"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ar/ARSessionManager.kt"
                startOffset="105709"
                endOffset="105775"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ar/ARSessionManager.kt"
            line="2127"
            column="28"
            startOffset="105709"
            endLine="2127"
            endColumn="94"
            endOffset="105775"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension property `View.isVisible` instead?">
        <fix-replace
            description="Replace with the isVisible extension property"
            family="Replace with the isVisible extension property"
            robot="true"
            independent="true"
            oldString="scrollView.visibility == View.VISIBLE"
            replacement="scrollView.isVisible"
            reformat="value"
            imports="androidx.core.view.isVisible"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/ConfigPanel.kt"
                startOffset="10203"
                endOffset="10240"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/ConfigPanel.kt"
            line="260"
            column="16"
            startOffset="10203"
            endLine="260"
            endColumn="53"
            endOffset="10240"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension property `View.isVisible` instead?">
        <fix-replace
            description="Replace with the isVisible extension property"
            family="Replace with the isVisible extension property"
            robot="true"
            independent="true"
            oldString="panelLayout.visibility == View.VISIBLE"
            replacement="panelLayout.isVisible"
            reformat="value"
            imports="androidx.core.view.isVisible"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/DeacePanel.kt"
                startOffset="2987"
                endOffset="3025"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/DeacePanel.kt"
            line="105"
            column="16"
            startOffset="2987"
            endLine="105"
            endColumn="54"
            endOffset="3025"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension property `View.isVisible` instead?">
        <fix-replace
            description="Replace with the isVisible extension property"
            family="Replace with the isVisible extension property"
            robot="true"
            independent="true"
            oldString="panelLayout.visibility == View.VISIBLE"
            replacement="panelLayout.isVisible"
            reformat="value"
            imports="androidx.core.view.isVisible"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/ExportPanel.kt"
                startOffset="7825"
                endOffset="7863"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/ExportPanel.kt"
            line="229"
            column="16"
            startOffset="7825"
            endLine="229"
            endColumn="54"
            endOffset="7863"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension property `View.isVisible` instead?">
        <fix-replace
            description="Replace with the isVisible extension property"
            family="Replace with the isVisible extension property"
            robot="true"
            independent="true"
            oldString="panelLayout.visibility == View.VISIBLE"
            replacement="panelLayout.isVisible"
            reformat="value"
            imports="androidx.core.view.isVisible"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/GridPanel.kt"
                startOffset="13168"
                endOffset="13206"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/GridPanel.kt"
            line="339"
            column="16"
            startOffset="13168"
            endLine="339"
            endColumn="54"
            endOffset="13206"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `Bitmap.scale` instead?">
        <fix-replace
            description="Replace with the scale extension function"
            family="Replace with the scale extension function"
            robot="true"
            independent="true"
            oldString="Bitmap.createScaledBitmap(loadedBitmap, thumbnailSize, thumbnailSize, true)"
            replacement="loadedBitmap.scale(thumbnailSize, thumbnailSize)"
            reformat="value"
            imports="androidx.core.graphics.scale"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/ImagePanel.kt"
                startOffset="26685"
                endOffset="26760"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/ImagePanel.kt"
            line="571"
            column="44"
            startOffset="26685"
            endLine="571"
            endColumn="119"
            endOffset="26760"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension property `View.isVisible` instead?">
        <fix-replace
            description="Replace with the isVisible extension property"
            family="Replace with the isVisible extension property"
            robot="true"
            independent="true"
            oldString="panelLayout.visibility == View.VISIBLE"
            replacement="panelLayout.isVisible"
            reformat="value"
            imports="androidx.core.view.isVisible"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/ImagePanel.kt"
                startOffset="34651"
                endOffset="34689"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/ImagePanel.kt"
            line="750"
            column="16"
            startOffset="34651"
            endLine="750"
            endColumn="54"
            endOffset="34689"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension property `View.isVisible` instead?">
        <fix-replace
            description="Replace with the isVisible extension property"
            family="Replace with the isVisible extension property"
            robot="true"
            independent="true"
            oldString="loadButton.visibility == View.VISIBLE"
            replacement="loadButton.isVisible"
            reformat="value"
            imports="androidx.core.view.isVisible"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/MainActivity.kt"
                startOffset="87568"
                endOffset="87605"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/MainActivity.kt"
            line="1762"
            column="95"
            startOffset="87568"
            endLine="1762"
            endColumn="132"
            endOffset="87605"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension property `View.isVisible` instead?">
        <fix-replace
            description="Replace with the isVisible extension property"
            family="Replace with the isVisible extension property"
            robot="true"
            independent="true"
            oldString="binding.quickLockImageButton.visibility == View.VISIBLE"
            replacement="binding.quickLockImageButton.isVisible"
            reformat="value"
            imports="androidx.core.view.isVisible"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/MainActivity.kt"
                startOffset="92062"
                endOffset="92117"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/MainActivity.kt"
            line="1884"
            column="39"
            startOffset="92062"
            endLine="1884"
            endColumn="94"
            endOffset="92117"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-google-android-material-material2"
            robot="true">
            <fix-replace
                description="Replace with googleMaterialVersion = &quot;1.11.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="googleMaterialVersion = &quot;1.11.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-google-android-material-material2 = { module = &quot;com.google.android.material:material&quot;, version.ref = &quot;googleMaterialVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-google-android-material-material2 = { module = &quot;com.google.android.material:material&quot;, version.ref = &quot;googleMaterialVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="471"
                    endOffset="471"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.google.android.material.material2"
                robot="true"
                replacement="libs.com.google.android.material.material2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1681"
                    endOffset="1726"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="62"
            column="20"
            startOffset="1681"
            endLine="62"
            endColumn="65"
            endOffset="1726"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="Custom view ``GLSurfaceView`` has `setOnTouchListener` called on it but does not override `performClick`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/MainActivity.kt"
            line="1174"
            column="9"
            startOffset="58164"
            endLine="1218"
            endColumn="10"
            endOffset="60108"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="`onTouch` lambda should call `View#performClick` when a click is detected">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/MainActivity.kt"
            line="1174"
            column="50"
            startOffset="58205"
            endLine="1218"
            endColumn="10"
            endOffset="60108"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/GridPanel.kt"
            line="83"
            column="37"
            startOffset="3356"
            endLine="83"
            endColumn="56"
            endOffset="3375"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/GridPanel.kt"
            line="157"
            column="41"
            startOffset="6467"
            endLine="157"
            endColumn="53"
            endOffset="6479"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/GridPanel.kt"
            line="269"
            column="37"
            startOffset="10865"
            endLine="269"
            endColumn="56"
            endOffset="10884"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/GridPanel.kt"
            line="275"
            column="37"
            startOffset="11279"
            endLine="275"
            endColumn="66"
            endOffset="11308"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/ImagePanel.kt"
            line="132"
            column="45"
            startOffset="6025"
            endLine="132"
            endColumn="62"
            endOffset="6042"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/deace/deacear/ui/ImagePanel.kt"
            line="768"
            column="41"
            startOffset="35374"
            endLine="768"
            endColumn="58"
            endOffset="35391"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;50%&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="202"
            column="13"
            startOffset="8598"
            endLine="202"
            endColumn="31"
            endOffset="8616"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;No image selected&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="250"
            column="13"
            startOffset="10695"
            endLine="250"
            endColumn="45"
            endOffset="10727"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TEST: Place&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="279"
            column="13"
            startOffset="11970"
            endLine="279"
            endColumn="39"
            endOffset="11996"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Lock Image&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="662"
            column="9"
            startOffset="28198"
            endLine="662"
            endColumn="34"
            endOffset="28223"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Graffiti_AR_{NNN}&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/config_panel.xml"
            line="303"
            column="17"
            startOffset="12266"
            endLine="303"
            endColumn="49"
            endOffset="12298"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;/Pictures/Graffiti_AR/&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/config_panel.xml"
            line="325"
            column="17"
            startOffset="13098"
            endLine="325"
            endColumn="54"
            endOffset="13135"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Tracking Help&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_tracking_help.xml"
            line="12"
            column="9"
            startOffset="428"
            endLine="12"
            endColumn="37"
            endOffset="456"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Red: Tracking lost. Move your device slowly to scan the environment.&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_tracking_help.xml"
            line="34"
            column="13"
            startOffset="1163"
            endLine="34"
            endColumn="96"
            endOffset="1246"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Yellow: Limited tracking. Continue moving to improve tracking quality.&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_tracking_help.xml"
            line="55"
            column="13"
            startOffset="1916"
            endLine="55"
            endColumn="98"
            endOffset="2001"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Green: Good tracking. Tap on a wall to place content.&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_tracking_help.xml"
            line="76"
            column="13"
            startOffset="2671"
            endLine="76"
            endColumn="81"
            endOffset="2739"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Tips for Detecting Vertical Walls:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_tracking_help.xml"
            line="85"
            column="9"
            startOffset="3000"
            endLine="85"
            endColumn="58"
            endOffset="3049"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;• Move your device VERY SLOWLY in a figure-8 pattern\n• Point camera directly at walls (not at an angle)\n• Scan textured walls with patterns or objects\n• Avoid plain white or reflective surfaces\n• Keep 0.5-1.5m distance from walls\n• Ensure bright, even lighting\n• Make sure camera lens is clean\n• Try different walls if one isn&apos;t detected&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_tracking_help.xml"
            line="94"
            column="9"
            startOffset="3313"
            endLine="94"
            endColumn="368"
            endOffset="3672"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Got it&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_tracking_help.xml"
            line="103"
            column="9"
            startOffset="3952"
            endLine="103"
            endColumn="30"
            endOffset="3973"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TEST: Place&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/image_panel.xml"
            line="48"
            column="13"
            startOffset="1873"
            endLine="48"
            endColumn="39"
            endOffset="1899"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Image Rotation&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/image_panel.xml"
            line="119"
            column="9"
            startOffset="4660"
            endLine="119"
            endColumn="38"
            endOffset="4689"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;↺ 90°&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/image_panel.xml"
            line="136"
            column="13"
            startOffset="5317"
            endLine="136"
            endColumn="33"
            endOffset="5337"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;↻ 90°&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/image_panel.xml"
            line="149"
            column="13"
            startOffset="5867"
            endLine="149"
            endColumn="33"
            endOffset="5887"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;180°&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/image_panel.xml"
            line="161"
            column="13"
            startOffset="6368"
            endLine="161"
            endColumn="32"
            endOffset="6387"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;↺ 15°&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/image_panel.xml"
            line="182"
            column="13"
            startOffset="7157"
            endLine="182"
            endColumn="33"
            endOffset="7177"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;↻ 15°&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/image_panel.xml"
            line="195"
            column="13"
            startOffset="7707"
            endLine="195"
            endColumn="33"
            endOffset="7727"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Flip H&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/image_panel.xml"
            line="207"
            column="13"
            startOffset="8213"
            endLine="207"
            endColumn="34"
            endOffset="8234"/>
    </incident>

</incidents>
